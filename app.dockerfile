# 第一阶段：构建应用
FROM node:20.18.0-alpine AS build

# 定义构建参数
ARG NEXT_PUBLIC_WS_URL=ws://127.0.0.1:3001
ARG NEXT_PUBLIC_API_URL=http://127.0.0.1:3001/api
ENV NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL}
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}

WORKDIR /app

# 首先复制依赖文件并安装
COPY ui/package.json ui/yarn.lock ./
RUN yarn config set registry https://registry.npmmirror.com \
    && yarn install --frozen-lockfile

# 复制源代码和配置文件
COPY ui .

# 构建应用
RUN yarn build

# 第二阶段：生产环境
FROM node:20.18.0-alpine

# 继承构建参数
ARG NEXT_PUBLIC_WS_URL
ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL}
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}

WORKDIR /app

# 从构建阶段复制构建产物和必要文件
COPY --from=build /app/.next/standalone ./
COPY --from=build /app/.next/static ./.next/static
COPY --from=build /app/public ./public

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000

# 启动命令,生产环境中使用了 standalone 模式，不能使用 next start 命令
CMD ["node", "server.js"]