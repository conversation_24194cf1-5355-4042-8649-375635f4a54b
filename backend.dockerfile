# backend.dockerfile
FROM custom-node-brotli:latest

WORKDIR /app

COPY src /app/src
COPY tsconfig.json /app/
COPY drizzle.config.ts /app/
COPY package.json /app/
COPY yarn.lock /app/
RUN mkdir /app/data
RUN mkdir /app/uploads

# 换成国内源-阿里云
RUN yarn config set registry https://registry.npmmirror.com
# 设置sharp镜像源
RUN yarn config set sharp_binary_host "https://npmmirror.com/mirrors/sharp"
RUN yarn config set sharp_libvips_binary_host "https://npmmirror.com/mirrors/sharp-libvips"
# 添加重试逻辑
RUN for i in 1 2 3; do yarn install --frozen-lockfile && break || sleep 5; done
RUN yarn build

CMD ["yarn", "start"]
