LINK_CONTENT_API = { }

[API_KEYS]
OPENAI = "sk-2zWVjdas05pjppEf1d0601EdF7004d7dB45a3f2d64Da3aC1"
GROQ = ""
ANTHROPIC = ""
GEMINI = ""

[API_ENDPOINTS]
OLLAMA = ""
SEARXNG = "https://search.cyjiaomu.com/"

[CUSTOM_MODEL]
CUSTOM_BASE_URL = "https://oneapi.cyjiaomu.com/v1"
CUSTOM_CHAT_MODEL = "deepseek-r1"
CUSTOM_EMBEDDING_MODEL = "text-embedding-3-large"
CUSTOM_SUMMARIZER_MODEL = "deepseek-v3"
CUSTOM_RESUME_MODEL = "deepseek-r1"
CUSTOM_SETTING_MODEL = false

[GENERAL]
PORT = 3_001
SIMILARITY_MEASURE = "cosine"
KEEP_ALIVE = "30m"

[DATABASE]
HOST = "**********"
PORT = 5432
USER = "postgres"
PASSWORD = "difyai123456"
NAME = "jmaisearch"

[OCR]
ALIBABA_CLOUD_ACCESS_KEY_ID = "LTAI5t6PQF8qQfDqP1yFtJHx"
ALIBABA_CLOUD_ACCESS_KEY_SECRET = "******************************"

[FILE_OCR]
ALIBABA_CLOUD_ACCESS_KEY_ID = "LTAI5t5v3u2ASW2eXQmcDFZq"
ALIBABA_CLOUD_ACCESS_KEY_SECRET = "******************************"

[JM_INFO]
JM_BASE_URL = "https://user-api.cyjiaomu.com"
PAY_CHANNELS = ["mp-kl", "mp", "guanyong", "guanwang", "xuexi", "gy-android", "gy-ios", "web"]
PAY_POINTS = { webSearch = 0, managementMaster = 0, resumeAnalyzer = 0 }
PAY_ENABLE = true
AVAILABLE_TASKS = ["webSearch", "managementMaster", "resumeAnalyzer", "careerCoach", "careerCoachAdvise"]
