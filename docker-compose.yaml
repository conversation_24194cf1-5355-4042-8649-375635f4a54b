version: "3.9"

services:
  jmso-backend:
    build:
      context: .
      dockerfile: backend.dockerfile
    #image: itzcrazykns1337/jmso-backend:latest
    container_name: jmso-backend
    ports:
      - 13001:3001
    volumes:
      - ./volumes/backend_dbstore:/app/data
      - ./volumes/backend_uploads:/app/uploads
      - ./config.toml:/app/config.toml
    networks:
      - jmso-network
    restart: unless-stopped

  jmso-frontend:
    build:
      context: .
      dockerfile: app.dockerfile
      args:
        - NEXT_PUBLIC_WS_URL=wss://jmso-api.cyjiaomu.com
        - NEXT_PUBLIC_API_URL=https://jmso-api.cyjiaomu.com/api
    #image: itzcrazykns1337/jmso-frontend:latest
    container_name: jmso-frontend
    depends_on:
      - jmso-backend
    ports:
      - 13000:3000
    networks:
      - jmso-network
    restart: unless-stopped

networks:
  jmso-network:
