{"name": "perplexica-backend", "version": "1.10.0-rc2", "license": "MIT", "author": "ItzCrazyKns", "scripts": {"start": "npm run db:push && node dist/app.js", "build": "tsc", "dev": "NODE_ENV=development nodemon --ignore uploads/ src/app.ts", "audit": "NODE_ENV=audit nodemon --ignore uploads/ src/app.ts", "db:push": "drizzle-kit push", "format": "prettier . --check", "format:write": "prettier . --write", "studio": "drizzle-kit studio", "prepare": "husky install"}, "devDependencies": {"@cspotcode/source-map-support": "^0.8.1", "@tsconfig/node16": "^16.1.3", "@types/better-sqlite3": "^7.6.10", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.27", "@types/html-to-text": "^9.0.4", "@types/multer": "^1.4.12", "@types/node": "^22.13.1", "@types/pdf-parse": "^1.1.4", "@types/readable-stream": "^4.0.11", "@types/ws": "^8.5.12", "drizzle-kit": "^0.30.5", "husky": "^8.0.0", "nodemon": "^3.1.0", "prettier": "^3.2.5", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "dependencies": {"@alicloud/docmind-api20220711": "^1.4.3", "@alicloud/ocr-api20210707": "^3.1.2", "@alicloud/openapi-client": "^0.4.13", "@alicloud/tea-console": "^1.0.0", "@alicloud/tea-typescript": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "@iarna/toml": "^2.2.5", "@langchain/anthropic": "^0.2.3", "@langchain/community": "^0.2.16", "@langchain/core": "^0.3.0", "@langchain/deepseek": "^0.0.1", "@langchain/google-genai": "^0.0.23", "@langchain/ollama": "^0.1.5", "@langchain/openai": "^0.4.4", "@types/pg": "^8.11.11", "@xenova/transformers": "^2.17.1", "ali-oss": "^6.22.0", "axios": "^1.6.8", "better-sqlite3": "^11.0.0", "compute-cosine-similarity": "^1.1.0", "compute-dot": "^1.1.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "drizzle-orm": "^0.40.0", "drizzle-orm-pg": "^0.16.3", "epub2": "^3.0.1", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.3", "framer-motion": "^12.19.1", "html-docx-js": "^0.3.1", "html-to-text": "^9.0.5", "jsdom": "^26.0.0", "mammoth": "^1.7.0", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "pg": "^8.14.0", "sharp": "^0.33.5", "tencentcloud-sdk-nodejs-cvm": "^4.1.64", "tencentcloud-sdk-nodejs-tts": "^4.1.45", "tesseract.js": "^6.0.1", "textract": "^2.5.0", "winston": "^3.13.0", "ws": "^8.17.1", "zod": "^3.22.4"}}