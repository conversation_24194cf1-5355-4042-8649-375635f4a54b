import { careerCoachPrompt, generateReferenceAnswerPrompt } from '../prompts/careerCoach';
import { getSummarizerModel } from '../lib/providers';
import logger from '../utils/logger';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { EventEmitter } from 'events';

// 定义评估结果接口
export interface CareerCoachEvaluationResult {
  score: number;
  passed: boolean;
  analysis: {
    strengths: string[];
    weaknesses: string[];
  };
  reference_answer: string;
  key_points: string[];
}

// 定义评估请求接口
export interface CareerCoachEvaluationRequest {
  topic: string;
  knowledge_points: string;
  question: string;
  options: string;
  correct_answer: string;
  user_answer: string;
}

// 生成参考答案请求接口
export interface GenerateReferenceAnswerRequest {
  topic: string;
  knowledge_points: string;
  question: string;
  options: string;
  correct_answer: string;
}

export class CareerCoachAgent {
  private model: BaseChatModel;

  constructor(model?: BaseChatModel) {
    // 如果没有提供模型，则使用默认的总结模型
    this.model = model || null;
  }

  /**
   * 初始化模型
   */
  private async initModel(): Promise<void> {
    if (!this.model) {
      try {
        this.model = await getSummarizerModel();
        if (!this.model) {
          throw new Error('无法加载模型');
        }
      } catch (error) {
        logger.error('初始化职场技能陪练模型失败:', error);
        throw new Error(`初始化职场技能陪练模型失败: ${error.message}`);
      }
    }
  }

  /**
   * 查找有效的JSON内容
   * @param jsonContent JSON字符串内容
   * @returns 有效的JSON内容和结束位置
   */
  private findValidJsonContent(jsonContent: string): { validContent: string, endPos: number } | null {
    let endPos = -1;
    let bracketCount = 0;
    let inString = false;
    let escapeNext = false;
    
    for (let i = 0; i < jsonContent.length; i++) {
      const char = jsonContent[i];
      
      if (escapeNext) {
        escapeNext = false;
        continue;
      }
      
      if (char === '\\') {
        escapeNext = true;
        continue;
      }
      
      if (char === '"' && !escapeNext) {
        inString = !inString;
        continue;
      }
      
      if (!inString) {
        if (char === '{') {
          bracketCount++;
        } else if (char === '}') {
          bracketCount--;
          if (bracketCount === 0) {
            endPos = i + 1;
            break;
          }
        }
      }
    }
    
    // 如果找到了完整的JSON
    if (endPos !== -1) {
      const validJsonContent = jsonContent.substring(0, endPos);
      return { validContent: validJsonContent, endPos };
    }
    
    return null;
  }

  /**
   * 尝试解析JSON内容
   * @param accumulatedContent 累积的内容
   * @param lastEmittedLength 上次发送的长度
   * @param emitter 事件发射器
   * @returns 解析结果
   */
  private tryParseJsonContent(
    accumulatedContent: string, 
    lastEmittedLength: number, 
    emitter: EventEmitter
  ): { partialJson: string, lastEmittedLength: number } {
    try {
      // 查找JSON开始位置
      const startPos = accumulatedContent.indexOf('{');
      if (startPos === -1) {
        return { partialJson: '', lastEmittedLength };
      }
      
      const jsonContent = accumulatedContent.substring(startPos);
      
      // 尝试解析完整的JSON
      try {
        const result = this.findValidJsonContent(jsonContent);
        
        if (result) {
          // 找到了完整的JSON
          const { validContent } = result;
          /*
          const parsedResult = JSON.parse(validContent);
          
          // 发送完整的JSON对象
          emitter.emit('data', JSON.stringify({
            type: 'full',
            data: parsedResult
          }));
          */
          
          return { partialJson: validContent, lastEmittedLength: validContent.length };
        } else {
          // 不是完整的JSON，发送增量更新
          if (jsonContent.length > lastEmittedLength) {
            const newContent = jsonContent.substring(lastEmittedLength);
            emitter.emit('data', JSON.stringify({
              type: 'partial',
              data: newContent
            }));
            
            return { partialJson: jsonContent, lastEmittedLength: jsonContent.length };
          }
        }
      } catch (e) {
        // 不是完整的JSON，发送增量更新
        if (jsonContent.length > lastEmittedLength) {
          const newContent = jsonContent.substring(lastEmittedLength);
          emitter.emit('data', JSON.stringify({
            type: 'partial',
            data: newContent
          }));
          
          return { partialJson: jsonContent, lastEmittedLength: jsonContent.length };
        }
      }
    } catch (e) {
      // 忽略解析错误
    }
    
    return { partialJson: '', lastEmittedLength };
  }

  /**
   * 处理最终的JSON结果
   * @param accumulatedContent 累积的内容
   * @param emitter 事件发射器
   */
  private handleFinalResult(accumulatedContent: string, emitter: EventEmitter): void {
    try {
      const startPos = accumulatedContent.indexOf('{');
      if (startPos === -1) {
        throw new Error('无法找到JSON开始位置');
      }
      
      const jsonContent = accumulatedContent.substring(startPos);
      const result = this.findValidJsonContent(jsonContent);
      
      if (result) {
        const { validContent } = result;
        const parsedResult = JSON.parse(validContent);
        
        // 发送完整的最终结果
        emitter.emit('data', JSON.stringify({
          type: 'complete',
          data: parsedResult
        }));
        
        logger.info(`评估完成，得分: ${parsedResult.score}, 通过: ${parsedResult.passed}`);
      } else {
        throw new Error('无法找到有效的JSON结束位置');
      }
    } catch (e) {
      logger.error('解析最终评估结果失败:', e);
      emitter.emit('error', JSON.stringify({
        error: '解析评估结果失败'
      }));
    }
  }

  /**
   * 评估用户的回答（非流式）
   * @param request 评估请求
   * @returns 评估结果
   */
  public async evaluateAnswer(request: CareerCoachEvaluationRequest): Promise<CareerCoachEvaluationResult> {
    try {
      // 确保模型已初始化
      await this.initModel();
      
      logger.info('开始评估用户回答');
      
      // 构建提示词
      const prompt = careerCoachPrompt
        .replace('{topic}', request.topic)
        .replace('{knowledge_points}', request.knowledge_points)
        .replace('{question}', request.question)
        .replace('{options}', request.options)
        .replace('{correct_answer}', request.correct_answer)
        .replace('{user_answer}', request.user_answer);
      
      // 调用模型进行评估
      const result = await this.model.invoke(prompt);
      
      // 解析JSON结果
      try {
        const content = result.content as string;
        const evaluationResult = JSON.parse(content) as CareerCoachEvaluationResult;
        
        logger.info(`评估完成，得分: ${evaluationResult.score}, 通过: ${evaluationResult.passed}`);
        
        return evaluationResult;
      } catch (parseError) {
        logger.error('解析评估结果失败:', parseError);
        throw new Error(`解析评估结果失败: ${parseError.message}`);
      }
    } catch (error) {
      logger.error('评估用户回答失败:', error);
      throw new Error(`评估用户回答失败: ${error.message}`);
    }
  }

  /**
   * 流式评估用户的回答
   * @param request 评估请求
   * @returns 事件发射器，用于流式传输结果
   */
  public async streamEvaluateAnswer(request: CareerCoachEvaluationRequest): Promise<EventEmitter> {
    const emitter = new EventEmitter();
    
    // 异步执行评估
    (async () => {
      try {
        // 确保模型已初始化
        await this.initModel();
        
        logger.info('开始流式评估用户回答');
        
        // 构建提示词
        const prompt = careerCoachPrompt
          .replace('{topic}', request.topic)
          .replace('{knowledge_points}', request.knowledge_points)
          .replace('{question}', request.question)
          .replace('{options}', request.options)
          .replace('{correct_answer}', request.correct_answer)
          .replace('{user_answer}', request.user_answer);
        
        // 获取流式响应
        const stream = await this.model.stream(prompt);
        
        let accumulatedContent = '';
        let partialJson = '';
        let lastEmittedLength = 0;
        
        // 处理流式响应
        for await (const chunk of stream) {
          const content = chunk.content as string;
          if (!content) continue;
          
          accumulatedContent += content;
          
          // 尝试提取和解析JSON
          const result = this.tryParseJsonContent(accumulatedContent, lastEmittedLength, emitter);
          partialJson = result.partialJson;
          lastEmittedLength = result.lastEmittedLength;
        }
        
        // 评估完成，处理最终结果
        this.handleFinalResult(accumulatedContent, emitter);
        
        // 发送结束信号
        emitter.emit('end');
        
      } catch (error) {
        logger.error('流式评估用户回答失败:', error.message, error.stack);
        emitter.emit('error', JSON.stringify({
          error: `评估失败: ${error.message}`
        }));
        emitter.emit('end');
      }
    })();
    
    return emitter;
  }

  /**
   * 生成参考答案（非流式）
   * @param request 生成参考答案的请求参数
   * @returns 参考答案文本
   */
  public async generateReferenceAnswer(request: GenerateReferenceAnswerRequest): Promise<string> {
    try {
      // 确保模型已初始化
      await this.initModel();
      
      logger.info('开始生成参考答案');
      
      // 构建提示词
      const prompt = generateReferenceAnswerPrompt
        .replace('{topic}', request.topic)
        .replace('{knowledge_points}', request.knowledge_points)
        .replace('{question}', request.question)
        .replace('{options}', request.options)
        .replace('{correct_answer}', request.correct_answer);
      
      // 调用模型
      const result = await this.model.invoke(prompt);
      const referenceText = result.content as string;
      
      logger.info('参考答案生成完成');
      return referenceText;

    } catch (error) {
      logger.error('生成参考答案失败:', error);
      throw new Error(`生成参考答案失败: ${error.message}`);
    }
  }

  /**
   * 流式生成参考答案
   * @param request 生成参考答案的请求参数
   * @returns 事件发射器，用于流式传输文本块
   */
  public async streamGenerateReferenceAnswer(request: GenerateReferenceAnswerRequest): Promise<EventEmitter> {
    const emitter = new EventEmitter();
    
    // 异步执行
    (async () => {
      try {
        // 确保模型已初始化
        await this.initModel();
        
        logger.info('开始流式生成参考答案');
        
        // 构建提示词
        const prompt = generateReferenceAnswerPrompt
          .replace('{topic}', request.topic)
          .replace('{knowledge_points}', request.knowledge_points)
          .replace('{question}', request.question)
          .replace('{options}', request.options)
          .replace('{correct_answer}', request.correct_answer);

        // 获取流式响应
        const stream = await this.model.stream(prompt);
        
        let accumulatedContent = '';
        
        // 处理流式响应
        for await (const chunk of stream) {
          const content = chunk.content as string;
          if (content) { // 确保内容存在
            accumulatedContent += content;
            emitter.emit('data', JSON.stringify({
              type: 'partial',
              data: content
            }));
          }
        }
        
        // 发送完整的最终结果
        emitter.emit('data', JSON.stringify({
          type: 'complete',
          data: accumulatedContent
        }));
        
        logger.info('参考答案流式生成完成');
        emitter.emit('end');
        
      } catch (error) {
        logger.error('流式生成参考答案失败:', error.message, error.stack);
        emitter.emit('error', JSON.stringify({
          error: `流式生成参考答案失败: ${error.message}`
        }));
        emitter.emit('end');
      }
    })();
    
    return emitter;
  }
}

export default CareerCoachAgent;
