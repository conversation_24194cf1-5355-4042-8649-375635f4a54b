import {
  generalAnalysisPrompt,
  jobMatchAnalysisPrompt,
  resumeReportPrompt,
  extractCoreImprovementsPrompt,
  resumeOptimizationPrompt,
} from '../prompts/resumeAnalyzer';
import { getResumeModel, getSummarizerModel } from '../lib/providers';
import logger from '../utils/logger';
import { taskQueueManager } from './taskQueue';
import { MessageUtils } from '../utils/messageUtils';
import { BillingUtils } from '../utils/billingUtils';
import { TaskType } from '../config'
import { processOSSFile, isValidResumeText } from '../utils/fileProcessor';

// 核心改进点接口
export interface CoreImprovement {
  title: string;
  description: string;
}

export interface ResumeAnalysisResult {
  analysis: string; // 全部分析结果
  summaryAnalysis?: string; // 概要评估
  generalAnalysis?: string;  // 通用维度分析
  jobMatchAnalysis?: string; // 岗位匹配度分析
  status: 'success' | 'error';
  model?: string;
  error?: string;
}

/**
 * 简历分析类
 * 负责处理简历内容并生成分析结果
 */
export class ResumeAnalyzer {
  public async analyzeResume(
    resumeText: string,
    jobDescription?: string
  ): Promise<ResumeAnalysisResult> {
    const startTime = Date.now();
    try {
      logger.info('开始分析简历');
      
      if (!resumeText || resumeText.trim() === '') {
        return {
          analysis: '',
          status: 'error',
          error: '简历内容为空'
        };
      }

      const resumeModel = await getResumeModel();
      if (!resumeModel) {
        return {
          analysis: '',
          status: 'error',
          error: '无法加载简历分析模型'
        };
      }
      logger.info(`模型加载耗时: ${Date.now() - startTime}ms`);

      // 1. 执行通用维度分析和岗位匹配度分析
      const analysisStartTime = Date.now();
      logger.info('开始并行执行通用维度分析和岗位匹配度分析');
      const [generalAnalysis, jobMatchAnalysis] = await Promise.all([
        this.performGeneralAnalysis(resumeText, resumeModel),
        jobDescription?.trim() 
          ? this.performJobMatchAnalysis(resumeText, jobDescription, resumeModel)
          : Promise.resolve(null)
      ]);
      logger.info(`并行分析耗时: ${Date.now() - analysisStartTime}ms`);

      // 检查通用维度分析结果
      if (generalAnalysis.status === 'error') {
        return generalAnalysis;
      }
      
      // 检查岗位匹配度分析结果（如果有）
      if (jobMatchAnalysis?.status === 'error') {
        return jobMatchAnalysis;
      }

      logger.info('所有分析任务完成');

      // 2. 生成整体评估
      const summaryStartTime = Date.now();
      const summaryAnalysis = await this.generateSummaryAnalysis(
        generalAnalysis.analysis,
        jobMatchAnalysis?.analysis,
        resumeModel
      );
      logger.info(`整体评估耗时: ${Date.now() - summaryStartTime}ms`);

      if (summaryAnalysis.status === 'error') {
        return summaryAnalysis;
      }

      // 3. 合并所有分析结果
      const combineStartTime = Date.now();
      const combinedAnalysis = await this.combineAnalysisResults(
        summaryAnalysis.analysis,
        generalAnalysis.analysis,
        jobMatchAnalysis?.analysis
      );
      logger.info(`结果合并耗时: ${Date.now() - combineStartTime}ms`);

      logger.info(`简历分析总耗时: ${Date.now() - startTime}ms`);
      return {
        analysis: combinedAnalysis,
        summaryAnalysis: summaryAnalysis.analysis,
        generalAnalysis: generalAnalysis.analysis,
        jobMatchAnalysis: jobMatchAnalysis?.analysis,
        status: 'success',
        model: resumeModel.modelName,
      };
    } catch (error) {
      logger.error(`简历分析异常: ${error.message}`);
      return {
        analysis: '',
        status: 'error',
        error: `分析过程发生异常: ${error.message}`
      };
    }
  }

  private async performGeneralAnalysis(
    resumeText: string,
    model: any
  ): Promise<ResumeAnalysisResult> {
    try {
      logger.info('开始执行通用维度分析');

      const prompt = generalAnalysisPrompt
        .replace('{resume}', resumeText)
        .replace('{date}', new Date().toLocaleDateString('zh-CN'));

      const result = await model.invoke(prompt);
      logger.info('通用维度分析完成');

      return {
        analysis: result.content,
        status: 'success'
      };
    } catch (error) {
      return {
        analysis: '',
        status: 'error',
        error: `通用分析失败: ${error.message}`
      };
    }
  }

  private async performJobMatchAnalysis(
    resumeText: string,
    jobDescription: string,
    model: any
  ): Promise<ResumeAnalysisResult> {
    try {
      logger.info('开始执行岗位匹配度分析');

      const prompt = jobMatchAnalysisPrompt
        .replace('{resume}', resumeText)
        .replace('{job}', jobDescription)
        .replace('{date}', new Date().toLocaleDateString('zh-CN'));

      const result = await model.invoke(prompt);
      logger.info('岗位匹配度分析完成');

      return {
        analysis: result.content,
        status: 'success'
      };
    } catch (error) {
      return {
        analysis: '',
        status: 'error',
        error: `岗位匹配分析失败: ${error.message}`
      };
    }
  }

  private async generateSummaryAnalysis(
    generalAnalysis: string,
    jobMatchAnalysis: string | undefined,
    model: any
  ): Promise<ResumeAnalysisResult> {
    try {
      logger.info('开始生成整体评估');
      const prompt = resumeReportPrompt
        .replace('{general_analysis}', generalAnalysis)
        .replace('{job_match_analysis}', jobMatchAnalysis || '')
        .replace('{date}', new Date().toLocaleDateString('zh-CN'));

      const result = await model.invoke(prompt);
      logger.info('整体评估生成完成');

      return {
        analysis: result.content,
        status: 'success'
      };
    } catch (error) {
      return {
        analysis: '',
        status: 'error',
        error: `整体评估生成失败: ${error.message}`
      };
    }
  }

  private async combineAnalysisResults(
    summaryAnalysis: string,
    generalAnalysis: string,
    jobMatchAnalysis?: string
  ): Promise<string> {
    try {
      logger.info('开始合并分析结果');
      const combinedResult = `## 简历分析报告

**报告生成日期:** ${new Date().toLocaleDateString('zh-CN')}

您好！

我们已对您的简历进行了全面的诊断评估。这份报告旨在帮助您了解简历的当前状态，并提供具体的改进建议，以帮助提升您在求职时的专业性和竞争力。

---

${summaryAnalysis?.replace(/```(markdown|json)?/g, '')}

希望这份详细的报告能帮助您更好地优化简历！祝您求职顺利！`;
      
      return combinedResult;
    } catch (error) {
      logger.error(`报告合并失败: ${error.message}`);
      // 发生错误时退回到简单合并
      return '【整体评估与核心建议】\n' + summaryAnalysis + '\n\n【通用维度分析明细及修改建议】\n' + generalAnalysis + '\n\n【岗位匹配度明细及修改建议】\n' + (jobMatchAnalysis || '无');
    }
  }

  /**
   * 创建简历分析任务
   * @param params 任务参数
   * @returns 任务ID
   */
  public async createAnalysisTask(params: {
    resumeFileId?: string;
    jobFileId?: string;
    resumeText?: string;
    jobText?: string;
    resumeFileName?: string;
    jobFileName?: string;
    channelId?: string;
    deviceId?: string;
    userId?: string;
    parentTaskId?: bigint;
  }): Promise<{taskId: bigint}> {
    const { resumeFileId, jobFileId, resumeText, jobText, resumeFileName, jobFileName, channelId, deviceId, userId, parentTaskId } = params;
    
    logger.info(`创建简历分析任务，参数: ${JSON.stringify({
      resumeFileId: !!resumeFileId,
      jobFileId: !!jobFileId,
      resumeText: !!resumeText,
      jobText: !!jobText,
      deviceId: deviceId?.substring(0, 8) + '...'
    })}`);
    
    // 创建文本识别任务
    const taskId = await taskQueueManager.addTask('textRecognizer', {
      resumeFileId,
      jobFileId,
      resumeText,
      jobText,
      resumeFileName,
      jobFileName,
      mode: resumeFileId || jobFileId ? 'mixed' : 'text',
      deviceId,
      userId,
      channelId,
    }, 0, undefined, parentTaskId);

    logger.info(`创建的任务ID: ${taskId}`);
    return { taskId };
  }

  /**
   * 创建会话消息并发送分析结果
   * @param analysisResult 分析结果
   * @param chatId 会话ID
   * @param taskId 当前任务ID
   * @returns 消息ID
   */
  public async createChatMessage4Analysis(
    analysisResult: ResumeAnalysisResult, 
    chatId: string,
    taskId: bigint
  ): Promise<string> {
    try {
      // 获取任务的 payload
      const taskPayload = await taskQueueManager.getTaskPayload(taskId);
      
      // AI 消息的元数据
      const aiMetadata = {
        analysisPayload: {
          generalAnalysis: analysisResult.generalAnalysis || '',
          jobMatchAnalysis: analysisResult.jobMatchAnalysis || '',
        },
        analysisComplete: true  // 标记分析已完成
      };
      
      // 用户消息的元数据，包含任务的 payload
      const userMetadata = taskPayload ? {
        recognizerPayload: {
          resumeText: taskPayload.resumeText || '',
          jobText: taskPayload.jobText || '',
          resumeFileId: taskPayload.resumeFileId || '',
          jobFileId: taskPayload.jobFileId || '',
          resumeFileName: taskPayload.resumeFileName || '',
          jobFileName: taskPayload.jobFileName || '',
          mode: taskPayload.mode as 'text' | 'mixed'
        }
      } : {};

      const { aiMessageId } = await MessageUtils.createMessagePair(
        "请分析我的简历",
        analysisResult.analysis,
        chatId,
        aiMetadata,
        userMetadata
      );
      
      logger.info(`为聊天 ${chatId} 创建了简历分析消息，ID: ${aiMessageId}`);
      return aiMessageId;
    } catch (error) {
      logger.error('创建交互式简历顾问失败:', error);
      return '创建交互式简历顾问失败，请稍后重试。';
    }
  }

  /**
   * 处理简历和岗位文本
   * @param params 文本处理参数
   * @returns 处理后的文本
   */
  public async processResumeTexts(params: {
    resumeFileId?: string;
    jobFileId?: string;
    resumeText?: string;
    jobText?: string;
    resumeFileName?: string;
    jobFileName?: string;
  }): Promise<{ resumeText: string; jobText: string, processMethod: string }> {
    const { resumeFileId, jobFileId, resumeText, jobText, resumeFileName, jobFileName } = params;
    let finalResumeText = resumeText;
    let finalJobText = jobText;

    try {
      // 处理简历文件
      let processMethod = 'unknown';
      if (resumeFileId) {
        const [resumeResult] = await processOSSFile([resumeFileId]);
        if (!resumeResult.text) {
          throw new Error(`简历文件 ${resumeFileName || resumeFileId} 处理失败或内容为空`);
        }
        finalResumeText = resumeResult.text;
        processMethod = resumeResult.processMethod;
      }

      // 处理岗位文件
      if (jobFileId) {
        const [jobResult] = await processOSSFile([jobFileId]);
        if (!jobResult.text) {
          throw new Error(`岗位文件 ${jobFileName || jobFileId} 处理失败或内容为空`);
        }
        finalJobText = jobResult.text;

        const { isValid, reason } = isValidResumeText(finalJobText, false);
        if (!isValid) {
          logger.warn(`职位描述文本验证未通过: ${reason}, 文件ID: ${jobFileId}`);
          finalJobText = '';
        } else {
          logger.info(`职位描述文本验证通过，长度: ${finalJobText.length}`);
        }
      }

      // 验证简历文本有效性
      if (!finalResumeText) {
        throw new Error('简历内容为空');
      }

      const validationResult = isValidResumeText(finalResumeText, true);
      if (!validationResult.isValid) {
        throw new Error(`简历内容无效: ${validationResult.reason}`);
      }

      return {
        resumeText: finalResumeText,
        jobText: finalJobText,
        processMethod,
      };
    } catch (error) {
      logger.error(`文本处理失败: ${error.message}`);
      throw error;
    }
  }

  public async createChatAndDeductPoints(params: {
    chatId?: string;
    deviceId?: string;
    channelId?: string;
    userId?: string;
  }): Promise<string> {
    const { chatId: existingChatId, deviceId, channelId, userId } = params;

    const { isNew, chatId } = await MessageUtils.createOrGetChat({
      chatId: existingChatId,
      title: '请分析我的简历',
      focusMode: 'resumeAnalyzer',
      deviceId: deviceId,
      source: channelId,
      userId: userId
    });

    // 如果是新创建的任务，执行扣费逻辑
    if (isNew) {
      await BillingUtils.deductUserBilling({
        chatId: chatId,
        channelId: channelId,
        userId: userId,
        taskType: 'resumeAnalyzer' as TaskType,
      });
    }

    return chatId;
  }

  /**
   * 提取简历分析报告中的核心改进点
   * @param analysisReport 分析报告内容
   * @returns 核心改进点数组
   */
  public async extractCoreImprovements(analysisReport: string): Promise<CoreImprovement[]> {
    try {      
      const resumeModel = await getSummarizerModel();
      if (!resumeModel) {
        logger.error('无法加载简历分析模型');
        return [];
      }
      
      const prompt = extractCoreImprovementsPrompt
        .replace('{analysisReport}', analysisReport);
      
      const result = await resumeModel.invoke(prompt);
      
      try {
        // 尝试解析JSON结果
        const improvements = JSON.parse(result.content);
        
        // 验证结果格式
        if (Array.isArray(improvements) && improvements.length > 0 && 
            improvements.every(item => typeof item.title === 'string' && typeof item.description === 'string')) {
          return improvements;
        } else {
          logger.warn('核心改进点格式不正确:', result.content);
          return [];
        }
      } catch (parseError) {
        logger.error(`解析核心改进点失败: ${parseError.message}`);
        return [];
      }
    } catch (error) {
      logger.error(`提取核心改进点失败: ${error.message}`);
      return [];
    }
  }
  
  /**
   * 根据分析结果优化简历
   * @param params 优化参数
   * @returns 优化结果，包含优化后的简历内容、状态和模型信息
   */
  public async optimizeResume(params: {
    resumeText: string;
    generalAnalysis?: string;
    jobMatchAnalysis?: string;
  }): Promise<{
    optimizedResume: string;
    optimizationHighlights: string;
    status: 'success' | 'error';
    model?: string;
    error?: string;
  }> {
    try {
      const { resumeText, generalAnalysis, jobMatchAnalysis } = params;
      
      const resumeModel = await getResumeModel();
      if (!resumeModel) {
        logger.error('无法加载简历优化模型');
        return {
          optimizedResume: '',
          optimizationHighlights: '',
          status: 'error',
          error: '无法加载简历优化模型'
        };
      }
      
      // 使用简历优化提示词
      const prompt = resumeOptimizationPrompt
        .replace('{resume}', resumeText)
        .replace('{general_analysis}', generalAnalysis || '')
        .replace('{job_match_analysis}', jobMatchAnalysis || '');
      
      const startTime = Date.now();
      const result = await resumeModel.invoke(prompt);
      logger.info(`简历优化耗时: ${Date.now() - startTime}ms`);
      
      // 提取优化内容和要点
      let optimizedContent = '';
      let optimizationHighlights = '';
      
      // 首先提取整个优化结果
      const fullResultMatch = result.content.match(/<optimization_result>([\s\S]*?)<\/optimization_result>/);
      const fullResult = fullResultMatch ? fullResultMatch[1].trim() : result.content;
      
      // 从完整结果中提取优化后的简历内容
      const resumeMatch = fullResult.match(/<optimized_resume>([\s\S]*?)<\/optimized_resume>/);
      optimizedContent = resumeMatch ? resumeMatch[1].trim() : fullResult;
      
      // 从完整结果中提取优化要点
      const highlightsMatch = fullResult.match(/<optimization_highlights>([\s\S]*?)<\/optimization_highlights>/);
      optimizationHighlights = highlightsMatch ? highlightsMatch[1].trim() : '';
      
      // 如果没有提取到优化要点，使用默认要点
      if (!optimizationHighlights) {
        optimizationHighlights = `- 调整了简历结构，使其更加清晰专业
- 增强了成就描述，添加了量化数据
- 优化了专业术语和关键词的使用
- 改进了整体表达方式和专业度`;
      }
      
      return {
        optimizedResume: optimizedContent,
        optimizationHighlights,
        status: 'success',
        model: resumeModel.modelName
      };
    } catch (error) {
      logger.error(`简历优化失败: ${error.message}`);
      return {
        optimizedResume: '',
        optimizationHighlights: '',
        status: 'error',
        error: `简历优化失败: ${error.message}`
      };
    }
  }

  /**
   * 创建会话消息并发送简历优化结果
   * @param optimizationResult 优化结果
   * @param chatId 会话ID
   * @param taskId 当前任务ID
   * @returns 消息ID
   */
  public async createChatMessage4Optimization(
    optimizationResult: {
      optimizedResume: string;
      optimizationHighlights: string;
      status: 'success' | 'error';
      model?: string;
      error?: string;
    }, 
    chatId: string,
    taskId: bigint
  ): Promise<string> {
    try {
      // 获取任务的 payload
      const taskPayload = await taskQueueManager.getTaskPayload(taskId);
      
      // 构建消息内容
      const messageContent = optimizationResult.status === 'success' 
        ? `## 📝 优化后的简历

根据对您简历的全面分析，我们已对原始简历进行了专业优化，提升了其整体质量和竞争力。以下是优化后的简历内容，您可以直接复制使用：

---

${optimizationResult.optimizedResume}

---

💡 **优化要点**：
${optimizationResult.optimizationHighlights}

您可以继续与我交流，获取更多关于简历和求职的建议。`
        : `## ❌ 简历优化未完成

很抱歉，在处理您的简历优化请求时遇到了问题：${optimizationResult.error || '未知错误'}

您可以尝试以下解决方法：
1. 重新提交简历进行分析
2. 确保简历内容完整且格式正确
3. 如果问题持续存在，请联系客服支持

您仍可以查看之前的简历分析结果，并根据分析建议手动优化您的简历。`;
      
      // AI 消息的元数据
      const aiMetadata = {
        optimizationPayload: {
          status: optimizationResult.status,
          model: optimizationResult.model || '',
          highlights: optimizationResult.optimizationHighlights || ''
        },
        optimizationComplete: true  // 标记优化已完成
      };
      
      // 用户消息的元数据
      const userMetadata = taskPayload ? {
        optimizationRequest: {
          resumeText: taskPayload.resumeText || '',
          jobDescription: taskPayload.jobDescription || '',
        }
      } : {};

      const { aiMessageId } = await MessageUtils.createMessagePair(
        "请优化我的简历",
        messageContent,
        chatId,
        aiMetadata,
        userMetadata
      );
      
      logger.info(`为聊天 ${chatId} 创建了简历优化消息，ID: ${aiMessageId}`);
      return aiMessageId;
    } catch (error) {
      logger.error('创建简历优化消息失败:', error);
      return '';
    }
  }
}
export const resumeAnalyzer = new ResumeAnalyzer();
