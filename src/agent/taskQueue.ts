import { db } from '../db';
import { taskQueue, TaskStatus, AgentType } from '../db/schema';
import { eq, and, lte, desc } from 'drizzle-orm';
import logger from '../utils/logger';
import { resumeAnalyzer } from './resumeAnalyzer';
import { CoreImprovement } from './resumeAnalyzer';

export class TaskQueueManager {
  private static instance: TaskQueueManager;
  private runningTasks: Set<bigint> = new Set();
  private readonly MAX_CONCURRENT_TASKS = 5;

  private constructor() {
    this.processQueue();
  }

  public static getInstance(): TaskQueueManager {
    if (!TaskQueueManager.instance) {
      TaskQueueManager.instance = new TaskQueueManager();
    }
    return TaskQueueManager.instance;
  }

  public async addTask(
    agentType: AgentType,
    payload: Record<string, any>,
    priority: number = 0,
    chatId?: string,
    parentTaskId?: bigint
  ): Promise<bigint> {
    try {
      const values = {
        agentType,
        payload,
        priority,
        status: 'pending',
        chatId,
        parentTaskId
      }
      const [task] = await db.insert(taskQueue).values(values).returning({ id: taskQueue.id });
  
      logger.info(`任务已添加到队列: ${task.id}, 会话ID: ${chatId}, 父任务ID: ${parentTaskId}`);
      return task.id;
    } catch (error) {
      logger.error(`添加任务失败: ${error.message}`);
      throw error;
    }
  }

  private async processQueue() {
    while (true) {
      try {
        await this.processPendingTasks();
      } catch (error) {
        logger.error(`处理队列出错: ${error.message}`);
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  private async processPendingTasks() {
    if (this.runningTasks.size >= this.MAX_CONCURRENT_TASKS) {
      return;
    }

    const pendingTasks = await db.select()
      .from(taskQueue)
      .where(and(
        eq(taskQueue.status, 'pending'),
        lte(taskQueue.retryCount, taskQueue.maxRetries)
      ))
      .orderBy(taskQueue.priority, taskQueue.createdAt)
      .limit(this.MAX_CONCURRENT_TASKS - this.runningTasks.size);

    for (const task of pendingTasks) {
      if (!this.runningTasks.has(task.id)) {
        this.executeTask(task);
      }
    }
  }

  private async executeTask(task: typeof taskQueue.$inferSelect) {
    this.runningTasks.add(task.id);

    try {
      await db.update(taskQueue)
        .set({
          [taskQueue.status.name]: 'processing' as TaskStatus,
          [taskQueue.startTime.name]: new Date(),
          [taskQueue.updatedAt.name]: new Date()
        })
        .where(eq(taskQueue.id, task.id));
      
      const result = await this.executeAgentTask(task);

      await db.update(taskQueue)
        .set({
          [taskQueue.status.name]: 'completed' as TaskStatus,
          [taskQueue.result.name]: result as Record<string, any>,
          [taskQueue.endTime.name]: new Date(),
          [taskQueue.updatedAt.name]: new Date()
        })
        .where(eq(taskQueue.id, task.id));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await db.update(taskQueue)
        .set({
          [taskQueue.status.name]: 'failed' as TaskStatus,
          [taskQueue.error.name]: errorMessage,
          [taskQueue.retryCount.name]: task.retryCount + 1,
          [taskQueue.endTime.name]: new Date(),
          [taskQueue.updatedAt.name]: new Date()
        })
        .where(eq(taskQueue.id, task.id));
    } finally {
      this.runningTasks.delete(task.id);
    }
  }

  private async executeAgentTask(task: typeof taskQueue.$inferSelect) {
    switch (task.agentType) {
      case 'textRecognizer': {
        const { resumeFileId, jobFileId, resumeText, jobText, resumeFileName, jobFileName, deviceId, userId, channelId } = task.payload;

        try {
          const { resumeText: finalResumeText, jobText: finalJobText, processMethod } = await resumeAnalyzer.processResumeTexts({
            resumeFileId,
            jobFileId,
            resumeText,
            jobText,
            resumeFileName,
            jobFileName
          });

          // 文本验证成功后，创建会话并处理扣费
          const chatId = await resumeAnalyzer.createChatAndDeductPoints({
            chatId: task.chatId,
            deviceId,
            channelId,
            userId
          });

          // 更新当前任务的 chatId
          await db.update(taskQueue)
            .set({ chatId: chatId })
            .where(eq(taskQueue.id, task.id));

          logger.info(`文本识别任务 ${task.id} 完成，准备创建简历分析任务，会话ID: ${chatId}`);

          const analysisTaskId = await this.addTask('resumeAnalyzer', {
            resumeText: finalResumeText,
            jobDescription: finalJobText,
            deviceId,
            userId
          }, 0, chatId, task.id);

          return {
            processMethod: processMethod,
            resumeText: finalResumeText,
            jobText: finalJobText,
            analysisTaskId,
            chatId,
          };
        } catch (error) {
          logger.error(`文本识别任务失败: ${error.message}`);
          throw error;
        }
      }
    
      case 'resumeAnalyzer': {
        const { resumeText, jobDescription } = task.payload;
        
        logger.info(`执行简历分析任务 ${task.id}，会话ID: ${task.chatId}`);
        
        const analysisResult = await resumeAnalyzer.analyzeResume(resumeText, jobDescription);
        
        // 如果有chatId，创建会话消息
        if (task.chatId) {
          logger.info(`简历分析完成，创建会话消息，会话ID: ${task.chatId}`);
          await resumeAnalyzer.createChatMessage4Analysis(analysisResult, task.chatId, task.id);
          
          // 创建提取核心改进点任务
          /*
          logger.info(`创建提取核心改进点任务，会话ID: ${task.chatId}`);
          await this.addTask('coreImprovements', {
            analysisReport: analysisResult.analysis,
            deviceId: task.payload.deviceId,
            userId: task.payload.userId
          }, 0, task.chatId, task.id);
          */
          
          // 创建简历优化任务
          logger.info(`创建简历优化任务，会话ID: ${task.chatId}`);
          await this.addTask('resumeOptimizer', {
            resumeText,
            jobDescription,
            generalAnalysis: analysisResult.generalAnalysis,
            jobMatchAnalysis: analysisResult.jobMatchAnalysis,
            deviceId: task.payload.deviceId,
            userId: task.payload.userId
          }, 0, task.chatId, task.id);
        }
        
        return analysisResult;
      }
      
      case 'coreImprovements': {
        const { analysisReport } = task.payload;
        
        logger.info(`执行提取核心改进点任务 ${task.id}，会话ID: ${task.chatId}`);
        
        const improvements = await resumeAnalyzer.extractCoreImprovements(analysisReport);
        logger.info(`提取核心改进点任务 ${task.id} 完成，会话ID: ${task.chatId}, resumeImprovements: ${JSON.stringify(improvements)}`);
        
        return { improvements };
      }
      
      case 'resumeOptimizer': {
        const { resumeText, generalAnalysis, jobMatchAnalysis } = task.payload;
        
        logger.info(`执行简历优化任务 ${task.id}，会话ID: ${task.chatId}`);
        
        const optimizationResult = await resumeAnalyzer.optimizeResume({
          resumeText,
          generalAnalysis,
          jobMatchAnalysis
        });
        
        // 如果有chatId，创建会话消息
        if (task.chatId) {
          logger.info(`简历优化完成，创建会话消息，会话ID: ${task.chatId}`);
          await resumeAnalyzer.createChatMessage4Optimization(optimizationResult, task.chatId, task.id);
        }
        
        logger.info(`简历优化任务 ${task.id} 完成，会话ID: ${task.chatId}, 状态: ${optimizationResult.status}`);
        
        return optimizationResult;
      }
    
      default:
        throw new Error(`未知的 agent 类型: ${task.agentType}`);
    }
  }

  // 获取任务链上的所有结果
  public async getTaskResult(taskId: bigint): Promise<{
    status: TaskStatus;
    result?: Record<string, any>;
    error?: string;
  } | null> {
    const task = await db.select({ 
      status: taskQueue.status,
      result: taskQueue.result,
      error: taskQueue.error
    })
    .from(taskQueue)
    .where(eq(taskQueue.id, taskId))
    .limit(1);

    if (!task[0]) return null;
    
    return {
      status: task[0].status,
      result: task[0].result,
      error: task[0].error
    };
  }

  /**
   * 获取任务链上的所有结果
   * @param taskId 起始任务ID
   * @returns 任务链上的所有任务结果
   */
  public async getTaskChainResults(taskId: bigint): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    let originalTaskId = taskId;
    let chatId = null;
    
    try {
      // 1. 获取原始任务
      const originalTask = await db.select()
        .from(taskQueue)
        .where(eq(taskQueue.id, originalTaskId))
        .limit(1);
      
      if (!originalTask[0]) {
        logger.warn(`任务 ${taskId} 不存在`);
        return { originalTaskId: String(originalTaskId), error: '任务不存在' };
      }
      
      chatId = originalTask[0].chatId;
      results.originalTaskId = String(originalTaskId);
      results.chatId = chatId;
      
      // 2. 存储原始任务信息
      this.storeTaskInResults(results, originalTask[0]);
      
      // 3. 如果有chatId，一次性查询所有相关任务
      if (chatId) {
        const relatedTasks = await db.select()
          .from(taskQueue)
          .where(eq(taskQueue.chatId, chatId))
          .orderBy(desc(taskQueue.createdAt));
        
        // 处理相关任务
        for (const task of relatedTasks) {
          // 跳过原始任务，因为已经处理过了
          if (task.id === originalTaskId) continue;
          
          // 根据任务类型存储任务信息
          this.storeTaskInResults(results, task);
        }
      } 
      // 4. 如果原始任务是文本识别任务，但没有chatId，查找其子任务
      else if (originalTask[0].agentType === 'textRecognizer' && 
               originalTask[0].result?.analysisTaskId) {
        
        const analysisTaskId = originalTask[0].result.analysisTaskId;
        const analysisTasks = await db.select()
          .from(taskQueue)
          .where(eq(taskQueue.id, analysisTaskId))
          .limit(1);
        
        if (analysisTasks.length > 0) {
          this.storeTaskInResults(results, analysisTasks[0]);
        }
      }
      
      // 5. 添加日志
      logger.info(`任务链结果: ${JSON.stringify({
        originalTaskId: String(originalTaskId),
        chatId,
        hasAnalyzerTask: !!results.analyzerTask,
        hasOptimizerTask: !!results.optimizerTask,
        hasImprovementsTask: !!results.improvementsTask
      })}`);
      
      return results;
    } catch (error) {
      logger.error(`获取任务链结果失败: ${error.message}`);
      return { 
        originalTaskId: String(originalTaskId), 
        error: `获取任务链结果失败: ${error.message}` 
      };
    }
  }

  /**
   * 将任务信息存储到结果对象中
   * @param results 结果对象
   * @param task 任务对象
   */
  private storeTaskInResults(results: Record<string, any>, task: any): void {
    // 创建任务信息对象
    const taskInfo = {
      taskId: String(task.id),
      status: task.status,
      result: task.result,
      error: task.error,
      chatId: task.chatId
    };
    
    // 1. 根据任务类型存储
    switch (task.agentType) {
      case 'textRecognizer':
        results.textRecognizerTask = taskInfo;
        // 如果文本识别任务已完成，将其结果也存储在completed字段中
        if (task.status === 'completed') {
          results.completed = taskInfo;
        }
        break;
      case 'resumeAnalyzer':
        // 只有当没有分析任务或当前任务更新时才更新
        if (!results.analyzerTask || BigInt(task.id) > BigInt(results.analyzerTask.taskId)) {
          results.analyzerTask = taskInfo;
        }
        break;
      case 'resumeOptimizer':
        // 只有当没有优化任务或当前任务更新时才更新
        if (!results.optimizerTask || BigInt(task.id) > BigInt(results.optimizerTask.taskId)) {
          results.optimizerTask = taskInfo;
        }
        break;
      case 'coreImprovements':
        // 只有当没有改进点任务或当前任务更新时才更新
        if (!results.improvementsTask || BigInt(task.id) > BigInt(results.improvementsTask.taskId)) {
          results.improvementsTask = taskInfo;
        }
        break;
    }
    
    // 2. 保留原有的按状态存储，以保持向后兼容
    // 只有当没有该状态的任务或当前任务更新时才更新
    if (!results[task.status] || BigInt(task.id) > BigInt(results[task.status].taskId)) {
      results[task.status] = taskInfo;
    }
  }

  /**
   * 获取简历分析相关的文本内容
   * @param taskId 当前任务ID
   * @returns 简历文本和职位文本
   */
  public async getResumeAnalysisTexts(taskId: bigint): Promise<{ resumeText: string; jobText: string }> {
    try {
      // 获取当前任务
      const currentTask = await db.select()
        .from(taskQueue)
        .where(eq(taskQueue.id, taskId))
        .limit(1);

      if (!currentTask[0]) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      // 如果是简历分析任务，检查是否有父任务
      if (currentTask[0].agentType === 'resumeAnalyzer' && currentTask[0].parentTaskId) {
        // 获取父任务（文本识别任务）
        const parentTask = await db.select()
          .from(taskQueue)
          .where(eq(taskQueue.id, currentTask[0].parentTaskId))
          .limit(1);

        if (parentTask[0] && parentTask[0].agentType === 'textRecognizer' && parentTask[0].result) {
          // 从文本识别任务的结果中获取文本
          return {
            resumeText: parentTask[0].result.resumeText || '',
            jobText: parentTask[0].result.jobText || ''
          };
        }
      }

      // 如果没有父任务或父任务不是文本识别任务，从当前任务的 payload 中获取
      return {
        resumeText: currentTask[0].payload.resumeText || '',
        jobText: currentTask[0].payload.jobDescription || ''
      };
    } catch (error) {
      logger.error(`获取简历分析文本失败: ${error.message}`);
      return { resumeText: '', jobText: '' };
    }
  }

  /**
   * 获取任务的 payload
   * @param taskId 任务ID
   * @returns 任务的 payload 数据
   */
  public async getTaskPayload(taskId: bigint): Promise<Record<string, any> | null> {
    try {
      const task = await db.select({
        payload: taskQueue.payload,
        parentTaskId: taskQueue.parentTaskId
      })
      .from(taskQueue)
      .where(eq(taskQueue.id, taskId))
      .limit(1);

      if (!task[0]) return null;

      // 如果是简历分析任务且有父任务，返回父任务的 payload
      if (task[0].parentTaskId) {
        const parentTask = await db.select({
          payload: taskQueue.payload
        })
        .from(taskQueue)
        .where(eq(taskQueue.id, task[0].parentTaskId))
        .limit(1);

        return parentTask[0]?.payload || null;
      }

      return task[0].payload;
    } catch (error) {
      logger.error(`获取任务 payload 失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取简历改进点建议
   * @param chatId 会话ID
   * @returns 改进点建议数组
   */
  public async getCoreImprovements(chatId: string): Promise<CoreImprovement[] | null> {
    try {
      // 查询与该会话关联的resumeAdvisor任务
      const tasks = await db.select({
        result: taskQueue.result
      })
      .from(taskQueue)
      .where(
        and(
          eq(taskQueue.chatId, chatId),
          eq(taskQueue.agentType, 'coreImprovements'),
          eq(taskQueue.status, 'completed')
        )
      )
      .orderBy(desc(taskQueue.createdAt))
      .limit(1);

      if (tasks.length === 0 || !tasks[0].result) {
        logger.info(`未找到会话 ${chatId} 的改进点建议`);
        return null;
      }

      // 从任务结果中提取改进点
      const improvements = tasks[0].result.improvements;
      if (!improvements || !Array.isArray(improvements)) {
        logger.warn(`会话 ${chatId} 的改进点格式不正确`);
        return null;
      }

      return improvements;
    } catch (error) {
      logger.error(`获取改进点建议失败: ${error.message}`);
      return null;
    }
  }
}

export const taskQueueManager = TaskQueueManager.getInstance();
