import fs from 'fs';
import path from 'path';
import toml from '@iarna/toml';

const isDev = process.env.NODE_ENV === 'development';
const isAudit = process.env.NODE_ENV === 'audit';
const configFileName = isDev ? 'config.dev.toml' : isAudit ? 'config.audit.toml' : 'config.toml';

interface Config {
  GENERAL: {
    PORT: number;
    SIMILARITY_MEASURE: string;
    KEEP_ALIVE: string;
  };
  API_KEYS: {
    OPENAI: string;
    GROQ: string;
    ANTHROPIC: string;
    GEMINI: string;
  };
  API_ENDPOINTS: {
    SEARXNG: string;
    OLLAMA: string;
  };
  LINK_CONTENT_API: {
    BASE_URL: string;
  };
  CUSTOM_MODEL: {
    CUSTOM_BASE_URL: string;
    CUSTOM_CHAT_MODEL: string;
    CUSTOM_EMBEDDING_MODEL: string;
    CUSTOM_SUMMARIZER_MODEL: string;  // 添加总结模型配置
    CUSTOM_RESUME_MODEL: string;      // 简历分析模型配置
    CUSTOM_SETTING_MODEL: boolean;
  };
  DATABASE: {
    HOST: string;
    PORT: number;
    USER: string;
    PASSWORD: string;
    NAME: string;
  };
  OCR: {
    ALIBABA_CLOUD_ACCESS_KEY_ID: string;
    ALIBABA_CLOUD_ACCESS_KEY_SECRET: string;
  };
  FILE_OCR: {
    ALIBABA_CLOUD_ACCESS_KEY_ID: string;
    ALIBABA_CLOUD_ACCESS_KEY_SECRET: string;
  };
  JM_INFO: {
    JM_BASE_URL: string;
    PAY_CHANNELS: string[];
    PAY_POINTS: {
      webSearch: number;
      managementMaster: number;
      resumeAnalyzer: number;
    };
    PAY_ENABLE: boolean;
    AVAILABLE_TASKS: TaskType[];
  };
}

export type TaskType = 'webSearch' | 'managementMaster' | 'resumeAnalyzer' | 'careerCoach' | 'careerCoachAdvise'; // 任务类型

type RecursivePartial<T> = {
  [P in keyof T]?: RecursivePartial<T[P]>;
};

const loadConfig = () =>
  toml.parse(
    fs.readFileSync(path.join(__dirname, `../${configFileName}`), 'utf-8'),
  ) as any as Config;

export const getPort = () => loadConfig().GENERAL.PORT;

export const getSimilarityMeasure = () =>
  loadConfig().GENERAL.SIMILARITY_MEASURE;

export const getKeepAlive = () => loadConfig().GENERAL.KEEP_ALIVE;

export const getOpenaiApiKey = () => loadConfig().API_KEYS.OPENAI;

export const getGroqApiKey = () => loadConfig().API_KEYS.GROQ;

export const getAnthropicApiKey = () => loadConfig().API_KEYS.ANTHROPIC;

export const getGeminiApiKey = () => loadConfig().API_KEYS.GEMINI;

export const getSearxngApiEndpoint = () =>
  process.env.SEARXNG_API_URL || loadConfig().API_ENDPOINTS.SEARXNG;

export const getOllamaApiEndpoint = () => loadConfig().API_ENDPOINTS.OLLAMA;

export const updateConfig = (config: RecursivePartial<Config>) => {
  const currentConfig = loadConfig();

  for (const key in currentConfig) {
    if (!config[key]) config[key] = {};

    if (typeof currentConfig[key] === 'object' && currentConfig[key] !== null) {
      for (const nestedKey in currentConfig[key]) {
        if (
          !config[key][nestedKey] &&
          currentConfig[key][nestedKey] &&
          config[key][nestedKey] !== ''
        ) {
          config[key][nestedKey] = currentConfig[key][nestedKey];
        }
      }
    } else if (currentConfig[key] && config[key] !== '') {
      config[key] = currentConfig[key];
    }
  }

  fs.writeFileSync(
    path.join(__dirname, `../${configFileName}`),
    toml.stringify(config),
  );
};

// 获取自定义 Base URL
export const getCustomBaseURL = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_BASE_URL || '';
};

// 获取自定义聊天模型
export const getCustomChatModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_CHAT_MODEL || '';
};

// 获取自定义嵌入模型
export const getCustomEmbeddingModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_EMBEDDING_MODEL || '';
};

// 获取自定义模型设置显示状态
export const getCustomSettingModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_SETTING_MODEL ?? false;
};

// 获取自定义总结模型
export const getCustomSummarizerModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_SUMMARIZER_MODEL || '';
};

// 获取自定义简历分析模型
export const getCustomResumeModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_RESUME_MODEL || '';
};

export const getLinkContentApiBaseUrl = () => {
  return loadConfig().LINK_CONTENT_API.BASE_URL;
}

export default {
  OCR: {
    ALIBABA_CLOUD_ACCESS_KEY_ID: loadConfig().OCR.ALIBABA_CLOUD_ACCESS_KEY_ID,
    ALIBABA_CLOUD_ACCESS_KEY_SECRET: loadConfig().OCR.ALIBABA_CLOUD_ACCESS_KEY_SECRET
  },
  FILE_OCR: {
    ALIBABA_CLOUD_ACCESS_KEY_ID: loadConfig().FILE_OCR.ALIBABA_CLOUD_ACCESS_KEY_ID,
    ALIBABA_CLOUD_ACCESS_KEY_SECRET: loadConfig().FILE_OCR.ALIBABA_CLOUD_ACCESS_KEY_SECRET
  }
};

// 数据库配置获取方法
export const getDatabaseConfig = () => {
  const config = loadConfig();
  return {
    host: process.env.DB_HOST || config.DATABASE.HOST,
    port: parseInt(process.env.DB_PORT || config.DATABASE.PORT.toString()),
    user: process.env.DB_USER || config.DATABASE.USER,
    password: process.env.DB_PASSWORD || config.DATABASE.PASSWORD,
    database: process.env.DB_NAME || config.DATABASE.NAME,
  };
};

// 获取JM API 基础 URL
export const getJMBaseURL = () => {
  return loadConfig().JM_INFO?.JM_BASE_URL || '';
};

// 获取扣费渠道列表
export const getPayChannels = () => {
  return loadConfig().JM_INFO?.PAY_CHANNELS || [];
};

// 获取任务点数配置
export const getPayPoints = () => {
  return loadConfig().JM_INFO?.PAY_POINTS || {
    webSearch: 0,
    managementMaster: 0,
    resumeAnalyzer: 0
  };
};

// 获取所有任务类型的电量消耗配置
export const getAllTaskPoints = () => {
  const taskPoints = getPayPoints();
  return {
    webSearch: taskPoints.webSearch || 0,
    managementMaster: taskPoints.managementMaster || 0,
    resumeAnalyzer: taskPoints.resumeAnalyzer || 0
  };
};

// 获取特定任务类型的点数
export const getPayPointsByType = (taskType: TaskType) => {
  const taskPoints = loadConfig().JM_INFO?.PAY_POINTS;
  return taskPoints?.[taskType] || 0;
};

// 获取扣费启用状态
export const getPaytEnable = () => {
  return loadConfig().JM_INFO?.PAY_ENABLE ?? false;
};

export const getAvailableTasks = () => {
  return loadConfig().JM_INFO?.AVAILABLE_TASKS || ['webSearch', 'managementMaster', 'resumeAnalyzer'];
};
