export type KnowledgeChannel = '' | 'knowledgeFromRAGFlow' | 'knowledgeFromDify' | 'knowledgeFromRAGFlowJM' | 'knowledgeFromDifyJM';

export const KNOWLEDGE_CONFIGS = {
  // 管理大师，供C端用户使用
  knowledgeFromRAGFlow: {
    url: 'https://rag.foundingaz.com:8090/api/v1/retrieval',
    key: 'ragflow-U1ZDI3YmQ0MTM3ODExZjBhNTc4MDI0Mm',
    dataset_ids: ["f2a150fa152811f0ab450242ac120007"],
    top_k: 8,
    similarity_threshold: 0.2, // 最小相似度分数
    vector_similarity_weight: 0.3, // 向量余弦相似度的权重
    rerank_id:'gte-rerank', // 重排序模型的 ID
  },
  knowledgeFromDify: {
    url: 'https://ai.foundingaz.com/v1/workflows/run',
    key: 'app-SQ60OjhgW61Pbv58MqVDiimh'
  },
  
  // 酵母管理大师，供酵母内部用户使用
  knowledgeFromRAGFlowJM: {
    url: 'https://rag.foundingaz.com:8090/api/v1/retrieval',
    key: 'ragflow-U1ZDI3YmQ0MTM3ODExZjBhNTc4MDI0Mm',
    dataset_ids: ["f2a150fa152811f0ab450242ac120007"],
    top_k: 8,
    similarity_threshold: 0.2, // 最小相似度分数
    vector_similarity_weight: 0.3, // 向量余弦相似度的权重
    rerank_id:'gte-rerank', // 重排序模型的 ID
  },
  knowledgeFromDifyJM: {
    url: 'https://ai.foundingaz.com/v1/workflows/run',
    key: 'app-ZfMzKmlYKgy8FdFlOFwWPtrz'
  },
} as const;