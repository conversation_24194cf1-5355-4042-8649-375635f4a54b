import { drizzle } from 'drizzle-orm/better-sqlite3';
import { drizzle as pgDrizzle } from 'drizzle-orm/node-postgres';
import Database from 'better-sqlite3';
import { Pool } from 'pg';
import { getDatabaseConfig } from '../config';
import * as sqliteSchema from './schema_sqllite';
import * as pgSchema from './schema';

/* 数据迁移命令：
  - yarn run db:push 
  - yarn ts-node src/db/migrate.ts
*/
const migrateData = async () => {
  // 源数据库（SQLite）
  const sqlite = new Database('./data/db.sqlite');
  const sourceDb = drizzle(sqlite, { schema: sqliteSchema });

  // 目标数据库（PostgreSQL）
  const pool = new Pool(getDatabaseConfig());
  const targetDb = pgDrizzle(pool, { schema: pgSchema });

  try {
    // 迁移聊天数据
    const chats = await sourceDb.query.chats.findMany();
    console.log(`开始迁移聊天数据，共 ${chats.length} 条记录`);
    
    for (const chat of chats) {
      try {
        // SQLite 中 files 是 text 类型，默认值是 '[]'
        let parsedFiles: { name: string; fileId: string }[] = [];
        if (chat.files) {
          try {
            parsedFiles = JSON.parse(chat.files as string);
            if (!Array.isArray(parsedFiles)) {
              console.warn(`files 不是数组格式，chatId: ${chat.id}, files: ${chat.files}`);
              parsedFiles = [];
            }
          } catch (e) {
            console.warn(`解析 files 失败，chatId: ${chat.id}, files: ${chat.files}`);
          }
        }

        const values = {
          id: chat.id as string,
          title: chat.title as string,
          createdAt: (() => {
            try {
              if (!chat.createdAt) return new Date();
              const dateStr = String(chat.createdAt).trim();
              
              // 处理 SQLite 的时间戳格式
              if (/^\d+$/.test(dateStr)) {
                return new Date(parseInt(dateStr));
              }
              
              // 处理 GMT 格式
              if (dateStr.includes('GMT')) {
                const gmtDate = new Date(dateStr);
                if (!isNaN(gmtDate.getTime())) {
                  return gmtDate;
                }
              }
              
              // 处理标准日期格式
              const date = new Date(dateStr.replace(' ', 'T'));
              // 检查日期是否有效
              return isNaN(date.getTime()) ? new Date('2025-03-12') : date;
            } catch (e) {
              const defaultDate = new Date('2025-03-12');
              console.warn(`解析日期失败，使用默认时间 ${defaultDate.toISOString()}，chatId: ${chat.id}, createdAt: ${chat.createdAt}`);
              return defaultDate;
            }
          })(),
          focusMode: chat.focusMode as string,
          deviceId: chat.deviceId as string,
          files: parsedFiles
        }

        await targetDb.insert(pgSchema.chats).values(values).onConflictDoNothing();
      } catch (error) {
        console.error(`迁移聊天记录失败，chatId: ${chat.id}`, error);
      }
    }
    console.log('聊天数据迁移完成');

    // 迁移消息数据
    const messages = await sourceDb.query.messages.findMany();
    console.log(`开始迁移消息数据，共 ${messages.length} 条记录`);
    
    for (const message of messages) {
      const values = {
        content: message.content as string,
        chatId: message.chatId as string,
        messageId: message.messageId as string,
        role: (message.role || 'user') as string,
        metadata: message.metadata ? JSON.parse(message.metadata as string) : null
      }
      await targetDb.insert(pgSchema.messages).values(values).onConflictDoNothing();
    }
    console.log('消息数据迁移完成');

    // 迁移链接内容数据
    const linkContents = await sourceDb.query.linkContents.findMany();
    console.log(`开始迁移链接内容数据，共 ${linkContents.length} 条记录`);
    
    for (const link of linkContents) {
      const values = {
        id: link.id as string,
        link: link.link as string,
        title: link.title as string,
        contents: link.contents ? JSON.parse(link.contents as string) as string[] : [],
        timestamp: link.timestamp ? new Date(String(link.timestamp).replace(' ', 'T')) : new Date(),
        tool: (link.tool || 'default') as string,
        createdAt: link.createdAt ? new Date(String(link.createdAt).replace(' ', 'T')) : new Date(),
        updatedAt: link.updatedAt ? new Date(String(link.updatedAt).replace(' ', 'T')) : new Date()
      }
      await targetDb.insert(pgSchema.linkContents).values(values).onConflictDoNothing();
    }
    console.log('链接内容数据迁移完成');

    console.log('所有数据迁移完成');
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    await pool.end();
    sqlite.close();
  }
};

migrateData().catch(console.error);