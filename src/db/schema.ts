import { text, integer, jsonb, boolean, timestamp, pgTable, index, serial, bigserial } from 'drizzle-orm/pg-core';

export const messages = pgTable('messages', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  content: text('content').notNull(),
  chatId: text('chatId').notNull(),
  messageId: text('messageId').notNull(),
  role: text('type').notNull(),
  metadata: jsonb('metadata').$type<Record<string, any>>(),
  isDeleted: boolean('isDeleted').notNull().default(false)
}, (table) => ({
  chatIdIdx: index('messages_chat_id_idx').on(table.chatId)
}));

interface File {
  name: string;
  fileId: string;
}

export const hotQuestions = pgTable('hot_questions', {
  id: serial('id').primaryKey(),
  title: text('title').notNull(),
  isEnabled: boolean('is_enabled').notNull().default(true),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
});

export const chats = pgTable('chats', {
  id: text('id').primaryKey().notNull(),
  title: text('title').notNull(),
  createdAt: timestamp('createdAt').defaultNow(),
  focusMode: text('focusMode').notNull(),
  deviceId: text('deviceId'),
  userId: text('user_id'),
  files: jsonb('files').$type<File[]>().default([]),
  isDeleted: boolean('isDeleted').notNull().default(false),
  source: text('source').default('web'),
  hotQuestionId: integer('hot_question_id').references(() => hotQuestions.id),  // 热门问题关联
}, (table) => ({
  deviceIdIdx: index('chats_device_id_idx').on(table.deviceId),
  userIdIdx: index('chats_user_id_idx').on(table.userId)
}));

export const linkContents = pgTable('link_contents', {
  id: text('id').primaryKey().notNull(), 
  link: text('link').notNull(),
  title: text('title').notNull(),
  contents: jsonb('contents').$type<string[]>().notNull(),
  timestamp: timestamp('timestamp').defaultNow(),
  tool: text('tool').default('default'),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
}, (table) => ({
  linkIdx: index('link_contents_link_idx').on(table.link)
}));


// 任务状态枚举
export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type AgentType = 'resumeAnalyzer' | 'textRecognizer' | 'managementMaster' | 'writingAssistant' | 'coreImprovements' | 'resumeOptimizer';

// 任务队列表
export const taskQueue = pgTable('task_queue', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  chatId: text('chat_id'),
  agentType: text('agent_type').notNull(),
  payload: jsonb('payload').$type<Record<string, any>>().notNull(),
  status: text('status').$type<TaskStatus>().notNull().default('pending'),
  priority: integer('priority').notNull().default(0),
  retryCount: integer('retry_count').notNull().default(0),
  maxRetries: integer('max_retries').notNull().default(3),
  error: text('error'),
  result: jsonb('result').$type<Record<string, any>>(),
  startTime: timestamp('start_time'),
  endTime: timestamp('end_time'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  parentTaskId: bigserial('parent_task_id', { mode: 'number' }).references(() => taskQueue.id),
}, (table) => ({
  statusIdx: index('task_queue_status_idx').on(table.status),
  agentTypeIdx: index('task_queue_agent_type_idx').on(table.agentType),
  createdAtIdx: index('task_queue_created_at_idx').on(table.createdAt),
  chatIdIdx: index('task_queue_chat_id_idx').on(table.chatId)
}));
