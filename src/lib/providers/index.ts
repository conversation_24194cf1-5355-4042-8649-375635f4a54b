import { loadChatModel, loadEmbeddingsModel, loadSummarizerModel, loadResumeModel } from './openai';

let modelCache = {
  chat: null,
  embedding: null,
  summarizer: null,
  resume: null,
};

export const getChatModel = async () => {
  if (modelCache.chat) {
    return modelCache.chat;
  }

  try {
    const model = await loadChatModel();
    if (!model) {
      throw new Error('Failed to load chat model');
    }
    modelCache.chat = model;
    return model;
  } catch (err) {
    console.error('Error loading chat model:', err);
    throw err;
  }
};

export const getEmbeddingModel = async () => {
  if (modelCache.embedding) {
    return modelCache.embedding;
  }

  try {
    const model = await loadEmbeddingsModel();
    if (!model) {
      throw new Error('Failed to load embedding model');
    }
    modelCache.embedding = model;
    return model;
  } catch (err) {
    console.error('Error loading embedding model:', err);
    throw err;
  }
};

export const getSummarizerModel = async () => {
  if (modelCache.summarizer) {
    return modelCache.summarizer;
  }

  try {
    const model = await loadSummarizerModel();
    if (!model) {
      throw new Error('Failed to load summarizer model');
    }
    modelCache.summarizer = model;
    return model;
  } catch (err) {
    console.error('Error loading summarizer model:', err);
    throw err;
  }
};

export const getResumeModel = async () => {
  if (modelCache.resume) {
    return modelCache.resume;
  }

  try {
    const model = await loadResumeModel();
    if (!model) {
      throw new Error('Failed to load resume model');
    }
    modelCache.resume = model;
    return model;
  } catch (err) {
    console.error('Error loading resume model:', err);
    throw err;
  }
};

// 缓存清理
export const clearModelCache = () => {
  modelCache = {
    chat: null,
    embedding: null,
    summarizer: null,
    resume: null,
  };
};
