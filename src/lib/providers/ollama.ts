import { OllamaEmbeddings } from '@langchain/ollama';
import { getKeepAlive, getOllamaApiEndpoint } from '../../config';
import logger from '../../utils/logger';
import { ChatOllama } from '@langchain/ollama';
import axios from 'axios';

// 定义嵌入模型列表
const EMBEDDING_MODELS = ['nomic-embed-text', 'all-minilm', 'bge'];

// 判断是否为嵌入模型
const isEmbeddingModel = (modelName: string) => {
  return EMBEDDING_MODELS.some(name => modelName.toLowerCase().includes(name));
};

export const loadOllamaChatModels = async () => {
  const ollamaEndpoint = getOllamaApiEndpoint();
  const keepAlive = getKeepAlive();

  if (!ollamaEndpoint) return {};

  try {
    const response = await axios.get(`${ollamaEndpoint}/api/tags`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const { models: ollamaModels } = response.data;

    const chatModels = ollamaModels.reduce((acc, model) => {
      // 过滤掉嵌入模型
      if (!isEmbeddingModel(model.model)) {
        acc[model.model] = {
          displayName: model.name,
          model: new ChatOllama({
            baseUrl: ollamaEndpoint,
            model: model.model,
            temperature: 0.7,
            keepAlive: keepAlive,
          }),
        };
      }
      return acc;
    }, {});

    return chatModels;
  } catch (err) {
    logger.error(`Error loading Ollama models: ${err}`);
    return {};
  }
};

export const loadOllamaEmbeddingsModels = async () => {
  const ollamaEndpoint = getOllamaApiEndpoint();

  if (!ollamaEndpoint) return {};

  try {
    const response = await axios.get(`${ollamaEndpoint}/api/tags`, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 5000
    });

    const { models: ollamaModels } = response.data;

    const embeddingsModels = ollamaModels.reduce((acc, model) => {
      // 只保留嵌入模型
      if (isEmbeddingModel(model.model)) {
        acc[model.model] = {
          displayName: model.name,
          model: new OllamaEmbeddings({
            baseUrl: ollamaEndpoint,
            model: model.model,
          }),
        };
      }
      return acc;
    }, {});

    return embeddingsModels;
  } catch (err) {
    logger.error(`Error loading Ollama embeddings model: ${err}`);
    return {};
  }
};
