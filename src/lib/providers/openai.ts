import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai';
import { ChatDeepSeek } from "@langchain/deepseek";
import { 
  getOpenaiApiKey, 
  getCustomBaseURL, 
  getCustomSummarizerModel, 
  getCustomResumeModel,
  getCustomChatModel,
  getCustomEmbeddingModel
} from '../../config';
import logger from '../../utils/logger';

// Volces模型配置
const volcesModels = {
  key: "b3b9fe12-0df6-4388-bf13-eeac00f2c06a",
  baseURL: "https://ark.cn-beijing.volces.com/api/v3",
  modelMap: {
    'deepseek-r1': 'ep-20250218163446-2xlhg',
    'deepseek-v3': 'ep-20250220100133-g4pj5'
  }
};

// 控制是否使用Volces模型
const useVolcesModels = true;

// 推理模型列表，使用ChatDeepSeek创建对象
const inferenceModels = ['deepseek-r1', 'qwen3-235b-a22b'];

// 核心模型创建函数
const createModelInstance = (options: {
  apiKey: string;
  modelName: string;
  baseURL: string;
  temperature?: number;
  streaming?: boolean;
}) => {
  const { apiKey, modelName, baseURL, temperature, streaming } = options;
  
  if (useVolcesModels && volcesModels.modelMap[modelName]) {
    logger.info(`Using Volces model: ${volcesModels.modelMap[modelName]}`);
    
    const volcesModelName = volcesModels.modelMap[modelName];
    if (inferenceModels.includes(modelName)) {
      return new ChatDeepSeek({
        apiKey: volcesModels.key,
        model: volcesModelName,
        temperature,
        streaming,
        configuration: {
          baseURL: volcesModels.baseURL,
        }
      });
    }
    return new ChatOpenAI({
      openAIApiKey: volcesModels.key,
      modelName: volcesModelName,
      temperature,
      streaming,
      configuration: {
        baseURL: volcesModels.baseURL,
      }
    });
  }

  if (inferenceModels.includes(modelName)) {
    return new ChatDeepSeek({
      apiKey,
      model: modelName,
      temperature,
      streaming,
      configuration: {
        baseURL,
      }
    });
  }

  return new ChatOpenAI({
    openAIApiKey: apiKey,
    modelName,
    temperature,
    streaming,
    configuration: {
      baseURL,
    }
  });
};

export const loadChatModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomChatModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    return createModelInstance({
      apiKey: openAIApiKey,
      modelName,
      baseURL,
      temperature: 0.7
    });
  } catch (err) {
    logger.error(`Error loading chat model: ${err}`);
    return null;
  }
};

export const loadEmbeddingsModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomEmbeddingModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    return new OpenAIEmbeddings({
      openAIApiKey,
      modelName,
      configuration: {
        baseURL,
      }
    });
  } catch (err) {
    logger.error(`Error loading embeddings model: ${err}`);
    return null;
  }
};

export const loadSummarizerModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomSummarizerModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    return createModelInstance({
      apiKey: openAIApiKey,
      modelName,
      baseURL,
      temperature: 0
    });
  } catch (err) {
    logger.error(`Error loading summarizer model: ${err}`);
    return null;
  }
};

export const loadResumeModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomResumeModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    return createModelInstance({
      apiKey: openAIApiKey,
      modelName,
      baseURL,
      temperature: 0.7,
      streaming: false
    });
  } catch (err) {
    logger.error(`Error loading resume model: ${err}`);
    return null;
  }
};
