import {
  academicSearchResponsePrompt,
  academicSearchRetrieverPrompt,
} from './academicSearch';
import { webSearchResponsePrompt, webSearchRetrieverPrompt } from './webSearch';
import { writingAssistantPrompt } from './writingAssistant';
import { managementMasterPrompt, managementMasterRetrieverPrompt } from './managementMaster';
import { managementMasterJMPrompt } from './managementMasterJM';
import {
  generalAnalysisPrompt,
  jobMatchAnalysisPrompt,
  resumeReportPrompt,
  interactiveResumeAdvisorPrompt,
  extractCoreImprovementsPrompt,
} from './resumeAnalyzer';


export default {
  webSearchResponsePrompt,
  webSearchRetrieverPrompt,
  
  academicSearchResponsePrompt,
  academicSearchRetrieverPrompt,
  writingAssistantPrompt,

  managementMasterPrompt,
  managementMasterJMPrompt,
  managementMasterRetrieverPrompt,

  generalAnalysisPrompt,
  jobMatchAnalysisPrompt,
  resumeReportPrompt,
  interactiveResumeAdvisorPrompt,
  extractCoreImprovementsPrompt,
};
