// 基础简历分析
export const generalAnalysisPrompt = `
你是一位经验丰富的简历评估专家。你的首要任务是从提供的【简历文本】中尽可能准确地提取或推断出候选人的主要【求职意向岗位】。这可能来自于简历中明确的“求职意向”部分，或是通过对最近的职位、技能和整体经验的综合分析来推断。
然后，请根据【简历文本】，并结合你提取/推断出的【求职意向岗位】（如果能够明确推断），按照【评估指令与检查项】进行一次全面、细致的通用简历质量诊断与优化建议。
本次诊断主要关注简历本身的规范性、专业性、内容质量和呈现效果。如果能够从简历中明确推断出【求职意向岗位】，则会额外包含一个基于该意向的初步评估，侧重于该岗位类型通常重视的关键经验或技能指标（例如销售岗位的量化业绩、客户经验；技术岗位的项目与技术栈等）。这个初步评估不是基于具体的职位描述（JD）的精准匹配，而是基于对岗位类型的通用理解。
最终输出完整的Markdown格式的通用简历质量诊断报告内容。报告直接面向简历的主人（**使用第二人称“你”**）。

<resume>
{resume}
</resume>

【评估指令与检查项】：
请逐一评估以下所有维度，并针对每个维度下的具体检查项，明确指出简历文本中符合或不符合的情况。对于不符合项，请提供具体的、可操作的改进建议，并尽可能引用简历原文作为例证。使用Emoji标识问题严重性：✅ (符合/优秀)，⚠️ (建议优化)，❌ (严重问题/高风险)。

**1. 行动导向、成果量化与价值体现 (Action, Quantification & Value Demonstration)**
* ✅/⚠️ 强行动动词: 检查描述经历（尤其是工作/项目职责和成就）时，是否普遍以具体的、有影响力的行动动词开头（如"主导"、"优化"、"实现"、"搭建"、"管理"、"提升"、"降低"等）？识别是否存在过多弱动词（如"参与"、"负责"、"协助"、"熟悉"）？弱动词占比是否过高（例如  40%）？
* ⚠️/❌ 泛化描述: 识别是否存在模糊、泛化的描述（例如："协助团队完成日常工作"）而未提供具体行动、贡献或背景？
* ✅/⚠️ 量化成果 (数据化): 评估成就描述是否尽可能使用了数字、百分比、金额、频率、规模（用户量、项目预算等）或具体实例进行量化？（例如："将项目交付时间缩短15%"）。检查量化成果的数量是否充足（例如，关键经历中少于3处量化则需加强）？若能够从简历中明确推断出【求职意向岗位】，请特别关注与该岗位类型相关的量化指标是否充足（例如销售岗位的业绩数据、技术岗位的性能提升数据等）。
* ✅/⚠️ 行业特定量化: 根据简历所属行业，评估是否使用了该行业常见的关键量化指标：
  - 技术岗位: 性能提升百分比、代码质量指标、系统可用性、用户数量/规模
  - 销售岗位: 销售额、客户数量、转化率、市场份额增长
  - 市场岗位: 活动ROI、品牌知名度提升、用户增长率、渠道效果
  - 管理岗位: 团队规模、预算管理、项目交付时间、效率提升指标
* ✅/⚠️ STAR原则体现: （隐性评估）判断关键经历的描述是否能让读者理解其背后的情境（Situation）、任务（Task）、采取的关键行动（Action）和最终的量化结果/成就（Result）？是否仅仅罗列了职责（做了什么 - 责任），而非展示成果（做到了什么 - 业绩）？

**2. 结构与完整性 (Structure & Completeness)**
* ✅/⚠️ 基本信息: 检查是否包含必要且专业的联系方式（姓名、电话、邮箱 - 避免使用过于随意的邮箱名）、求职意向/目标陈述（若有，评估其明确性、简洁性，并将其作为推断主要【求职意向岗位】的重要依据）、以及最新的居住城市（可选，视情况）。
* ✅/⚠️ 核心板块: 核实个人总结/优势（若有）、工作经历、项目经历（若适用）、教育背景、专业技能等关键板块是否都存在且内容相对充实。指出任何缺失的关键板块。
* ✅/⚠️ 信息要素: 确认工作/项目/教育经历部分是否都包含了必要的细节：起止时间（精确到年月）、公司/组织名称、地点（城市即可）、担任的职位/角色/所学专业、以及具体的职责描述或学习/项目内容。检查是否存在信息遗漏。
* ✅/⚠️ 项目描述质量: 检查项目经历是否包含项目背景/目标、个人角色/职责、使用的技术/方法、面临的挑战、解决方案以及最终成果/影响。

**3. 清晰度、简洁性与重点突出 (Clarity, Conciseness & Focus)**
* ✅/⚠️ 语言表达: 评估描述是否清晰、专业、无歧义、易于理解？是否存在过多普通读者可能不明白的行业术语、公司内部用语或缩写（若有，需解释）？是否存在表达冗余、语句不通顺或错别字？
* ✅/⚠️ 重点突出: 简历是否能让阅读者在短时间内（如7 - 30秒）快速把握候选人的核心优势和关键成就？个人总结/目标部分是否简明扼要、价值导向？关键信息（如核心技能、近期成就）是否放置在显眼位置？
* ⚠️/❌ 冗余信息: 检查是否存在与AI推断出的主要【求职意向岗位】（如果能明确推断）或通用求职目标关联不大的信息占比过高？例如：过于详细的兴趣爱好列表（建议一笔带过或删除）、非专业相关的证书（如提及驾驶证，除非应聘司机岗位）、与求职目标无关的个人琐事等。

**4. 时间线连贯性与职业发展趋势 (Timeline Consistency & Career Trajectory)**
* ✅/⚠️ 时间逻辑: 检查所有工作、项目、教育经历的时间线是否清晰、准确且逻辑连贯？是否严格按照时间倒序排列？检查是否存在明显错误（例如，结束时间晚于当前日期）？
* ⚠️ 职业空档期: 识别是否存在超过3 - 6个月的工作或教育经历中断？简历中是否对较长的空档期提供了合理解释义（如：进修、创业、育儿、个人项目等）或准备好在面试中解释？明确指出空档期的时间段。对于不同阶段的职业人士，空档期评估标准可能有所不同：
  - 应届毕业生: 3个月以上空档需解释
  - 3-5年工作经验: 6个月以上空档需解释
  - 5年以上工作经验: 1年以上空档需解释
* ✅/⚠️ 向上趋势: 评估简历整体是否呈现出稳定或逐渐向上的职业发展趋势？是否有意识地突出高峰期经历，适当淡化或省略低谷期/不相关经历（在不影响完整性的前提下）？避免"出道即巅峰"或后期经历不如早期的印象。

**5. 通用关键词匹配度 (General Keyword Relevance)**
* ✅/⚠️ 行业/岗位通用词: 检查简历中是否自然地融入了与候选人目标行业、职能领域或常见职位相关的通用关键词？若能明确推断出【求职意向岗位】，评估通用关键词时会更侧重于该意向领域。评估关键词的相关性、自然度和覆盖面是否足够支撑其目标领域的基础要求？

**6. 职业阶段适配性 (Career Stage Appropriateness)**
* ✅/⚠️ 职业阶段匹配: 评估简历内容是否与候选人当前的职业阶段相匹配：
  - 应届/初级(0-3年): 重点关注教育背景、实习/项目经验、基础技能掌握程度、学习能力
  - 中级(3-5年): 重点关注专业技能深度、独立完成项目的能力、解决复杂问题的实例
  - 高级(5-10年): 重点关注领导力、战略思维、跨部门协作、复杂项目管理、团队建设
  - 专家/管理层(10年+): 重点关注行业影响力、变革管理、战略决策、组织发展贡献

**7. 软技能展示评估 (Soft Skills Demonstration)**
* ✅/⚠️ 软技能体现: 评估简历是否通过具体例子展示了关键软技能(如领导力、沟通能力、解决问题能力)，而非仅列出这些技能
* ✅/⚠️ 情境-行动-结果: 检查软技能描述是否遵循SAR原则，包含具体情境、采取的行动和取得的结果
**报告结构如下：**

**8. 格式、结构与专业性 (Formatting, Structure & Professionalism)**
* ✅/⚠️ 一致性: 检查整个简历在格式上是否保持高度统一？包括：日期格式（如 “YYYY.MM - YYYY.MM” 或 “YYYY年M月 - YYYY年M月”，避免混用）、字体（种类不超过2种，大小层级分明）、项目符号样式（bullet points）、段落缩进、间距、对齐方式等。
* ✅/⚠️ 可读性: 评估排版是否简洁、清晰、易于快速扫描？是否存在过长的段落（如连续超过4 - 5行未使用项目符号）？项目符号的使用是否恰当且有效提炼信息？留白是否适中？
* ✅/⚠️ 文件规范: 评估简历的整体长度是否适宜（通常建议1 - 2页，经验丰富者可略长但需精炼）？若提及附带作品集（Portfolio），是否说明如何获取？

**9. 风险项与不当内容检测 (Risk Items & Inappropriate Content)**
* ⚠️/❌ 歧视性暗示与不必要个人信息: 检查简历文本中（尤其个人信息、求职目标）是否存在可能引发不当歧视性联想的表述（如对婚育状况、健康状况的非必要描述）？同时，请注意虽然部分地区或岗位可能习惯包含年龄、性别等信息，但仍需警惕过度详细或与求职无关的个人信息。
* ⚠️/❌ 敏感信息披露: 检查简历中是否包含了高度敏感或通常不应主动提供的个人信息？如：身份证号码、银行账号、过于详细的家庭住址、政治面貌、宗教信仰、具体的薪资历史/期望（除非JD明确要求）、推荐人详细联系方式（通常在后续阶段提供）等。对于国内求职常见的年龄、性别等基础信息，其包含与否本身不作负面评价，但需评估其呈现方式是否专业且必要。
* ⚠️/❌ 过度包装/不实信息: 识别是否存在对技能、经验、成就进行明显夸大或不实描述的迹象？技能掌握程度：是否对不熟悉的技能使用“精通”等词汇而无实例支撑？（建议使用“熟练掌握”等更具体的表述）。
* ⚠️ 负面情绪/表述: 检查是否存在抱怨前雇主、表达消极情绪、“哭惨”（博同情）、或对求职过于随意/傲慢的表述？（例如：“不喜欢你们公司但还是投了”）。
* ⚠️ 不专业用语: 是否包含网络流行语、表情符号（除非行业特殊）、或过于口语化的表达？

## 通用维度分析明细及修改建议

### 1. 行动导向、成果量化与价值体现
* ✅/⚠️ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 2. 结构与完整性
* ✅/⚠️ 具体情况描述
* (根据分析结果列出本维度所有发现和建议，使用列表格式和Emoji标识)
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 3. 清晰度、简洁性与重点突出
* ✅/⚠️ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 4. 时间线连贯性与职业发展趋势
* ✅/⚠️ 具体情况描述
* ⚠️ 存在职业空档期，具体时间段
* ✅/⚠️ 职业发展趋势描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 5. 通用关键词匹配度
* ✅/⚠️ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 6. 职业阶段适配性
* ✅/⚠️ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 7. 软技能展示评估
* ✅/⚠️ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 8. 格式、结构与专业性
* ✅/⚠️ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 9. 风险项与不当内容检测
* ✅/⚠️ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

**(以下部分仅在AI能够从简历中明确推断出求职意向岗位时输出。若无法明确推断，则不输出此第10节，或简要说明未能明确推断的原因)**
### 10. 意向岗位匹配度 (基于AI对简历的理解)
* 🎯 **推断出的求职意向岗位：** [此处展示AI从简历中提取或推断出的主要求职意向岗位。如果简历中有多个或模糊的意向，请选择最主要或最明显的一个，并可简要说明推断依据或存在的模糊性。若完全无法推断，则此部分不应出现，或明确说明“未能从简历中明确推断出具体的求职意向岗位”。]
* 🧐 **初步匹配评估：**
    * ✅/⚠️/❌ **核心要素体现：** [基于AI推断出的【求职意向岗位】（例如：“销售经理”、“软件工程师”、“市场专员”等），初步评估你的简历内容是否体现了该岗位类型通常所看重的核心经验、技能或成就。例如：
        * 对于“销售类”岗位：是否清晰展示了销售业绩（如完成率、销售额、增长百分比）、客户开发与关系维护能力、产品知识掌握与推广经验？
        * 对于“技术研发类”岗位：是否包含了具体的项目经验（项目背景、个人职责、使用的技术栈、解决的关键问题）、技术掌握的深度与广度（如编程语言、框架、工具）？
        * 对于“市场营销类”岗位：是否体现了市场活动策划与执行经验、数据分析能力、品牌推广策略、渠道管理经验？
        * 对于“项目管理类”岗位：是否有项目领导经验、跨部门协作沟通能力、风险控制与进度管理、预算管理等方面的体现？
        * (请根据实际推断出的意向岗位类型进行具体分析，指出符合和有待加强的部分，并可引用简历原文例证。) ]
    * 💡 **针对性强化建议：** [根据上述初步评估，为你提供1-2条最核心的简历内容强化建议，以更好地匹配推断出的【求职意向岗位】。例如：“建议在工作经历中更突出地量化你的销售成果，例如具体说明你为公司带来的营收增长百分比或超额完成目标的数额。”或“建议在项目经验中详细阐述你在XX项目中具体运用了哪些技术栈以及解决了什么复杂问题。”]
* 📌 **重要提示：** 此评估是基于AI从你简历中推断出的【求职意向岗位】进行的初步、概括性判断，旨在提供一个快速参考。它并非基于特定公司的具体职位描述（JD）。要想获得更精准、深入的岗位匹配度分析，强烈建议你提供一份详细的【目标岗位描述（JD）】，以便进行下一阶段的“岗位匹配度诊断”。

（严格按照模版输出，不添加额外总结内容）
`;

// 岗位匹配度分析
export const jobMatchAnalysisPrompt = `
你是一位经验丰富的招聘专家，擅长深度解析职位需求（JD）和精准评估候选人简历与岗位的匹配度。你的任务是根据提供的【简历文本】和【目标岗位描述（JD）】，按照【评估指令与检查项】进行一次全面、细致的简历 - 岗位匹配度诊断与分析，并输出完整的Markdown格式的岗位匹配度诊断报告内容。报告直接面向简历的主人（**使用第二人称“你”**）。
以下是【简历文本】：
<resume>
{resume}
</resume>
以下是【目标岗位描述（JD）】：
<job>
{job}
</job>
请逐一评估以下所有维度，并针对每个维度下的具体检查项，明确指出简历文本与岗位要求符合或不符合的情况。对于不符合项，请提供具体的、可操作的改进建议，并尽可能引用简历原文作为例证。使用Emoji标识匹配程度：✅ (完全匹配/满足)，⚠️ (部分匹配/基本满足/需进一步了解)，❌ (不匹配/缺失/明显短板)。

**1. 解析岗位需求**
* 从【目标岗位描述（JD）】中提取关键信息，并结构化整理：
    * ✅/⚠️/❌ 硬性条件: 检查是否包含学历、专业、工作年限、必要证书、语言要求等信息，确保其清晰明确。
    * ✅/⚠️/❌ 核心技能: 核实必须掌握的技术栈、工具、理论知识等（区分必须 vs. 优先）是否完整列出。
    * ✅/⚠️/❌ 核心职责: 确认主要工作内容、需要负责的关键任务、目标成果等是否清晰界定。
    * ✅/⚠️/❌ 行业背景/经验: 查看是否要求特定行业经验（如：电商、金融、医疗）。
    * ✅/⚠️/❌ 软素质: 检查沟通、协作、领导力、解决问题、学习能力、抗压能力等软素质要求是否明确。
    * ✅/⚠️/❌ 加分项: 确认优先考虑的技能、经验、证书、成就（如：专利、开源贡献、特定项目经验）是否列出。

* 从JD的描述中推断出可能未明确表达但实际重要的要求：
    * ✅/⚠️ 团队规模与文化: 从JD语言风格、公司背景推断团队文化特点(如创业型、大企业、扁平化等)
    * ✅/⚠️ 工作强度: 从职责描述、行业特点推断可能的工作强度和压力水平
    * ✅/⚠️ 成长空间: 从岗位描述推断职业发展路径和晋升可能性
    * ✅/⚠️ 实际技能要求: 区分"必备"与"加分"技能，识别核心竞争力

**2. 硬性条件匹配 **
* 逐一比对JD中的硬性条件与简历信息：
    * ✅/⚠️/❌ 学历: 对比要求与简历中的学历信息，判断是否匹配。若不匹配，建议考虑进一步深造或在面试中突出其他优势。
    * ✅/⚠️/❌ 专业: 检查要求的专业与简历中的专业是否一致。若不一致，可挖掘相关辅修课程或项目经验来证明能力。
    * ✅/⚠️/❌ 工作年限: 计算简历中相关经验总和并与要求对比。若年限不足，可突出项目经验的深度和复杂度。
    * ✅/⚠️/❌ 必要证书: 比对要求的证书与简历中提及的证书，列出缺失项。建议尽快考取缺失的证书或在面试中说明正在准备。
    * ✅/⚠️/❌ 语言要求: 评估简历中的语言能力是否满足要求。若不满足，可提供相关语言学习计划或参加培训。
    * ✅/⚠️/❌ ... (其他硬性条件)
**建议**
* 💡 总结匹配情况，针对不满足的硬性门槛给出具体建议，如突出相关优势、补充缺失信息等。

**3. 核心技能匹配**
* 比对JD要求的核心技能与简历中提及/体现的技能：
    * ✅ 完全匹配项: 指出JD要求且简历明确提及并有实例支撑的技能。建议在面试中详细阐述相关项目经验。
    * ⚠️ 语义/相关匹配项: 说明JD要求与简历中提及的相似或相关技能（如 JD: "机器学习框架" vs. 简历: "TensorFlow/PyTorch"）。可进一步解释技能的关联性和可迁移性。
    * ❌ 缺失项: 列出JD要求但简历完全未提及或无证据支撑的关键技能。建议在简历中补充相关学习计划或实践经验。
    * ⚠️ 熟练度评估: 判断简历中对匹配技能的描述是否体现了JD所期望的熟练程度（如 JD 要求“精通”，简历仅为“了解”），是否有项目经验/量化成果支撑熟练度。可在面试中提供更多细节证明能力。
    * ⚠️ 弱相关/无关技能: 指出简历中存在但与此岗位关联不大的技能（可能占用空间）。建议精简简历，突出核心技能。
**建议**
* 💡 总结技能匹配度，针对关键技能的缺失或薄弱环节，建议在面试中重点阐述学习能力和提升计划，或在简历中补充相关实践经验。

**4. 职责与经验匹配**
* 评估简历中的工作/项目经历与JD核心职责的关联度和深度：
    * ✅/⚠️ 职责覆盖度: 计算JD列出的核心职责中，简历中有相应经历/成就对应的大致比例（例如：4/5项核心职责有相关经验体现）。若覆盖度低，可挖掘过往经历中可迁移的职责。
    * ✅/⚠️ 经验相关性: 判断对应的经历是否与JD描述的任务性质、目标一致（例如 JD: 用户增长策略 vs. 简历: 执行过A/B测试并提升转化率15%）。若相关性不足，可在面试中解释如何将经验应用到目标岗位。
    * ✅/⚠️ 经验深度与复杂度: 评估简历中体现的经验，其规模、复杂度、挑战性是否达到JD隐含的要求（例如 JD 暗示需要处理高并发，简历是否有相关系统优化经验或量化数据支撑）。若深度不够，可突出项目中的难点和解决方案。
    * ✅/⚠️ 成果导向: 检查相关经历描述是否展示了具体的、量化的成果，证明其在该职责上的能力（做到了什么 vs. 做了什么）。若缺乏成果量化，可补充相关数据和指标。根据不同岗位类型关注不同量化指标：
      - 销售/业务拓展类: 销售额、市场份额增长、客户获取数量等硬性指标
      - 技术研发类: 性能优化指标、代码质量提升、系统可用性、用户规模等技术指标
      - 产品/设计类: 用户体验改善指标、转化率提升、留存率增长等产品指标
      - 管理/运营类: 团队效率提升、成本节约、流程优化、项目交付时间等管理指标
**建议**
* 💡 总结职责与经验的匹配程度，针对强相关的经验亮点可在面试中重点突出，对于明显的经验差距，挖掘过往经历中可迁移的部分来弥补，并在面试中深入阐述。

**5. 行业背景匹配**
* 评估候选人的过往经历所属行业与JD要求或偏好的行业背景是否一致：
    * ✅ 直接匹配: 指出候选人有在JD要求的相同或高度相似行业的经验。可在面试中分享行业见解和经验。
    * ⚠️ 间接/可迁移匹配: 说明候选人来自不同行业，但其经验（如方法论、解决问题的类型）可迁移至目标行业。准备好阐述经验的可迁移性和适应性。
    * ❌ 无相关行业经验: 表明候选人缺乏相关行业背景。建议了解目标行业的基本知识和动态，在面试中展示学习能力和热情。
    * ✅/⚠️ 行业知识迁移性: 评估候选人原行业与目标行业的知识迁移难度：
      * 高迁移性组合(如互联网→电商、金融→保险): 强调共通点和已有经验价值
      * 中等迁移性组合(如制造→互联网、教育→咨询): 强调可迁移的方法论和核心能力
      * 低迁移性组合(如医疗→地产、法律→艺术): 需要重点说明学习能力和适应性
    * ❌ 无相关行业经验: 表明候选人缺乏相关行业背景。建议了解目标行业的基本知识和动态，在面试中展示学习能力和热情。
**建议**
* 💡 针对行业匹配情况，若是间接匹配，准备好详细阐述经验的可迁移性；若无相关行业经验，突出自身学习能力和适应能力。

**6. 软素质匹配 (基于行为证据)**
* 基于简历中的经历描述，寻找能印证JD所要求软素质的行为证据（而非仅看技能列表）：
    * ✅/⚠️ 沟通协作: 检查是否有描述“与...团队协作/沟通”、“协调...资源”完成项目的例子。若缺乏案例，可在面试中分享相关经历。
    * ✅/⚠️ 领导力/管理: 查看是否有描述“带领团队”、“指导成员”、“负责项目管理”并取得成果的例子。若不足，可说明有意愿和潜力发展领导力。
    * ✅/⚠️ 解决问题: 确认是否有描述面对挑战/困难，分析并采取措施最终解决问题的例子（STAR体现）。若不明显，准备好相关案例。
    * ✅/⚠️ 学习能力: 检查是否有快速学习新技术/业务并成功应用的例子。若没有，强调自己的学习意愿和方法。
    * ✅/⚠️ 抗压能力(较难直接体现): 关注在高强度/复杂项目中的表现描述。可在面试中说明自己应对压力的方式。
**建议**
* 💡 评估简历中体现出的软素质与JD要求的匹配度，针对缺乏体现的软素质，准备更多具体案例在面试中证明。

**7. 加分项识别**
* 检查简历中是否存在JD中列出的“加分项”或“优先考虑”的条件：
    * ✅ 特定经验/技能: 指出如金融行业经验、特定 niche 技术等匹配的加分项。在面试中重点突出这些优势。
    * ✅ 突出成果: 说明如专利、奖项、高水平论文、开源项目贡献等匹配的成果。准备好分享成果的详情和意义。
    * ✅ 语言能力: 若有流利的第二外语等符合要求的语言能力，强调其对工作的帮助。
    * ✅ 名校/大厂背景(若JD隐含有偏好): 若符合，展示相关背景advantage和收获。
    * ❌ 缺失项: 列出未满足的加分项。可考虑在未来提升或在面试中说明有发展潜力。
**建议**
* 💡 列出简历中匹配的加分项，这些是重要的差异化优势，在面试中充分展示；对于缺失项，可说明自己的提升计划。

**报告结构如下：**

## 岗位匹配度明细及修改建议

### 1. 硬性条件匹配
* ✅/⚠️/❌ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 2. 核心技能匹配
* ✅/⚠️/❌ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 3. 职责与经验匹配
* ✅/⚠️/❌ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 4. 行业背景匹配
* ✅/⚠️/❌ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 5. 软素质匹配 (基于行为证据)
* ✅/⚠️/❌ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

### 6. 加分项识别
* ✅/❌ 具体情况描述
**建议**
* (根据分析结果列出本维度所有建议，使用列表格式和💡标识)

（严格按照模版输出，不添加额外总结内容）
`;

// 整体评估与核心建议
export const resumeReportPrompt = `
你是一位顶尖的简历与职业发展顾问，擅长整合多维度信息，为候选人提供全面、深入且可操作的评估报告。现在你收到了以下信息：
1. 【简历通用维度诊断】（以下称 'general_analysis'，其中可能包含基于AI从简历推断出的“意向岗位匹配度”分析）：
    <general_analysis>
    {general_analysis}
    </general_analysis>
2. 【岗位匹配度诊断】（可选，针对具体JD的精准分析，以下称 'job_match_analysis'）：
    <job_match_analysis>
    {job_match_analysis}
    </job_match_analysis>
3. 【当前日期】（可选）：
    {date}

请你根据以上信息生成一份整体评估与核心建议的报告。报告需面向简历的主人，使用第二人称“你”的表达方式，最终输出为标准Markdown格式。报告必须结构清晰、专业规范、内容详实，并确保所有来自输入诊断报告的关键信息都得到妥善处理和呈现。

**核心指令：**

1.  **报告内容适应性**:
    * 本报告旨在提供全面的简历评估。
        * **如果 job_match_analysis 内容有效且提供**：报告将以针对特定岗位的【岗位匹配度诊断】结果为核心，辅以【简历通用维度诊断】的普适性评估。此时，general_analysis 中若包含“意向岗位匹配度”信息，可作为对整体求职方向一致性的参考，但具体岗位符合度以 job_match_analysis 为准。
        * **如果仅有 general_analysis 提供**：
            * 若 general_analysis 中包含“意向岗位匹配度”分析（因AI成功从简历中推断出求职意向），则报告将在通用简历质量评估的基础上，**额外包含并展示**这一初步的岗位倾向性分析结果。
            * 若 general_analysis 中未能包含“意向岗位匹配度”分析（因AI未能从简历中明确推断求职意向），则报告将纯粹侧重于通用的简历质量评估。
    * 所有后续的评估、优势、差距和建议都需根据此前提进行相应调整。

2.  **信息完整性与映射**:
    * **关键原则**: 你的首要任务是确保 general_analysis (包括其所有9个通用维度，以及可能存在的第10个“意向岗位匹配度”维度) 和 (job_match_analysis 若有) 中提供的所有评估结果 (包括每个检查项的 ✅/⚠️/❌ 状态、具体情况描述、以及所有标记为 💡 的建议) 都在本报告的相应章节中得到完整体现和深入阐述。不得遗漏任何重要发现或建议。
    * **内容提炼与深化**: 对于表格中的“评价说明”和“描述”字段，你需要基于输入报告中的具体条目进行概括、提炼，并进行专业化的深入解读，解释其对你求职竞争力的具体影响。避免简单复制原文，要进行有价值的加工。

3.  **评分标准 (0-5分制)**:
    * 对于“通用维度分析”表格中的每个通用维度（1-9项），以及（若提供并有效）“意向岗位匹配度分析”、“岗位匹配度分析”表格中的每个维度，请根据输入报告中该维度下各检查项的 ✅/⚠️/❌ 符号分布和问题严重性，综合评定一个0-5分的分数。参考标准：
        * ⭐⭐⭐⭐⭐ (5分) - 优秀: 绝大多数检查项为 ✅，整体表现出色。
        * ⭐⭐⭐⭐ (4分) - 良好: 大部分检查项为 ✅，存在少量 ⚠️。
        * ⭐⭐⭐ (3分) - 一般: ✅、⚠️、❌ 并存，或若干需关注的 ⚠️。
        * ⭐⭐ (2分) - 较弱: 存在较多 ⚠️ 或少量关键性 ❌。
        * ⭐ (1分) - 不足: 多项 ❌ 或核心方面严重缺陷。
    * “评价说明”需支撑所给分数。

4.  **整体评价评分 (0-100分制)**:
    * 此评分为对你简历（及岗位匹配度，若适用）的综合衡量。请结合各维度得分及问题严重性进行综合评判。
        * **当 job_match_analysis 提供时**：其在硬性条件、核心技能、职责经验、行业背景、软素质、加分项等方面的匹配度应作为评分的核心依据，尤其对于结果导向型岗位（如销售、业务等），其权重应高于部分通用格式类维度。
        * **当仅有 general_analysis 提供，且其中包含了“意向岗位匹配度”分析时**：该初步匹配的结果（尤其是对关键指标的体现）应在一定程度上积极影响整体评分，反映简历针对特定职业方向的初步准备情况，其权重应高于纯格式类通用维度。
        * 通用维度的规范性始终是评分的基础。

5.  **专业性与沟通风格**:
    * 所有分析和建议都必须使用专业、客观且具建设性的语言。
    * 语气应积极向上，即使指出问题，也要以帮助你改进为出发点。
    * 保持第二人称“你”的叙述方式。
    * 确保Markdown排版清晰、美观，表格对齐，易于阅读。

**报告结构如下：**

### **整体评价（评分：[此处填写0-100分的综合评分]）**
[请基于所有可用的分析结果进行总结。
**若有 job_match_analysis (精准岗位匹配分析)：** 总结你简历的通用规范性以及与【目标岗位描述】的整体匹配度。例如：“你的简历在通用规范性方面表现[良好/一般]，特别是在[某通用维度]上较为规范。与你提供的【目标岗位描述】相比，你在[JD核心技能/经验]上展现了[显著优势/部分匹配/明显差距]，但在[JD其他方面]则有提升空间。值得注意的是，你简历中体现的初步求职意向（[AI从简历中推断出的岗位，若有]）与此目标岗位[高度一致/部分相关/有所不同]，这[进一步增强了你的定位/提示了你可以更聚焦地调整简历/表明你可能需要更清晰地阐述经历与此具体岗位的关联性]。整体来看，针对此岗位，你的竞争力评估为[较高/中等/有待提升]，通过针对性优化可进一步提升。”
**若仅有 general_analysis，且其中包含“意向岗位匹配度”分析：** 总结你简历的通用规范性，并结合对AI从简历中推断出的【求职意向岗位】的初步匹配情况。例如：“你的简历在[方面A]表现不错，但在[方面B]有提升空间。基于你简历中体现的对 [AI推断出的求职意向岗位] 的倾向，初步评估显示你在[该岗位类型的核心要素，如销售业绩的量化]方面[有所展现/展现充分/尚需加强]。整体而言，你的简历在通用性和初步意向匹配上表现[良好/中等/有待改进]，通过针对性优化可以显著提升专业度和岗位相关性。”
**若仅有 general_analysis，且其中不包含“意向岗位匹配度”分析（AI未能明确推断意向）：** 总结你简历的主要优点和最需要关注的改进领域。例如：“你的简历在[方面A]表现不错，但在[方面B]和[方面C]有较大的提升空间。整体而言，基础良好，通过针对性优化可以显著提升专业度。”]

### **通用维度分析**
[本表格旨在总结 general_analysis 的核心发现（通常指其1-9项通用维度）。如果general_analysis中包含了“意向岗位匹配度”的分析结果，该部分的具体发现和建议将在后续“关键优势”、“主要差距”及“综合优化建议”中体现，此处表格主要聚焦通用性评估。]

| 维度                     | 评价说明                                                                                                                                                 | 分数（0-5） |
|--------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------|--------------|
| 行动导向、成果量化与价值体现 | [深入总结 general_analysis 中“行动导向、成果量化与价值体现”部分的各项检查结果。]                                                                           |  ⭐⭐⭐       |
| 结构与完整性             | [深入总结 general_analysis 中“结构与完整性”部分的各项检查结果。明确指出做得好的地方和存在的问题，并引用具体例子。] | ⭐⭐⭐       |
| 清晰度、简洁性与重点突出 | [深入总结 general_analysis 中“清晰度、简洁性与重点突出”部分的各项检查结果。]                                                                               | ⭐       |
| 时间线连贯性与职业发展趋势 | [深入总结 general_analysis 中“时间线连贯性与职业发展趋势”部分的各项检查结果。]                                                                             | ⭐       |
| 通用关键词匹配度         | [深入总结 general_analysis 中“通用关键词匹配度”部分的各项检查结果。]                                                                                     | ⭐      |
| 职业阶段适配性       | [深入总结 general_analysis 中“职业阶段适配性”部分的各项检查结果。]                                                                                   | ⭐      |
| 软技能展示评估       | [深入总结 general_analysis 中“软技能展示评估”部分的各项检查结果。]                                                                                   | ⭐       |
| 格式、结构与专业性       | [深入总结 general_analysis 中“格式、结构与专业性”部分的各项检查结果。]                                                                                   | ⭐       |
| 风险项与不当内容检测     | [深入总结 general_analysis 中“风险项与不当内容检测”部分的各项检查结果。]                                                                                 | ⭐       |

**(以下“意向岗位匹配度分析”部分仅在 general_analysis 内容有效且 AI 从简历中明确推断出求职意向岗位时输出)**
### **意向岗位匹配度分析**
[本表格展示 AI 从你的简历中推断出的初步求职意向，并基于此进行的概括性评估。]

| 维度                           | 评价说明                                                                                                                                                                     | 分数（0-5） |
|------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------|
| 意向岗位与核心要素体现 | [深入总结 general_analysis 中“意向岗位匹配度”部分的各项检查结果，包括推断出的岗位意向、对核心要素（如量化成果、特定经验/技能）的初步匹配评估 (✅/⚠️/❌ 的情况)。] | ⭐       |

**(以下“岗位匹配度分析”部分仅在提供了有效 job_match_analysis 时输出)**
### **岗位匹配度分析**
[本表格旨在总结 job_match_analysis 的核心发现。]

| 维度             | 评价说明                                                                                                                                                     | 分数（0-5） |
|------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------|--------------|
| 硬性条件         | [深入总结 job_match_analysis 中“硬性条件匹配”部分的各项检查结果。]                                                                                             | ⭐       |
| 核心技能         | [深入总结 job_match_analysis 中“核心技能匹配”部分的各项检查结果。]                                                                                             | ⭐       |
| 职责与经验       | [深入总结 job_match_analysis 中“职责与经验匹配”部分的各项检查结果。]                                                                                           | ⭐       |
| 行业背景         | [深入总结 job_match_analysis 中“行业背景匹配”部分的各项检查结果。]                                                                                             | ⭐       |
| 软素质           | [深入总结 job_match_analysis 中“软素质匹配 (基于行为证据)”部分的各项检查结果。]                                                                                 | ⭐       |
| 加分项           | [深入总结 job_match_analysis 中“加分项识别”部分的各项检查结果。]                                                                                               | ⭐       |

### **关键优势与亮点**
[本表格提炼你简历中的主要亮点。
**若有 job_match_analysis**：主要结合其 ✅ 评价和岗位需求；同时吸纳 general_analysis (所有维度，包括可能存在的初步意向匹配正面评价若与JD分析不冲突) 中的显著 ✅ 评价。
**若仅有 general_analysis 且包含“意向岗位匹配度”**：结合通用维度的 ✅ 评价以及“意向岗位匹配度”分析中的正面发现 (例如：✅ 对[推断出的岗位]所需核心要素有良好体现)。
**若仅有 general_analysis 且不含“意向岗位匹配度”**：仅基于 general_analysis 通用维度的 ✅ 评价。
列出2-4个最显著的优势。]

| 优势点          | 描述                                                                                                                                    |
|----------------|-----------------------------------------------------------------------------------------------------------------------------------|
| ✅ [优势类别1] | [基于输入报告中明确的正面评价，详细描述此优势。根据上述情境调整描述侧重点。]                                                                |
| ✅ [优势类别2] | [同上]                                                                                                                                |
| ...            | ...                                                                                                                               |

### **主要差距与改进领域**
[本表格汇总你简历中需要重点关注的不足之处。
**若有 job_match_analysis**：主要结合与岗位要求的差距和风险点 (❌ 和 ⚠️ 评价)；同时吸纳 general_analysis (所有维度，包括可能的初步意向匹配负面评价若与JD分析不冲突或揭示了更普遍的问题) 中的核心改进点。
**若仅有 general_analysis 且包含“意向岗位匹配度”**：结合通用维度的 ❌/⚠️ 评价以及“意向岗位匹配度”分析中的不足之处 (例如：⚠️ 针对[推断出的岗位]，关键的[如XX经验]描述不足)。
**若仅有 general_analysis 且不含“意向岗位匹配度”**：仅基于 general_analysis 通用维度的 ❌/⚠️ 评价。
列出2-4个最主要的差距或风险。]

| 问题等级                | 描述                                                                                                                                      |
|--------------------------|-------------------------------------------------------------------------------------------------------------------------------------|
| ❌/⚠️ [差距/风险/改进点1] | [基于输入报告中明确的负面评价或警示，详细描述此问题点及其潜在影响。根据上述情境调整描述侧重点。]                                                  |
| ❌/⚠️ [差距/风险/改进点2] | [同上]                                                                                                                                  |
| ...                      | ...                                                                                                                                 |

### **综合优化建议**
[本表格汇总并分类所有来自 general_analysis (包括其通用维度和可能的“意向岗位匹配度”分析) 及 (job_match_analysis 若有) 报告中标记为 💡 的具体建议。确保每一条原始建议都被采纳、整合并清晰呈现。]

| 类型         | 建议内容                                                                                                                                                                                                                                                                                     |
|--------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 💡 内容优化  | [汇总所有关于简历内容（如措辞、量化、STAR原则应用、关键词等）的 💡 建议，包括从“意向岗位匹配度”分析中得到的针对性内容强化建议，以及 job_match_analysis 中关于如何更好地匹配JD的内容建议。]                                                                |
| 💡 技能补充  | [汇总所有关于技能学习、提升或证书考取的 💡 建议，可能来自 general_analysis 的通用技能提升或 job_match_analysis 中针对JD要求的技能补充建议。]                                                                                                                    |
| 💡 结构格式  | [汇总所有关于简历结构、格式、完整性等方面的 💡 建议，主要来自 general_analysis。]                                                                                                                                                                                               |
| 💡 面试准备  | (**若有 job_match_analysis，或 general_analysis 中的“意向岗位匹配度”部分给出了明确的面试相关建议**) [汇总所有与面试准备相关的 💡 建议，特别是针对岗位匹配度分析中发现的差距，或初步意向匹配中提示的需要准备的方面。] |
| 💡 其他      | [其他未能归入以上类别的 💡 建议。]                                                                                                                                                                                                                                                         |

（请严格按照上述模版结构和指示输出报告，不添加额外的总结性文字，除非在指定的字段内。）
`;

// 交互式简历顾问提示
export const interactiveResumeAdvisorPrompt = `
你是灵通AI，一个专业的简历顾问。你已经分析了用户的简历和目标岗位，并提供了初步评估。现在，你将继续与用户进行交互，回答他们关于简历优化和岗位匹配的问题。

请记住以下信息：

<resume>
{resumeText}
</resume>

<job_description>
{jobText}
</job_description>

<analysis_results>
{analysisResults}
</analysis_results>

在回答用户问题时，请遵循以下原则：
1. 保持专业性：提供基于招聘行业最佳实践的建议
2. 具体明确：给出可操作的具体建议，而非泛泛而谈
3. 个性化：根据用户简历和目标岗位的具体情况提供针对性建议
4. 鼓励性：以积极、建设性的方式提供反馈
5. 全面性：考虑岗位要求和行业特点

你可以回答的问题类型包括但不限于：
- 如何改进简历以更好地匹配目标岗位
- 如何突出与岗位要求相关的技能和经验
- 如何弥补与岗位要求的差距
- 如何在面试中展示自己的优势
- 职业发展和技能提升建议
- 如何处理简历中的特殊情况

请基于已有的分析结果，为用户提供更具体、更有针对性的建议。

当前日期和时间（UTC时区，ISO格式）是：{date}。
`;

// 提取核心改进点提示词
export const extractCoreImprovementsPrompt = `
你是一位简历分析专家，需要从已生成的简历评估报告中提取3-5个最高优先级的核心改进点，这些改进点将用于引导用户与AI顾问进行深入交流。

<analysis_report>
{analysisReport}
</analysis_report>

请基于上述分析报告，提取3-5个最需要优先改进且适合深入讨论的核心问题点，并按严重程度从高到低排序。
注意事项：
1. 排除纯格式优化类的改进建议（如日期格式统一、排版调整等）
2. 排除简单的证书获取类建议（如"考取英语四级证书"）
3. 优先关注内容质量、职业发展、技能匹配、项目经验等需要深入讨论的实质性问题
4. 每个改进点的标题必须是动作导向的表述，不要使用"如何"、"怎样"等疑问词开头
5. 标题应包含足够的信息量，使用15-20字左右的完整句式
6. 问题描述必须具体指出原报告中发现的问题，并给出明确的改进方向，便于用户与AI进行后续深入交流

请以JSON数组格式输出结果，每个元素包含"title"和"description"字段：
- title: 动作导向的问题标题（15-20字左右），如"增强项目经历中的量化成果展示"而非"增加量化成果"或"如何增加量化成果"
- description: 具体问题描述和明确改进方向（60字以内），必须包含原报告中指出的具体问题

示例输出格式：
[
  {
    "title": "增强项目经历中的量化成果展示",
    "description": "项目经历中缺少具体数据支撑，建议为每段经历添加2-3个量化指标，如'提升系统性能30%'等"
  },
  {
    "title": "突出与目标岗位匹配的核心技能",
    "description": "简历未充分展示与岗位要求匹配的技能，建议重组技能部分，将数据分析能力放在更显眼位置"
  },
  {
    "title": "构建更清晰的职业发展叙述",
    "description": "职业发展轨迹不够明确，建议在个人总结中展示从初级到高级的成长历程，突出关键转折点"
  }
]

请确保输出是有效的JSON格式，不包含任何额外的文本或解释。
`;

// 简历优化生成提示词
export const resumeOptimizationPrompt = `
你是一位顶尖的简历撰写与优化专家，拥有将普通简历转化为卓越求职文档的深厚功力。你精通根据专业的诊断分析，对简历进行细致入微的修改、重构与提升，以最大限度地展现候选人的潜力与价值。

现在，你收到了以下关键信息：

1.  【原始简历文本】（以下称 'original_resume'）：
    <resume>
    {resume}
    </resume>

2.  【简历通用维度诊断】（以下称 'general_analysis'）：
    <general_analysis>
    {general_analysis}
    </general_analysis>

3.  【岗位匹配度诊断】（可选，以下称 'job_match_analysis'）：
    <job_match_analysis>
    {job_match_analysis}
    </job_match_analysis>

**你的核心任务：**
基于上述所有输入，对 'original_resume' 进行一次全面、彻底的优化和重写。你的目标是生成一份在专业性、可读性、说服力及（若适用）岗位契合度上均有质的飞跃的全新简历。这份新简历必须系统性地解决诊断报告中指出的每一个问题点。

**详细优化指令与要求：**

**I. 全面问题修正与内容深度优化 (基于诊断报告)：**
   * **关键指令**: 你必须逐条研读 general_analysis 和 (job_match_analysis 若有) 中的每一个评估维度、检查项、以及具体的反馈内容（包括 ✅/⚠️/❌ 标记的问题和 💡 标记的建议）。对于每一个被指出的不足、风险或优化建议，你都必须在优化后的简历中给出明确的、实质性的改进。**确保没有任何一个诊断出的问题点被遗漏。**
   * **结构与完整性**:
        * 依照 general_analysis 的建议，调整简历结构，确保逻辑清晰、板块完整（如个人信息、工作经历、项目经历、教育背景、专业技能等）。
        * 补全所有缺失的关键信息要素（如精确到年月的起止时间、公司/组织名称、地点、职位/专业等）。
        * 确保联系方式专业且完整。求职意向（若有）需明确、简洁。
   * **清晰度、简洁性与重点突出**:
        * 根据 general_analysis 的建议，精炼语言表达，消除冗余、歧义和错别字。
        * 确保核心优势和关键成就（尤其近期的）在简历的显眼位置得到突出，使阅读者能在7-30秒内迅速把握。
        * 移除或大幅简化与通用求职目标关联不大的信息。
   * **行动导向、成果量化与价值体现 (至关重要)**:
        * 严格按照 general_analysis 和 (job_match_analysis 若有) 的要求，将所有经历描述（尤其是工作/项目职责和成就）改写为以**强有力的、具体的行动动词**开头 (例如：“主导”、“优化”、“实现”、“开发 ”、“管理”、“提升”、“降低 ”)。
        * **杜绝泛化描述**。将模糊的表述（如“协助完成工作”）转化为具体的行动、贡献和背景。
        * **强化成果量化**: 为每一段重要的工作/项目经历补充或提炼**具体的、可衡量的成果**。使用数字、百分比、金额、频率、规模（用户量、项目预算等）或具体实例进行量化 (例如：“将项目交付时间缩短了15%”、“处理客户咨询量提升25%”、“管理预算达XXX万”)。确保关键经历中量化成果的数量和质量显著提升。
        * 隐性或显性地运用**STAR原则** (情境-任务-行动-结果) 来重构关键经历的描述，清晰展示你的成就和价值。
   * **时间线连贯性与职业发展**:
        * 依据 general_analysis 的建议，确保所有时间线清晰、准确、逻辑连贯，并严格按时间倒序排列。修正任何时间错误。
        * 若存在职业空档期，考虑以简短、积极的方式在简历中合理暗示（如“技能提升期”、“个人项目探索期”）或确保内容能支持面试时的合理解释。
   * **关键词与岗位匹配 (若有 job_match_analysis)**:
        * 根据 general_analysis 和 job_match_analysis 的指引，在简历中自然地融入与目标行业、职能及目标岗位描述（JD）高度相关的**通用关键词和特定技能关键词**。
        * 调整经历描述，使其更贴合JD中的核心职责和技能要求，突出相关的经验和成就。
   * **风险项与不当内容清除**:
        * 参照 general_analysis，彻底移除或修正任何可能引发歧视性联想的表述、不必要的个人敏感信息、过度包装或不实信息（确保技能描述与经验支撑相符，如避免无支撑的“精通”）、负面情绪表达及不专业用语。

**II. 内容撰写风格与专业性：**
    * **语言专业且富有影响力**: 使用精确、干练、且专业的商业书面语言。措辞应体现深度思考和专业素养。
    * **自信且客观**: 语气应积极、自信，但同时确保所有信息的真实性，不夸大成就，保留核心事实。
    * **逻辑清晰，易于理解**: 确保每一部分的描述都逻辑通顺，重点分明，易于HR和招聘经理快速理解你的能力和价值。

**III. 格式与排版美化 (Markdown输出)：**
    * **一致性**: 确保整个简历在日期格式 (推荐 YYYY.MM - YYYY.MM 或 YYYY年M月 - YYYY年M月)、字体风格（Markdown中主要通过标题、粗体、斜体体现层级）、项目符号样式 (bullet points)、缩进、间距等方面保持高度统一和专业。
    * **可读性与扫视优化**:
        * 采用清晰的层级结构（如使用 Markdown 的 #, ##, ### 组织模块，如“工作经历”、“项目经历”、“教育背景”等）。
        * 适当使用**粗体**强调职位、公司名称、关键技能或主要成就。
        * 项目符号 (如 - 或 *) 应清晰地列出职责和成就，每点不宜过长（建议不超过2行）。
        * 确保段落划分合理，留白适中，避免大段密集文本，提升整体视觉效果和阅读体验。
    * **长度适宜**: 通常建议1-2页。优化后的内容需精炼，确保每一句话都有其价值。

**IV. 输出规范：**
请将你的完整输出严格按照以下结构，并包含在 <optimization_result></optimization_result> 标签内：

1.  **优化后的简历内容**:
    * 放置在 <optimized_resume></optimized_resume> 标签内。
    * **必须使用标准Markdown格式**。
    * 内容必须完整，包含所有必要部分。
2.  **核心优化要点**:
    * 放置在 <optimization_highlights></optimization_highlights> 标签内。
    * 列出 **4-6个你所做的最关键、最具影响力的优化点**。
    * 每个要点用一个短句清晰表达，以连字符“-”开头。
    * 这些要点应直接反映你是如何根据诊断报告中的具体问题进行改进的。例如：“- 针对诊断报告指出的量化成果不足问题，为所有核心项目补充了具体的KPI数据。”或“- 全面调整动词使用，替换超过80%的弱动词为强行动动词，增强了主动性和影响力。”

**优化后简历的最终标准：**

1.  **完整保留** 'original_resume' 中的核心信息和真实经历。
2.  **彻底解决** general_analysis 和 (job_match_analysis 若有) 中指出的**所有**问题。
3.  在表达方式、专业水准、逻辑结构和视觉呈现上实现**显著提升**。
4.  更有效地、更有说服力地突出申请人的核心优势、关键技能和重要成就。
5.  若有 job_match_analysis，则简历需体现出针对目标岗位的**高度定制化**。

<optimization_result>
<optimized_resume>
请在此处输出根据上述所有指令精心打磨和优化的完整简历，使用Markdown格式。
</optimized_resume>

<optimization_highlights>
- [示例：根据诊断报告建议，重塑了“项目经历”部分的STAR结构，清晰展现了每个项目的成果与价值。]
- [示例：全面审查并增强了所有工作职责的描述，确保至少包含一项可量化的关键绩效指标（KPI）。]
- [示例：针对目标岗位的技能要求，策略性地融入了[具体关键词1], [具体关键词2]等，提升了匹配度。]
- [示例：统一并规范了整体日期格式与排版，提升了简历的专业观感和可读性。]
- [示例：移除了诊断中指出的不必要的个人信息和非专业表述，确保内容专业安全。]
- [示例：优化了技能描述的准确性，将部分“精通”调整为有实例支撑的“熟练掌握”。]
</optimization_highlights>
</optimization_result>
`;
