import express from 'express';
import { BillingUtils } from '../utils/billingUtils';
import logger from '../utils/logger';
import { TaskType, getAllTaskPoints, getAvailableTasks } from '../config';

const router = express.Router();

/**
 * 获取用户积分和扣费通道状态
 * @route GET /checkUserPoints
 * @param {string} userId - 用户ID
 * @param {string} channelId - 通道ID
 * @param taskType 任务类型（可选）
 * @returns {Object} 返回用户积分信息和通道状态
 *   - available: 会员扣费功能是否可用
 *   - remainingPoints: 用户当前可用积分
 *   - isEnough: 用户积分是否足够满足当次任务
 *   - taskPoints: 任务点数配置
 *   - availableTasks: 当前可用任务列表
 */
router.get('/checkUserPoints', async (req, res) => {
  try {
    const { userId, channelId, taskType } = req.query as { 
      userId: string; 
      channelId: string;
      taskType?: TaskType;
    };
    
    // 参数校验
    if (!userId || !channelId) {
      return res.status(400).json({ 
        code: 400,
        message: !userId ? '缺少用户ID参数' : '缺少通道ID参数'
      });
    }

    // 检查用户积分状态
    const { isEnough, remainingPoints, available } = await BillingUtils.checkUserPoints(
      userId, 
      channelId,
      taskType
    );

    // 构建响应数据
    const responseData = {
      code: 0,
      data: {
        available,
        remainingPoints: remainingPoints,
        isEnough,
        taskPoints: getAllTaskPoints(),
        availableTasks: getAvailableTasks()
      },
      message: 'success'
    };

    res.status(200).json(responseData);
  } catch (err) {
    logger.error(`查询用户电量失败: ${err.message}`);
    res.status(500).json({ 
      code: 500,
      message: '查询用户电量失败'
    });
  }
});

/**
 * 获取用户会员信息
 * @route GET /checkUserMember
 * @param {string} userId - 用户ID
 * @returns {Object} 返回用户会员信息
 *   - valid: 会员是否有效
 *   - expire_time: 会员到期时间
 */
router.get('/checkUserMember', async (req, res) => {
  try {
    const { userId } = req.query as { userId: string };
    
    // 参数校验
    if (!userId) {
      return res.status(400).json({ 
        code: 400,
        message: '缺少用户ID参数'
      });
    }

    // 检查用户会员状态
    const memberInfo = await BillingUtils.getUserMember(userId);

    // 构建响应数据
    const responseData = {
      code: 0,
      data: {
        info: memberInfo
      },
      message: 'success'
    };

    res.status(200).json(responseData);
  } catch (err) {
    logger.error(`查询用户会员信息失败: ${err.message}`);
    res.status(500).json({ 
      code: 500,
      message: '查询用户会员信息失败'
    });
  }
});

export default router;
