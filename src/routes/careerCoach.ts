import axios from 'axios';
import express from 'express';
import { CareerCoachAgent, CareerCoachEvaluationRequest, GenerateReferenceAnswerRequest } from '../agent/careerCoachAgent';
import logger from '../utils/logger';

const router = express.Router();
const careerCoachAgent = new CareerCoachAgent();

// 设置SSE
const setupSSEConnection = (res: express.Response) => {
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders();
};

// 发送SSE消息
const sendSSEMessage = (res: express.Response, data: any) => {
  res.write(`data: ${JSON.stringify(data)}\n\n`);
};

// 评估用户回答 (非流式)
router.post('/evaluate', async (req, res) => {
  try {
    const evaluationRequest: CareerCoachEvaluationRequest = req.body;
    
    // 验证请求参数
    if (!evaluationRequest.topic || 
        !evaluationRequest.knowledge_points || 
        !evaluationRequest.question || 
        !evaluationRequest.options || 
        !evaluationRequest.correct_answer || 
        !evaluationRequest.user_answer) {
      return res.status(400).json({ 
        status: 'error', 
        message: '缺少必要的评估参数' 
      });
    }

    // 调用智能体进行评估
    const evaluationResult = await careerCoachAgent.evaluateAnswer(evaluationRequest);

    // 返回评估结果
    res.status(200).json({
      status: 'success',
      result: evaluationResult
    });

  } catch (error) {
    logger.error('职场技能评估失败:', error);
    res.status(500).json({ 
      status: 'error', 
      message: '评估失败',
      details: error.message 
    });
  }
});

// 评估用户回答 (流式)
router.post('/evaluate-stream', async (req, res) => {
  try {
    const evaluationRequest: CareerCoachEvaluationRequest = req.body;
    
    // 验证请求参数
    if (!evaluationRequest.topic || 
        !evaluationRequest.knowledge_points || 
        !evaluationRequest.question || 
        !evaluationRequest.options || 
        !evaluationRequest.correct_answer || 
        !evaluationRequest.user_answer) {
      return res.status(400).json({ 
        status: 'error', 
        message: '缺少必要的评估参数' 
      });
    }

    // 设置SSE连接
    setupSSEConnection(res);
    
    // 发送连接成功消息
    sendSSEMessage(res, { status: 'connected', message: '连接成功' });

    // 调用智能体进行流式评估
    const emitter = await careerCoachAgent.streamEvaluateAnswer(evaluationRequest);

    // 处理流式数据
    emitter.on('data', (data) => {
      sendSSEMessage(res, JSON.parse(data));
    });

    // 处理错误
    emitter.on('error', (error) => {
      sendSSEMessage(res, JSON.parse(error));
    });

    // 处理结束
    emitter.on('end', () => {
      sendSSEMessage(res, { status: 'completed', message: '评估完成' });
      res.end();
    });

    // 处理客户端断开连接
    req.on('close', () => {
      emitter.removeAllListeners();
      res.end();
    });

  } catch (error) {
    logger.error('流式职场技能评估失败:', error);
    if (!res.headersSent) {
      res.status(500).json({ 
        status: 'error', 
        message: '评估失败',
        details: error.message 
      });
    } else {
      sendSSEMessage(res, { 
        status: 'error', 
        message: '评估失败',
        details: error.message 
      });
      res.end();
    }
  }
});

// 生成参考答案（非流式）
router.post('/generate-answer', async (req, res) => {
  try {
    const requestData: GenerateReferenceAnswerRequest = req.body;

    // 验证请求参数
    if (!requestData.topic ||
        !requestData.knowledge_points ||
        !requestData.question ||
        !requestData.options ||
        !requestData.correct_answer) {
      return res.status(400).json({
        status: 'error',
        message: '缺少必要的生成参考答案参数'
      });
    }

    // 调用智能体生成参考答案
    const referenceAnswer = await careerCoachAgent.generateReferenceAnswer(requestData);

    // 返回生成的参考答案
    res.status(200).json({
      status: 'success',
      result: referenceAnswer
    });

  } catch (error) {
    logger.error('生成参考答案失败:', error);
    res.status(500).json({ 
      status: 'error', 
      message: '生成参考答案失败',
      details: error.message 
    });
  }
});

// 生成参考答案（流式）
router.post('/generate-answer-stream', async (req, res) => {
  try {
    const requestData: GenerateReferenceAnswerRequest = req.body;

    // 验证请求参数
    if (!requestData.topic ||
        !requestData.knowledge_points ||
        !requestData.question ||
        !requestData.options ||
        !requestData.correct_answer) {
      return res.status(400).json({
        status: 'error',
        message: '缺少必要的生成参考答案参数'
      });
    }

    // 设置SSE连接
    setupSSEConnection(res);
    sendSSEMessage(res, { status: 'connected', message: '连接成功' });

    // 调用智能体进行流式生成
    const emitter = await careerCoachAgent.streamGenerateReferenceAnswer(requestData);

    // 处理流式数据
    emitter.on('data', (data) => {
      sendSSEMessage(res, JSON.parse(data));
    });

    // 处理错误
    emitter.on('error', (error) => {
      sendSSEMessage(res, JSON.parse(error));
    });

    // 处理结束
    emitter.on('end', () => {
      sendSSEMessage(res, { status: 'completed', message: '生成参考答案完成' });
      res.end();
    });

    // 处理客户端断开连接
    req.on('close', () => {
      emitter.removeAllListeners();
      res.end();
    });

  } catch (error) {
    logger.error('流式生成参考答案失败:', error);
    // 确保即使在初始设置SSE之前发生错误，也能发送错误响应
    if (!res.headersSent) {
      res.status(500).json({ status: 'error', message: '生成参考答案失败', details: error.message });
    } else if (!res.writableEnded) {
      sendSSEMessage(res, { status: 'error', message: '生成参考答案失败', details: error.message });
      res.end();
    }
  }
});

// -------------------------------- 测试数据 --------------------------------------------

const CRM_BASE_API_URL = 'https://crm.foundingaz.com/api';
const CRM_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbi1kYXRhIjoiclEtYm1Yb0YuY2Z6N2JTNUw3VHBrRWdQN0poV2FqN3Y2QzRvZ2cwdFlxS0Y0ZDJNTUlHcHg5cjQ0ckJCZS1YNkZ1QXY1bDB3VWt2MDNwLjFGaUcyMVd0M3hwakdQbnhrdG96UkVWSDAydmpwblJOa3c0NENSYmQ5aElqeEdvcDZKMW9zckxkeThqWUM3VmRLeWt5aTRFcUE3aFg4cnJaSmFWaWQ5Uml6WmRXIn0.mbAwCxpAimOtexY-CUbNGj5gIYML_81V32PLt3wta-o"

// 获取所有测试主题
router.get('/topics', async (req, res) => {
  try {
    const response = await axios.get(`${CRM_BASE_API_URL}/training/topic/list?name=&module_id=&status=&page=1&limit=100&from=PC&token=${CRM_TOKEN}`);
    const topicList = response.data.data.list.map((item: any) => ({
      id: item.id,
      title: item.title,
      sub_title: item.sub_title,
      module_id: item.module_id,
      module_name: item.module_name
    }));
    res.status(200).json({ status: 'success', topics: topicList });
  } catch (error) {
    logger.error('获取职场技能测试主题失败:', error);
    res.status(500).json({ status: 'error', message: '获取测试主题失败', details: error.message });
  }
});

// 获取特定主题的问题
router.get('/topics/:topicId/questions', async (req, res) => {
  try {
    const { topicId } = req.params;
    const response = await axios.get(`${CRM_BASE_API_URL}/training/question/list?query=&training_title=&status=1&page=1&limit=1000&from=PC&token=${CRM_TOKEN}`);
    const filteredQuestions = response.data.data.list.filter((q: any) => q.training_id == topicId).map((q: any) => ({
      id: q.id,
      query: q.query,
      subject: q.subject
    }));
    res.status(200).json({ status: 'success', questions: filteredQuestions });
  } catch (error) {
    logger.error('获取职场技能测试问题失败:', error);
    res.status(500).json({ status: 'error', message: '获取测试问题失败', details: error.message });
  }
});

// 获取知识点讲解
router.get('/topics/:topicId/knowledge', async (req, res) => {
  try {
    const { topicId } = req.params;
    const response = await axios.get(`${CRM_BASE_API_URL}/training/knowledge/list?page=1&limit=1000&training_id=${topicId}&from=PC&token=${CRM_TOKEN}`);
    const knowledgeText = response.data.data.list.map((item: any) => item.text).join('\n');
    res.status(200).json({ status: 'success', knowledge_text: knowledgeText });
  } catch (error) {
    logger.error('获取知识点讲解失败:', error);
    res.status(500).json({ status: 'error', message: '获取知识点讲解失败', details: error.message });
  }
});


// 获取问题详情
router.get('/questions/:questionId', async (req, res) => {
  try {
    const { questionId } = req.params;
    const response = await axios.get(`${CRM_BASE_API_URL}/training/question/${questionId}?from=PC&token=${CRM_TOKEN}`);
    const questionData = response.data.data;

    const questionText = questionData.question.query;
    const optionsText = questionData.options.map((opt: any) => `${opt.label}. ${opt.content}`).join('\n');
    const correctOption = questionData.options.find((opt: any) => opt.is_correct === 1);
    const analysis = correctOption?.analysis || '';

    res.status(200).json({
      status: 'success',
      question: questionText,
      options: optionsText,
      correct_answer: analysis
    });
  } catch (error) {
    logger.error('获取问题详情失败:', error);
    res.status(500).json({ status: 'error', message: '获取问题详情失败', details: error.message });
  }
});

export default router;
