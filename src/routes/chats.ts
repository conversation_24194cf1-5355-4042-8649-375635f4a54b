import express from 'express';
import logger from '../utils/logger';
import { db } from '../db/index';
import { eq, and, sql, desc } from 'drizzle-orm';
import { chats, messages, taskQueue } from '../db/schema';

const router = express.Router();

router.get('/', async (req: express.Request<{}, {}, {}, { page: string, limit?: string, source?: string }>, res) => {
  try {
    const deviceId = req.headers['x-device-id'] as string;
    if (!deviceId) {
      return res.status(400).json({ message: '缺少设备标识' });
    }

    const page = parseInt(req.query.page || '1');
    const limit = parseInt(req.query.limit || '50');
    const offset = (page - 1) * limit;
    const source = req.query.source;
    const whereConditions = [
      eq(chats.deviceId, deviceId),
      eq(chats.isDeleted, false)
    ];

    if (source) {
      whereConditions.push(eq(chats.focusMode, source));
    }

    const [{ count }] = await db.select({
      count: sql<number>`cast(count(*) as integer)`,
    })
    .from(chats)
    .where(and(...whereConditions));

    // 分页查询数据，并添加任务状态查询
    const result = await db.select({
      id: chats.id,
      title: chats.title,
      createdAt: chats.createdAt,
      focusMode: chats.focusMode,
      deviceId: chats.deviceId,
      files: chats.files,
      source: chats.source,
      hotQuestionId: chats.hotQuestionId,
      taskStatus: taskQueue.status,
      taskId: taskQueue.id,
      taskAgentType: taskQueue.agentType
    })
    .from(chats)
    .leftJoin(
      taskQueue,
      and(
        eq(taskQueue.chatId, chats.id),
        eq(taskQueue.agentType, 'resumeAnalyzer')
      )
    )
    .where(and(...whereConditions))
    .limit(limit)
    .offset(offset)
    .orderBy((chats) => desc(chats.createdAt));
    
    return res.status(200).json({
      chats: result.map(chat => ({
        ...chat,
        task: chat.taskId ? {
          id: chat.taskId,
          status: chat.taskStatus,
          agentType: chat.taskAgentType
        } : null
      })),
      pagination: {
        current: page,
        size: limit,
        total: Number(count)
      }
    });
  } catch (err) {
    logger.error(`Error in getting chats: ${err}`);
    res.status(500).json({ message: '获取聊天列表失败' });
  }
});

router.get('/:id', async (req: express.Request<{id: string}, {}, {}, { share?: string }>, res) => {
  try {
    const share = req.query.share === 'true';
    const deviceId = req.headers['x-device-id'] as string;
    
    if (!share && !deviceId) {
      return res.status(400).json({ message: '缺少设备标识' });
    }

    const chatExists = await db.select()
      .from(chats)
      .where(
        share ? 
          eq(chats.id, req.params.id) :
          and(
            eq(chats.id, req.params.id),
            eq(chats.deviceId, deviceId)
          )
      )
      .limit(1)
      .execute();

    if (!chatExists?.length) {
      return res.status(404).json({ message: '未找到聊天记录' });
    }

    const chat = chatExists[0];
    const chatMessages = await db.select()
      .from(messages)
      .where(eq(messages.chatId, req.params.id))
      .orderBy(messages.id)
      .execute();

    // 如果是简历分析模式，查询相关任务
    let task = null;
    if (chat.focusMode === 'resumeAnalyzer') {
      const tasks = await db.select({
        id: taskQueue.id,
        agentType: taskQueue.agentType,
        status: taskQueue.status
      })
      .from(taskQueue)
      .where(
        and(
          eq(taskQueue.chatId, req.params.id),
          eq(taskQueue.agentType, 'resumeAnalyzer')
        )
      )
      .orderBy(desc(taskQueue.createdAt))
      .limit(1)
      .execute();

      if (tasks.length > 0) {
        task = tasks[0];
      }
    }

    return res.status(200).json({ 
      chat, 
      messages: chatMessages,
      task: task // 如果存在任务则返回，否则为 null
    });
  } catch (err) {
    logger.error(`Error in getting chat: ${err}`);
    res.status(500).json({ message: '获取聊天详情失败' });
  }
});

router.delete('/:id', async (req, res) => {
  try {
    await db.transaction(async (tx) => {
      // 软删除消息
      await tx.update(messages)
        .set({ [messages.isDeleted.name]: true })
        .where(eq(messages.chatId, req.params.id));
      
      // 软删除聊天记录
      const result = await tx.update(chats)
        .set({ [chats.isDeleted.name]: true })
        .where(eq(chats.id, req.params.id))
        .returning({ id: chats.id });

      if (!result.length) {
        throw new Error('Chat not found');
      }
    });

    return res.status(200).json({ message: '删除成功' });
  } catch (err) {
    logger.error(`Error in deleting chat: ${err}`);
    if (err.message === 'Chat not found') {
      return res.status(404).json({ message: '未找到聊天记录' });
    }
    res.status(500).json({ message: '删除失败' });
  }
});

router.delete('/messages/:messageId', async (req, res) => {
  try {
    const result = await db
      .update(messages)
      .set({ [messages.isDeleted.name]: true })
      .where(
        and(
          eq(messages.messageId, req.params.messageId),
          eq(messages.isDeleted, false)
        )
      )
      .returning({ messageId: messages.messageId })
      .execute();

    if (!result.length) {
      return res.status(404).json({ message: 'Message not found' });
    }

    return res.status(200).json({ message: 'Message deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(`Error in deleting message: ${err.message}`);
  }
});

router.get('/:id/messages', async (req, res) => {
  try {
    const chatMessages = await db.select({
      type: messages.role,
      messageId: messages.messageId,
      content: messages.content,
      metadata: messages.metadata
    })
    .from(messages)
    .where(
      and(
        eq(messages.chatId, req.params.id),
        eq(messages.isDeleted, false)
      )
    )
    .orderBy(messages.id)
    .execute();

    if (!chatMessages.length) {
      return res.status(404).json({ message: '未找到聊天记录' });
    }

    return res.status(200).json({ messages: chatMessages });
  } catch (err) {
    logger.error(`Error in getting chat messages: ${err}`);
    res.status(500).json({ message: '获取消息失败' });
  }
});

export default router;
