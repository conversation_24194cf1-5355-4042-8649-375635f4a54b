import express from 'express';
import {
  getChatModel,
  getEmbeddingModel,
} from '../lib/providers';
import {
  getGroqApi<PERSON>ey,
  getOllamaApiEndpoint,
  getAnthropicApiKey,
  getGeminiApiKey,
  getOpenaiApiKey,
  getCustomSettingModel,  // 添加导入
  updateConfig,
} from '../config';
import logger from '../utils/logger';

const router = express.Router();

router.get('/', async (_, res) => {
  try {
    const config = {};

    const [chatModel, embeddingModel] = await Promise.all([
      getChatModel(),
      getEmbeddingModel()
    ]);

    if (!chatModel || !embeddingModel) {
      throw new Error('Failed to load models');
    }

    config['openaiApiKey'] = getOpenaiApiKey();
    config['ollamaApiUrl'] = getOllamaApiEndpoint();
    config['anthropicApiKey'] = getAnthropicApiKey();
    config['groqApiKey'] = getGroqApiKey();
    config['geminiApiKey'] = getGeminiApiKey();
    config['customSettingModel'] = getCustomSettingModel();

    res.status(200).json(config);
  } catch (err: any) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(`Error getting config: ${err.message}`);
  }
});

router.post('/', async (req, res) => {
  const config = req.body;

  const updatedConfig = {
    API_KEYS: {
      OPENAI: config.openaiApiKey,
      GROQ: config.groqApiKey,
      ANTHROPIC: config.anthropicApiKey,
      GEMINI: config.geminiApiKey,
    },
    API_ENDPOINTS: {
      OLLAMA: config.ollamaApiUrl,
    },
  };

  updateConfig(updatedConfig);

  res.status(200).json({ message: 'Config updated' });
});

export default router;
