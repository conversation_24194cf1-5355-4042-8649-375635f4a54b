import express from 'express';
import htmlDocx from 'html-docx-js';

const router = express.Router();

router.post('/', async (req, res) => {
    try {
        const { htmlContent, title, chatId } = req.body;

        const blob = htmlDocx.asBlob(htmlContent, {
            orientation: 'portrait',
            margins: { top: 1440, right: 1440, bottom: 1440, left: 1440 }
        });

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        res.setHeader('Content-Disposition', `attachment; filename="chat-${encodeURIComponent(title)}-${encodeURIComponent(chatId)}.docx"; filename*=UTF-8''chat-${encodeURIComponent(title)}-${encodeURIComponent(chatId)}.docx`);
        res.send(Buffer.from(await blob.arrayBuffer()));
    } catch (error) {
        console.error('导出失败:', error);
        res.status(500).json({ error: '导出失败' });
    }
});

export default router;