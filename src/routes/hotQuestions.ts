import express from 'express';
import { db } from '../db/index';
import { eq, and } from 'drizzle-orm';
import { hotQuestions, chats } from '../db/schema';
import logger from '../utils/logger';

const router = express.Router();

// 获取热门问题列表
router.get('/', async (req, res) => {
  try {
    const deviceId = req.headers['x-device-id'] as string;
    
    const questions = await db.select()
      .from(hotQuestions)
      .where(eq(hotQuestions.isEnabled, true))
      .execute();

    if (deviceId) {
      // 如果有 deviceId，查询关联的会话
      const relatedChats = await db.select({
        hotQuestionId: chats.hotQuestionId,
        chatId: chats.id
      })
      .from(chats)
      .where(
        and(
          eq(chats.deviceId, deviceId),
          eq(chats.isDeleted, false)
        )
      )
      .execute();

      // 将会话信息添加到热门问题中
      const questionsWithChat = questions.map(question => ({
        id: question.id,
        title: question.title,
        chatId: relatedChats.find(chat => chat.hotQuestionId === question.id)?.chatId || null
      }));

      return res.status(200).json({ questions: questionsWithChat });
    }

    // 如果没有 deviceId，只返回问题信息
    const questionsWithoutChat = questions.map(question => ({
      id: question.id,
      title: question.title,
      chatId: null
    }));

    res.status(200).json({ questions: questionsWithoutChat });
  } catch (err) {
    logger.error(`Error in getting hot questions: ${err.message}`);
    res.status(500).json({ message: '获取热门问题失败' });
  }
});

// 创建热门问题
router.post('/', async (req, res) => {
  try {
    const { title } = req.body;
    
    if (!title) {
      return res.status(400).json({ message: '标题不能为空' });
    }

    const values = {
      title,
      isEnabled: true,
    };
    const result = await db.insert(hotQuestions)
      .values(values)
      .returning()
      .execute();

    res.status(201).json(result[0]);
  } catch (err) {
    logger.error(`Error in creating hot question: ${err.message}`);
    res.status(500).json({ message: '创建热门问题失败' });
  }
});

// 更新热门问题
router.put('/:id', async (req, res) => {
  try {
    const { title, isEnabled } = req.body;
    const id = parseInt(req.params.id);

    if (!title && isEnabled === undefined) {
      return res.status(400).json({ message: '更新内容不能为空' });
    }

    const result = await db.update(hotQuestions)
      .set({
        ...(title && { title }),
        ...(isEnabled !== undefined && { isEnabled }),
        [hotQuestions.updatedAt.name]: new Date(),
      })
      .where(eq(hotQuestions.id, id))
      .returning()
      .execute();

    if (!result.length) {
      return res.status(404).json({ message: '热门问题不存在' });
    }

    res.status(200).json(result[0]);
  } catch (err) {
    logger.error(`Error in updating hot question: ${err.message}`);
    res.status(500).json({ message: '更新热门问题失败' });
  }
});

// 删除热门问题
router.delete('/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    
    const result = await db.update(hotQuestions)
      .set({ [hotQuestions.isEnabled.name]: false })
      .where(eq(hotQuestions.id, id))
      .returning()
      .execute();

    if (!result.length) {
      return res.status(404).json({ message: '热门问题不存在' });
    }

    res.status(200).json({ message: '删除成功' });
  } catch (err) {
    logger.error(`Error in deleting hot question: ${err.message}`);
    res.status(500).json({ message: '删除热门问题失败' });
  }
});

export default router;