import express from 'express';
import handleImageSearch from '../chains/imageSearchAgent';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { getSummarizerModel } from '../lib/providers';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import logger from '../utils/logger';

const router = express.Router();

interface ImageSearchBody {
  query: string;
  chatHistory: any[];
}

router.post('/', async (req, res) => {
  try {
    let body: ImageSearchBody = req.body;

    const chatHistory = body.chatHistory.map((msg: any) => {
      if (msg.role === 'user') {
        return new HumanMessage(msg.content);
      } else if (msg.role === 'assistant') {
        return new AIMessage(msg.content);
      }
    });

    const llm = await getSummarizerModel();

    const images = await handleImageSearch(
      { query: body.query, chat_history: chatHistory },
      llm,
    );

    res.status(200).json({ images });
  } catch (err) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(`Error in image search: ${err.message}`);
  }
});

export default router;
