import express from 'express';
import imagesRouter from './images';
import videosRouter from './videos';
import configRouter from './config';
import modelsRouter from './models';
import suggestionsRouter from './suggestions';
import chatsRouter from './chats';
import searchRouter from './search';
import discoverRouter from './discover';
import uploadsRouter from './uploads';
import optimizeRouter from './optimize';
import hotQuestionsRouter from './hotQuestions';
import exportDocxRouter from './export-docx';
import resumeRouter from './resume';
import knowledgeRouter from './knowledge';
import tasksRouter from './tasks';
import billingRouter from './billing';
import careerCoachRouter from './careerCoach';
import podcastRouter from './podcast';

const router = express.Router();

router.use('/images', imagesRouter);
router.use('/videos', videosRouter);
router.use('/config', configRouter);
router.use('/models', modelsRouter);
router.use('/suggestions', suggestionsRouter);
router.use('/chats', chatsRouter);
router.use('/search', searchRouter);
router.use('/discover', discoverRouter);
router.use('/uploads', uploadsRouter);
router.use('/optimize', optimizeRouter);
router.use('/hot-questions', hotQuestionsRouter);
router.use('/export-docx', exportDocxRouter);
router.use('/resume', resumeRouter);
router.use('/knowledge', knowledgeRouter);
router.use('/tasks', tasksRouter);
router.use('/billing', billingRouter);
router.use('/career-coach', careerCoachRouter);
router.use('/podcast', podcastRouter);

export default router;
