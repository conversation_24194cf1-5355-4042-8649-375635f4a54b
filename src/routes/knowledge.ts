import express from 'express';
import { KnowledgeSearcher } from '../search/knowledgeSearcher';
import { KnowledgeChannel } from '../config/knowledge';
import logger from '../utils/logger';

const router = express.Router();

interface KnowledgeSearchBody {
  query: string;
  userId?: string;
  channel?: KnowledgeChannel;
}

router.post('/', async (req: express.Request<{}, {}, KnowledgeSearchBody>, res) => {
  try {
    const { query, userId = 'default-user', channel = 'knowledgeFromDify' } = req.body;
    
    if (!query) {
      return res.status(400).json({ message: '缺少查询内容' });
    }

    const knowledgeSearcher = new KnowledgeSearcher();
    const results = await knowledgeSearcher.search(query, userId, channel);

    res.status(200).json({ 
      results: results.map(doc => ({
        content: doc.pageContent,
        ...doc.metadata
      }))
    });
  } catch (err) {
    logger.error(`Error in knowledge search: ${err.message}`);
    res.status(500).json({ message: '知识库搜索失败' });
  }
});

export default router;