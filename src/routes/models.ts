import express from 'express';
import logger from '../utils/logger';
import { 
  getOpenaiApiKey, 
  getCustomBaseURL, 
  getCustomChatModel,
  getCustomEmbeddingModel,
} from '../config';

const router = express.Router();

router.get('/', async (req, res) => {
  try {
    const openAIApiKey = getOpenaiApiKey();
    const baseURL = getCustomBaseURL();
    
    // 获取所有配置的模型名称
    const chatModel = getCustomChatModel();
    const embeddingModel = getCustomEmbeddingModel();

    // 只有当API密钥存在时才返回模型信息
    if (!openAIApiKey) {
      return res.status(200).json({ 
        chatModelProviders: {}, 
        embeddingModelProviders: {} 
      });
    }

    // 构建模型提供者对象
    const modelProviders = {
      chatModelProviders: {
        openai: {
          [chatModel]: {
            displayName: chatModel,
            baseURL: baseURL
          }
        }
      },
      embeddingModelProviders: {
        openai: {
          [embeddingModel]: {
            displayName: embeddingModel,
            baseURL: baseURL
          }
        }
      }
    };

    res.status(200).json(modelProviders);
  } catch (err) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(err.message);
  }
});

export default router;
