import express from 'express';
import { optimizeSearchQuery } from '../utils/queryOptimizer';
import logger from '../utils/logger';

const router = express.Router();

interface OptimizeQueryBody {
  query: string;
  type?: 'search' | 'knowledge';
}

router.post('/', async (req: express.Request<{}, {}, OptimizeQueryBody>, res) => {
  try {
    const { query, type = 'search' } = req.body;
    
    if (!query) {
      return res.status(400).json({ message: '缺少查询内容' });
    }

    const optimizedQuery = await optimizeSearchQuery(query, type);

    res.status(200).json({ 
      original: query,
      optimized: optimizedQuery,
      type
    });
  } catch (err) {
    logger.error(`Error in optimizing query: ${err.message}`);
    res.status(500).json({ message: '优化查询失败' });
  }
});

export default router;