import express from 'express';
import ffmpeg from 'fluent-ffmpeg';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import {
    generateAndSaveIndividualAudio,
    combineAudioFiles,
    uploadCombinedAudioToOss,
    cleanupTemporaryFiles,
    fetchGeneratedAudio
} from '../utils/podcast';

import {
    generateAndSaveIndividualAudio as generateAndSaveIndividualAudioDy,
    combineAudioFiles as combineAudioFilesDy,
    uploadCombinedAudioToOss as uploadCombinedAudioToOssDy,
    cleanupTemporaryFiles as cleanupTemporaryFilesDy,
    fetchGeneratedAudio as fetchGeneratedAudioDy
} from '../utils/podcast-dy';

import {
    generateAndSaveIndividualAudioTx,
    combineAudioFilesTx,
    uploadCombinedAudioToOssTx,
    cleanupTemporaryFilesTx,
    fetchGeneratedAudioTx
} from '../utils/podcast-tx';

const router = express.Router();

router.post('/generate-xf', async (req, res) => {

    logger.info('--- Entering /generate route handler ---');
    logger.info('Received /generate request');
    const { textArray } = req.body;


    const signature = `ai_xf_${uuidv4()}`;
    const tempDir = path.join(__dirname, '..', '..', 'temp', signature);
    fs.mkdirSync(tempDir, { recursive: true });
    logger.info(`Created temporary directory: ${tempDir}`);

    try {
        const audioFilePaths: string[] = [];

        // 前奏音频
        const preludeAudioData: any = await fetchGeneratedAudio(`https://f.foundingaz.cn/api/files/audio/podcast-prelude`, 0) as ArrayBuffer;
        const originalPreludePath = path.join(tempDir, `audio_prelude_original.mp3`);
        fs.writeFileSync(originalPreludePath, preludeAudioData);
        logger.info(`Saved original prelude audio file: ${originalPreludePath}`);

        const trimmedPreludePath = path.join(tempDir, `audio_prelude.mp3`);
        await new Promise<void>((resolve, reject) => {
            ffmpeg(originalPreludePath)
                .setDuration(3)
                .on('end', () => {
                    logger.info('Prelude audio trimmed to 3 seconds successfully.');
                    resolve();
                })
                .on('error', (err) => {
                    logger.error(`Error trimming prelude audio: ${err.message}`);
                    reject(err);
                })
                .save(trimmedPreludePath);
        });
        audioFilePaths.push(trimmedPreludePath);

        for (let i = 0; i < textArray.length; i++) {
            const tempAudioPath = await generateAndSaveIndividualAudio(textArray[i], i + 1, signature, tempDir);
            audioFilePaths.push(tempAudioPath);
        }
        logger.info(`All individual audio files generated. Paths: ${audioFilePaths.join(', ')}`);
        logger.info(`Audio files to combine: ${JSON.stringify(audioFilePaths)}`);

        const outputFilePath = await combineAudioFiles(audioFilePaths, tempDir, signature);

        const combinedAudioUploadResult = await uploadCombinedAudioToOss(outputFilePath, signature);

        cleanupTemporaryFiles(tempDir);

        res.status(200).json({
            message: 'Podcast generated and uploaded successfully',
            audioUrl: `https://f.foundingaz.cn/api/files/audio/${signature}`,
            uploadResult: combinedAudioUploadResult,
        });
        logger.info('Response sent successfully.');
    } catch (error: any) {
        logger.error(`Error generating podcast: ${error.message}`);
        cleanupTemporaryFiles(tempDir);
        res.status(500).json({ error: 'Failed to generate podcast.' });
        logger.info('Error response sent.');
    }
});

router.post('/generate-dy', async (req, res) => {

    logger.info('--- Entering /generate-dy route handler ---');
    logger.info('Received /generate-dy request');
    const { textArray } = req.body;
    logger.info(`textArray: ${JSON.stringify(textArray)}`);

    if (!textArray || !Array.isArray(textArray) || textArray.length === 0) {
        logger.info('Validation failed: textArray is invalid.');
        return res
            .status(400)
            .json({ error: 'textArray is required and must be a non-empty array.' });
    }
    logger.info('textArray validated successfully.');

    const signature = `ai_dy_${uuidv4()}`;
    const tempDir = path.join(__dirname, '..', '..', 'temp', signature);
    fs.mkdirSync(tempDir, { recursive: true });
    logger.info(`Created temporary directory: ${tempDir}`);

    try {
        const audioFilePaths: string[] = [];

        // 前奏音频
        const preludeAudioData: any = await fetchGeneratedAudioDy(`https://f.foundingaz.cn/api/files/audio/podcast-prelude`, 0) as ArrayBuffer;
        const originalPreludePath = path.join(tempDir, `audio_prelude_original.mp3`);
        fs.writeFileSync(originalPreludePath, preludeAudioData);
        logger.info(`Saved original prelude audio file: ${originalPreludePath}`);

        const trimmedPreludePath = path.join(tempDir, `audio_prelude.mp3`);
        await new Promise<void>((resolve, reject) => {
            ffmpeg(originalPreludePath)
                .setDuration(3)
                .on('end', () => {
                    logger.info('Prelude audio trimmed to 3 seconds successfully.');
                    resolve();
                })
                .on('error', (err) => {
                    logger.error(`Error trimming prelude audio: ${err.message}`);
                    reject(err);
                })
                .save(trimmedPreludePath);
        });
        audioFilePaths.push(trimmedPreludePath);

        for (let i = 0; i < textArray.length; i++) {
            const tempAudioPath = await generateAndSaveIndividualAudioDy(textArray[i], i + 1, signature, tempDir);
            if (tempAudioPath) { // Only push if not null
                audioFilePaths.push(tempAudioPath);
            }
        }
        logger.info(`All individual audio files generated. Paths: ${audioFilePaths.join(', ')}`);
        logger.info(`Audio files to combine: ${JSON.stringify(audioFilePaths)}`);

        const outputFilePath = await combineAudioFilesDy(audioFilePaths, tempDir, signature);

        const combinedAudioUploadResult = await uploadCombinedAudioToOssDy(outputFilePath, signature);

        cleanupTemporaryFilesDy(tempDir);

        res.status(200).json({
            message: 'Podcast generated and uploaded successfully',
            audioUrl: `https://f.foundingaz.cn/api/files/audio/${signature}`,
            uploadResult: combinedAudioUploadResult,
        });
        logger.info('Response sent successfully.');
    } catch (error: any) {
        logger.error(`Error generating podcast: ${error.message}`);
        cleanupTemporaryFilesDy(tempDir);
        res.status(500).json({ error: 'Failed to generate podcast.' });
        logger.info('Error response sent.');
    }
});

// 腾讯云语音合成接口
router.post('/generate-tx', async (req, res) => {

    logger.info('--- Entering /generate-tx route handler ---');
    logger.info('Received /generate-tx request');
    const { textArray } = req.body;
    logger.info(`textArray: ${JSON.stringify(textArray)}`);

    if (!textArray || !Array.isArray(textArray) || textArray.length === 0) {
        logger.info('Validation failed: textArray is invalid.');
        return res
            .status(400)
            .json({ error: 'textArray is required and must be a non-empty array.' });
    }
    logger.info('textArray validated successfully.');

    const signature = `ai_tx_${uuidv4()}`;
    const tempDir = path.join(__dirname, '..', '..', 'temp', signature);
    fs.mkdirSync(tempDir, { recursive: true });
    logger.info(`Created temporary directory: ${tempDir}`);

    try {
        const audioFilePaths: string[] = [];

        // 前奏音频
        const preludeAudioData: any = await fetchGeneratedAudioTx(`https://f.foundingaz.cn/api/files/audio/podcast-prelude`, 0) as ArrayBuffer;
        const originalPreludePath = path.join(tempDir, `audio_prelude_original.mp3`);
        fs.writeFileSync(originalPreludePath, preludeAudioData);
        logger.info(`Saved original prelude audio file: ${originalPreludePath}`);

        const trimmedPreludePath = path.join(tempDir, `audio_prelude.mp3`);
        await new Promise<void>((resolve, reject) => {
            ffmpeg(originalPreludePath)
                .setDuration(3)
                .on('end', () => {
                    logger.info('Prelude audio trimmed to 3 seconds successfully.');
                    resolve();
                })
                .on('error', (err) => {
                    logger.error(`Error trimming prelude audio: ${err.message}`);
                    reject(err);
                })
                .save(trimmedPreludePath);
        });
        audioFilePaths.push(trimmedPreludePath);

        for (let i = 0; i < textArray.length; i++) {
            const tempAudioPath = await generateAndSaveIndividualAudioTx(textArray[i], i + 1, signature, tempDir);
            if (tempAudioPath) { // Only push if not null
                audioFilePaths.push(tempAudioPath);
            }
        }
        logger.info(`All individual audio files generated. Paths: ${audioFilePaths.join(', ')}`);
        logger.info(`Audio files to combine: ${JSON.stringify(audioFilePaths)}`);

        const outputFilePath = await combineAudioFilesTx(audioFilePaths, tempDir, signature);

        const combinedAudioUploadResult = await uploadCombinedAudioToOssTx(outputFilePath, signature);

        cleanupTemporaryFilesTx(tempDir);

        res.status(200).json({
            message: 'Podcast generated and uploaded successfully',
            audioUrl: `https://f.foundingaz.cn/api/files/audio/${signature}`,
            uploadResult: combinedAudioUploadResult,
        });
        logger.info('Response sent successfully.');
    } catch (error: any) {
        logger.error(`Error generating podcast: ${error.message}`);
        cleanupTemporaryFilesTx(tempDir);
        res.status(500).json({ error: 'Failed to generate podcast.' });
        logger.info('Error response sent.');
    }
});

export default router;
