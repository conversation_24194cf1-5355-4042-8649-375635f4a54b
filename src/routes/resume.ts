import express from 'express';
import { TaskType } from '../config';
import rateLimit from 'express-rate-limit';
import { resumeAnalyzer } from '../agent/resumeAnalyzer';
import { taskQueueManager } from '../agent/taskQueue';
import { BillingUtils } from '../utils/billingUtils';
import logger from '../utils/logger';

const router = express.Router();

// 限流配置 - 每分钟最多100个请求
const limiter = rateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 100,
    message: '请求过于频繁，请稍后再试',
    standardHeaders: true,
    legacyHeaders: false,
});

// 设置SSE
const setupSSEConnection = (res: express.Response) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();
    sendSSEMessage(res, { status: 'connected', message: '连接成功' });
};

// 发送SSE消息的函数
const sendSSEMessage = (res: express.Response, params: any) => {
    res.write(`data: ${JSON.stringify(params)}\n\n`);
}

// 处理错误消息
const handleErrorMessage = (error: string, taskId?: bigint, chatId?: string) => {
    const taskError = error || '未知错误';
    let userFriendlyMessage = '处理失败';
    
    // 根据错误信息提供更友好的提示
    if (taskError.includes('文本内容过短')) {
        userFriendlyMessage = '简历内容过短，或者文件加密无法解析，请确保提供可用的简历信息';
    } else if (taskError.includes('乱码')) {
        userFriendlyMessage = '简历文本存在乱码，请重新上传或检查文件格式';
    } else if (taskError.includes('缺少基本的简历关键信息')) {
        userFriendlyMessage = '未能识别出简历的基本信息，请确保文件包含姓名、教育、工作经验等关键信息';
    }

    return { 
        status: 'error', 
        message: userFriendlyMessage,
        error: taskError,
        ...(chatId && { chatId }),
        ...(taskId && { taskId })
    };
};

// 检查任务状态
const monitorTaskStatus = (res: express.Response, taskId: bigint) => {
  const checkInterval = setInterval(async () => {
      try {
          const chainResults = await taskQueueManager.getTaskChainResults(taskId);
          
          // 检查文本识别任务
          if (chainResults.completed?.result?.resumeText) {
              sendSSEMessage(res, { 
                  status: 'analyzing', 
                  message: '文本识别完成，正在分析简历...',
                  chatId: chainResults.chatId,
                  taskId: chainResults.completed.taskId
              });
          }
          
          // 检查分析任务
          if (chainResults.analyzerTask?.status === 'completed' && 
              chainResults.analyzerTask?.result?.analysis) {
              sendSSEMessage(res, { 
                  status: 'optimizing',
                  message: '分析完成，正在优化简历...',
                  chatId: chainResults.chatId,
                  taskId: chainResults.originalTaskId
              });
          }
          
          // 检查优化任务
          if (chainResults.optimizerTask?.status === 'completed' && 
              chainResults.optimizerTask?.result?.optimizedResume) {
              sendSSEMessage(res, { 
                  status: 'completed',
                  message: '简历优化完成',
                  chatId: chainResults.chatId,
                  taskId: chainResults.originalTaskId
              });
              clearInterval(checkInterval);
              res.end();
              return;
          }
          
          // 检查失败状态
          if (chainResults.failed?.error || chainResults.optimizerTask?.status === 'failed') {
              const errorMessage = chainResults.failed?.error || 
                                  chainResults.optimizerTask?.error || 
                                  '任务执行失败';
              
              sendSSEMessage(res, handleErrorMessage(
                  errorMessage,
                  taskId,
                  chainResults.chatId,
              ));
              clearInterval(checkInterval);
              res.end();
          }
      } catch (error) {
          sendSSEMessage(res, handleErrorMessage(
              error.message,
              taskId
          ));
          clearInterval(checkInterval);
          res.end();
      }
  }, 3000);

  return checkInterval;
};

router.post('/analyze-sse', limiter, async (req, res) => {
    const { resumeFileId, resumeFileName, jobFileId, jobFileName, resumeText, jobText, channelId, deviceId, userId } = req.body;
    const clientDeviceId = deviceId || req.headers['x-device-id'] as string;
    
    try {
        if (!(resumeFileId || resumeText)) {
            return res.status(400).json({ 
                error: '需要提供简历文件ID或简历文本' 
            });
        }

        // 设置SSE响应
        setupSSEConnection(res);

        // 检查用户电量
        const pointsCheck = await BillingUtils.checkUserPoints(userId, channelId, 'resumeAnalyzer' as TaskType);
        logger.info(`用户电量检查结果: ${JSON.stringify(pointsCheck)}`);
        if (!pointsCheck.isEnough) {
            sendSSEMessage(res, { 
                status: 'not_enough_points', 
                message: `当前电量: ${pointsCheck.remainingPoints}，电量不足，请充值后继续使用`, 
                error: '电量不足，请充值后继续使用',
            });
            res.end();
            return;
        }

        // 创建任务
        const { taskId } = await resumeAnalyzer.createAnalysisTask({
            resumeFileId,
            jobFileId,
            resumeText,
            jobText,
            resumeFileName,
            jobFileName,
            channelId,
            deviceId: clientDeviceId,
            userId,
        });

        console.log(`简历分析任务创建成功，任务ID: ${taskId}`);

        // 发送任务状态消息和chatId
        sendSSEMessage(res, { 
            status: 'recognizing', 
            message: resumeFileId ? '文件识别中' : '简历分析中',
            taskId
        });
        
        // 监控任务状态
        const checkInterval = monitorTaskStatus(res, taskId);

        // 处理客户端断开连接
        req.on('close', () => {
            clearInterval(checkInterval);
            res.end();
        });
    } catch (error) {
        console.error('任务创建异常:', error.message);
        sendSSEMessage(res, { 
            status: 'error', 
            message: '任务创建失败', 
            details: error.message 
        });
        res.end();
    }
});

// 通过任务ID获取任务状态更新
router.get('/task-status/:taskId', limiter, async (req, res) => {
    const { taskId } = req.params;
    
    try {
        if (!taskId || isNaN(Number(taskId))) {
            return res.status(400).json({ 
                error: '需要提供有效的任务ID' 
            });
        }

        const taskIdBigInt = BigInt(taskId);
        
        // 设置SSE响应
        setupSSEConnection(res);
        
        // 开始监控任务状态
        const checkInterval = monitorTaskStatus(res, taskIdBigInt);

        // 处理客户端断开连接
        req.on('close', () => {
            clearInterval(checkInterval);
            res.end();
        });
    } catch (error) {
        console.error('任务状态监控异常:', error.message);
        sendSSEMessage(res, { 
            status: 'error', 
            message: '任务状态监控失败', 
            details: error.message 
        });
        res.end();
    }
});

// 简历文本分析
router.post('/analyze', async (req, res) => {
  try {
    const { resumeText, jobDescription } = req.body;

    if (!resumeText || typeof resumeText !== 'string') {
      return res.status(400).json({ 
        status: 'error', 
        message: '缺少简历文本内容或格式不正确' 
      });
    }

    // 调用简历分析agent
    const analysisResult = await resumeAnalyzer.analyzeResume(resumeText, jobDescription);

    if (analysisResult.status === 'error') {
      return res.status(500).json({ 
        status: 'error', 
        message: '简历分析失败',
        details: analysisResult.error 
      });
    }

    res.status(200).json({
      status: 'success',
      ...analysisResult
    });

  } catch (error) {
    console.error('简历分析请求异常:', error.message);
    res.status(500).json({ 
      status: 'error', 
      message: '简历分析失败',
      details: error.message 
    });
  }
});

// 获取简历改进点建议
router.get('/improvements/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    
    if (!chatId) {
      return res.status(400).json({ 
        status: 'error', 
        message: '缺少会话ID参数' 
      });
    }

    // 获取改进点建议
    const improvements = await taskQueueManager.getCoreImprovements(chatId);

    if (!improvements) {
      return res.status(404).json({ 
        status: 'not_found', 
        message: '未找到改进点建议' 
      });
    }

    res.status(200).json({
      status: 'success',
      improvements
    });

  } catch (error) {
    console.error('获取改进点建议异常:', error.message);
    res.status(500).json({ 
      status: 'error', 
      message: '获取改进点建议失败',
      details: error.message 
    });
  }
});

export default router;
