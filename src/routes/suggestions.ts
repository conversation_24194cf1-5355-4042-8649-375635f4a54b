import express from 'express';
import generateSuggestions from '../chains/suggestionGeneratorAgent';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { getSummarizerModel } from '../lib/providers';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { GoodsSearcher } from '../search/goodsSearcher';
import { optimizeSearchQuery } from '../utils/queryOptimizer';
import logger from '../utils/logger';

const router = express.Router();

interface SuggestionsBody {
  chatHistory: any[];
  taskType?: string;      // 任务类型参数
  recommendType?: 'training' | 'goods'; // 推荐类型参数
}

/**
 * @api 获取智能建议与推荐内容
 * @apiDescription
 * 根据用户对话历史生成智能建议，并根据 recommendType 推荐商品或训练主题卡片。
 *
 * @apiParam {Object[]} chatHistory 聊天历史，数组，每项包含 role（'user'/'assistant'）和 content（消息内容）
 * @apiParam {String} [taskType] 任务类型（如 'resumeAnalyzer'，用于区分简历分析等特殊模式）
 * @apiParam {String} [recommendType] 推荐类型，可选值 'goods'（商品推荐，默认）或 'training'（训练主题推荐）
 *
 * @apiSuccess {Object[]} suggestions 智能建议列表
 * @apiSuccess {Object[]} relatedContent 推荐内容列表，结构如下：
 *  - 商品卡片：{ title, desc, type, thumb_url, ... }
 *  - 训练卡片：{ title, desc, training_id, ... }
 */
router.post('/', async (req, res) => {
  try {
    let body: SuggestionsBody = req.body;

    const chatHistory = body.chatHistory.map((msg: any) => {
      if (msg.role === 'user') {
        return new HumanMessage(msg.content);
      } else if (msg.role === 'assistant') {
        return new AIMessage(msg.content);
      }
    });

    const llm = await getSummarizerModel();

    // 从最后一条用户消息获取查询文本
    const lastUserMessage = chatHistory.findLast(msg => msg instanceof HumanMessage);
    const query = typeof lastUserMessage?.content === 'string' ? lastUserMessage.content : '';

    // 根据任务类型处理不同的逻辑
    if (body.taskType === 'resumeAnalyzer') {
      // 简历分析模式：只生成建议，不推荐商品
      const suggestions = await generateSuggestions({ chat_history: chatHistory }, llm);
      
      return res.status(200).json({ 
        suggestions: suggestions,
        relatedContent: [] // 简历分析模式不返回相关商品
      });
    } else {
      // 其他模式：生成建议并推荐相关内容
      const optimizedQuery = await optimizeSearchQuery(query, 'search');
      logger.info(`原始 Query: ${query} - 生成商品查询关键词: ${optimizedQuery}`);

      const goodsSearcher = new GoodsSearcher();
      const [suggestions, relatedContent] = await Promise.all([
        generateSuggestions({ chat_history: chatHistory }, llm),
        goodsSearcher.fetchGoodsOrTrainingCards(
          optimizedQuery,
          body.recommendType
        )
      ]);

      return res.status(200).json({ 
        suggestions: suggestions,
        relatedContent: relatedContent.map(doc => {
          const { metadata } = doc;
          if (metadata.training_id) {
            return {
              title: metadata.title,
              desc: doc.pageContent,
              training_id: metadata.training_id
            };
          } else {
            return {
              title: metadata.title,
              desc: doc.pageContent,
              type: metadata.type,
              thumb_url: metadata.thumb_url,
              ...metadata
            };
          }
        })
      });
    }
  } catch (err) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(`Error in generating suggestions: ${err.message}`);
  }
});

export default router;
