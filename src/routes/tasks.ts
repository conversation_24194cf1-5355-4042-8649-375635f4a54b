import express from 'express';
import { db } from '../db/index';
import { eq } from 'drizzle-orm';
import { taskQueue } from '../db/schema';
import logger from '../utils/logger';

const router = express.Router();

router.get('/:taskId', async (req, res) => {
    try {
        const taskId = parseInt(req.params.taskId);

        const task = await db.select()
            .from(taskQueue)
            .where(eq(taskQueue.id, taskId))
            .limit(1)
            .execute();

        if (!task?.length) {
            return res.status(404).json({ message: '任务不存在' });
        }

        res.status(200).json({
            id: task[0].id,
            status: task[0].status,
            result: task[0].result,
            error: task[0].error,
            startTime: task[0].startTime,
            endTime: task[0].endTime
        });
    } catch (err) {
        logger.error(`Error in getting task status: ${err}`);
        res.status(500).json({ message: '获取任务状态失败' });
    }
});

export default router;