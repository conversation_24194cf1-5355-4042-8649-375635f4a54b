import express from 'express';
import logger from '../utils/logger';
import multer from 'multer';
import path from 'path';
import crypto from 'crypto';
import fs from 'fs';
import { Embeddings } from '@langchain/core/embeddings';
import { getEmbeddingModel } from '../lib/providers';
import { processDocument } from '../utils/documentProcessor';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { Document } from 'langchain/document';

const router = express.Router();

const splitter = new RecursiveCharacterTextSplitter({
  chunkSize: 500,
  chunkOverlap: 100,
});

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(process.cwd(), './uploads'));
  },
  filename: (req, file, cb) => {
    const splitedFileName = file.originalname.split('.');
    const fileExtension = splitedFileName[splitedFileName.length - 1];
    if (!['pdf', 'docx', 'txt'].includes(fileExtension)) {
      return cb(new Error('File type is not supported'), '');
    }
    cb(null, `${crypto.randomBytes(16).toString('hex')}.${fileExtension}`);
  },
});

const upload = multer({ storage });

router.post(
  '/',
  upload.fields([
    { name: 'files', maxCount: 10 },
  ]) as unknown as express.RequestHandler,
  async (req, res) => {
    try {
      const { embeddings = false } = req.body;

      const embeddingsModel = await getEmbeddingModel();
      if (!embeddingsModel) {
        res.status(400).json({ message: 'Failed to load embedding model' });
        return;
      }

      const files = req.files['files'] as Express.Multer.File[];
      if (!files || files.length === 0) {
        res.status(400).json({ message: 'No files uploaded' });
        return;
      }

      await Promise.all(
        files.map(async (file) => {
          const { docs, metadata } = await processDocument(file.path, file.originalname);

          const splitted = await splitter.splitDocuments(docs);

          const json = JSON.stringify({
            title: file.originalname,
            contents: splitted.map((doc) => doc.pageContent),
          });

          const pathToSave = file.path.replace(/\.\w+$/, '-extracted.json');
          fs.writeFileSync(pathToSave, json);

          if (embeddings) {
            const embeddings = await embeddingsModel.embedDocuments(
              splitted.map((doc) => doc.pageContent),
            );
  
            const embeddingsJSON = JSON.stringify({
              title: file.originalname,
              embeddings: embeddings,
            });
  
            const pathToSaveEmbeddings = file.path.replace(
              /\.\w+$/,
              '-embeddings.json',
            );
            fs.writeFileSync(pathToSaveEmbeddings, embeddingsJSON);
          }
        }),
      );

      res.status(200).json({
        files: files.map((file) => {
          return {
            fileName: file.originalname,
            fileExtension: file.filename.split('.').pop(),
            fileId: file.filename.replace(/\.\w+$/, ''),
          };
        }),
      });
    } catch (err: any) {
      logger.error(`Error in uploading file results: ${err.message}`);
      res.status(500).json({ message: 'An error has occurred.' });
    }
  },
);

export default router;
