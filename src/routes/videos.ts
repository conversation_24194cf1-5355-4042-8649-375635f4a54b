import express from 'express';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import logger from '../utils/logger';
import handleVideoSearch from '../chains/videoSearchAgent';
import { getSummarizerModel } from '../lib/providers';

const router = express.Router();

interface VideoSearchBody {
  query: string;
  chatHistory: any[];
}

router.post('/', async (req, res) => {
  try {
    let body: VideoSearchBody = req.body;

    const chatHistory = body.chatHistory.map((msg: any) => {
      if (msg.role === 'user') {
        return new HumanMessage(msg.content);
      } else if (msg.role === 'assistant') {
        return new AIMessage(msg.content);
      }
    });

    const llm = await getSummarizerModel();

    const videos = await handleVideoSearch(
      { chat_history: chatHistory, query: body.query },
      llm,
    );

    res.status(200).json({ videos });
  } catch (err) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(`Error in video search: ${err.message}`);
  }
});

export default router;
