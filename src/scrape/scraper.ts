import logger from '../utils/logger';
import axios from 'axios';
import { htmlToText } from 'html-to-text';
import pdfParse from 'pdf-parse';

export interface ScrapedContent {
  title: string;
  content: string;
  url: string;
  timestamp: string;
  tool?: 'axios' | 'error';
}

export class WebScraper {
  private static instance: WebScraper;

  public static getInstance(): WebScraper {
    if (!WebScraper.instance) {
      WebScraper.instance = new WebScraper();
    }
    return WebScraper.instance;
  }

  private cleanContent(content: string): string {
    return content
      .replace(/data:image\/[^;]+;base64,[^\s]+/g, '') // 移除 base64 图片
      .replace(/[ \t]+/g, ' ')  // 只处理水平空白字符
      .replace(/\n{3,}/g, '\n\n')  // 将连续3个以上换行符替换为2个
      .trim();
  }

  private isValidContent(content: string): boolean {
    if (!content || content.trim().length < 100) {
      return false;
    }

    const failureMarkers = [
      'Continue reading',
      'More for You',
      'login.microsoftonline.com',
      'Expand article logo',
      'Just a moment...',
      'Verify you are human',
      'Access denied',
      'Please enable JavaScript',
      'Robot check',
      'Subscribe to read'
    ];

    return !failureMarkers.some(marker => content.includes(marker));
  }

  private async fetchWithAxios(url: string, cookies?: { name: string; value: string; domain: string }[]): Promise<ScrapedContent> {
    const cookieHeader = cookies?.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

    const res = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: 15000,
      maxContentLength: 10 * 1024 * 1024,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      },
      validateStatus: (status) => status >= 200 && status < 300,
      maxRedirects: 5,
      ...(cookieHeader ? { 'Cookie': cookieHeader } : {})
    });

    if (res.headers['content-type']?.includes('application/pdf')) {
      const pdfText = await pdfParse(res.data);
      return {
        title: 'PDF Document',
        content: this.cleanContent(pdfText.text),
        url,
        timestamp: new Date().toISOString()
      };
    }

    const htmlContent = res.data.toString('utf8');
            
    if (htmlContent.includes('Just a moment...') || 
        htmlContent.includes('Verify you are human')) {
      throw new Error('遇到反爬虫验证，需要人工验证');
    }

    const content = htmlToText(htmlContent, {
      selectors: [{ selector: 'a', options: { ignoreHref: true } }],
    });
    
    const title = htmlContent.match(/<title>(.*?)<\/title>/)?.[1] || url;
    if (!this.isValidContent(content) || title.includes('Subscribe to read')) {
      throw new Error('页面内容为空、过短或包含无效内容');
    }

    logger.info(`Axios scraping success: ${url}`);
    
    return {
      title,
      content: this.cleanContent(content),
      url,
      timestamp: new Date().toISOString(),
      tool: 'axios'
    };
  }
  
  public async fetchContent(url: string, options: { 
    useAxios?: boolean;
    cookies?: { name: string; value: string; domain: string }[];
  } = {}): Promise<ScrapedContent> {
    const { 
      useAxios = true,
      cookies
    } = options;

    try {
      // 首先尝试使用 Axios
      if (useAxios) {
        try {
          return await this.fetchWithAxios(url, cookies);
        } catch (axiosError) {
          logger.info(`Axios failed: ${url}`);
        }
      }

      // 尝试其他抓取方法
      // TODO

      throw new Error('所有启用的抓取方法都失败了');
    } catch (error) {
      logger.error(`All scraping methods failed for ${url}`);
      throw error;
    }
  }
}