import { Document } from 'langchain/document';
import axios from 'axios';
import { TrainingThemeSearcher } from './trainingThemeSearcher';

interface GoodsItem {
  id: number;
  name: string;
  desc: string;
  type: number;
  keywords: string;
  thumb_url: string;
  price: number;
  origin_price: number;
  lesson_count: number;
  learn_count: number;
  cate_name: string;
}

interface NewsItem {
  id: number;
  type: number;
  title: string;
  desc: string;
  keywords: string;
  like_count: number;
  comment_count: number;
  thumb_url: string;
  publish_time: number;
  is_top: number;
}

interface HighLight {
  desc?: string;
  name?: string;
  keywords?: string;
}

interface SearchItem {
  id: number;
  item_id: string;
  type: number;
  name: string;
  keywords: string;
  desc: string;
  high_light: HighLight;
  goods_item: GoodsItem | null;
  news_item: NewsItem | null;
  vlog_item: null;
  expert_item: null;
  chapter_item: null;
}

interface SearchResponse {
  code: number;
  data: {
    list: SearchItem[];
    total: number;
  };
  message: string;
  request_id: string;
}

interface SearchRequest {
  page: number;
  limit: number;
  text: string;
  types: string;
}

/**
 * 额外搜索服务
 * 处理教目平台的搜索请求
 */
export class GoodsSearcher {
  private static readonly API_URL = 'https://user-api.cyjiaomu.com/api/v2/search';
  private static readonly DEFAULT_LIMIT = 10;

  /**
   * 执行搜索并返回处理后的文档
   * @param query 搜索关键词
   * @param page 页码
   * @param limit 每页数量
   */
  async search(query: string, page: number = 1, limit: number = GoodsSearcher.DEFAULT_LIMIT, types: string = "1,2"): Promise<Document[]> {
    try {
      const searchRequest: SearchRequest = {
        page,
        limit,
        text: query,
        types,
      };

      const response = await axios.post<SearchResponse>(
        GoodsSearcher.API_URL,
        searchRequest
      );

      return this.convertAdditionalResults(response.data);
    } catch (error) {
      console.error('Additional search failed:', error);
      return [];
    }
  }

  /**
   * 根据条件返回商品信息或训练卡片信息
   * @param query 搜索关键词
   * @param recommendType 返回类型：'goods' 或 'training'
   */
  async fetchGoodsOrTrainingCards(query: string, recommendType: 'goods' | 'training' = 'goods'): Promise<Document[]> {
    if (recommendType === 'goods') {
      // 调用 GoodsSearcher 获取商品信息
      return this.search(query);
    } else if (recommendType === 'training') {
      // 调用 TrainingThemeSearcher 获取训练主题卡片（每页20个，随机6个）
      const trainingSearcher = new TrainingThemeSearcher();
      return trainingSearcher.search(1, 20, 6);
    } else {
      return [];
    }
  }

  /**
     * 将额外搜索结果转换为 Document 格式
     */
  private convertAdditionalResults(results: SearchResponse): Document[] {
    if (!results?.data?.list?.length) return [];
    
    const documents = results.data.list.map(item => {
        let content = '';
        let metadata: Record<string, any> = {
            id: item.id,
            item_id: item.item_id,
            type: item.type,
            keywords: item.keywords,
            source: 'guanyong_goods',
        };

        // 根据不同类型处理内容
        if (item.goods_item) {
            content = `${item.goods_item.name}\n${item.goods_item.desc}`;
            metadata = {
                ...metadata,
                title: item.goods_item.name,
                price: item.goods_item.price,
                thumb_url: item.goods_item.thumb_url,
                lesson_count: item.goods_item.lesson_count,
                learn_count: item.goods_item.learn_count,
                cate_name: item.goods_item.cate_name,
            };
        } else if (item.news_item) {
            content = `${item.news_item.title}\n${item.news_item.desc}`;
            metadata = {
                ...metadata,
                title: item.news_item.title,
                thumb_url: item.news_item.thumb_url,
                like_count: item.news_item.like_count,
                comment_count: item.news_item.comment_count,
                publish_time: item.news_item.publish_time,
            };
        } else {
            // 使用默认字段
            content = `${item.name}\n${item.desc}`;
            metadata.title = item.name;
        }

        // 添加高亮信息（如果存在）
        if (item.high_light) {
            metadata.highlight = item.high_light;
        }

        return new Document({
            pageContent: content,
            metadata: metadata,
        });
    });

    return documents.sort((a, b) => {
        const typeA = a.metadata.type as number;
        const typeB = b.metadata.type as number;
        
        if (typeA === typeB) {
            return 0; // 类型相同时保持原有顺序
        }
        
        return typeA === 1 ? -1 : 1; // type=1 排在前面
    });
  }
}
