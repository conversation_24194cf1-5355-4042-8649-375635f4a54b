import { Document } from 'langchain/document';
import axios from 'axios';
import { KnowledgeChannel, KNOWLEDGE_CONFIGS } from '../config/knowledge';

// RAGFlow 接口定义
interface RAGFlowResponse {
  code: number;
  data: {
    chunks: Array<{
      content: string;
      document_id: string;
      document_keyword: string;
      highlight: string;
      similarity: number;
    }>;
  };
}

interface KnowledgeResult {
  metadata: {
    document_id: string;
    score: number;
  };
  title: string;
  content: string;
}

interface DifyResponse {
  task_id: string;
  workflow_run_id: string;
  data: {
    id: string;
    status: string;
    outputs: {
      result: KnowledgeResult[];
    };
    error: string | null;
  };
}

/**
 * 知识库搜索服务
 * 处理知识库的搜索请求
 */
export class KnowledgeSearcher {
  private static readonly API_CONFIGS = KNOWLEDGE_CONFIGS;

  private static getApiConfig(channel: KnowledgeChannel) {
    return KnowledgeSearcher.API_CONFIGS[channel];
  }

  /**
   * 执行搜索并返回处理后的文档
   * @param query 搜索关键词
   * @param userId 用户ID
   * @param channel 知识库渠道
   */
  async search(
    query: string, 
    userId: string = 'default-user',
    channel: KnowledgeChannel = 'knowledgeFromDify'
  ): Promise<Document[]> {
    try {
      const apiConfig = KnowledgeSearcher.getApiConfig(channel);
      
      // 根据渠道选择不同的搜索方法
      if (channel.includes('RAGFlow')) {
        try {
          const results = await this.searchRAGFlow(query, apiConfig);
          if (results.length > 0) {
            return results;
          }
          // RAGFlow 搜索失败或无结果时，使用 Dify 作为兜底
          console.log('RAGFlow search failed or empty, falling back to Dify');
          const difyConfig = KnowledgeSearcher.getApiConfig('knowledgeFromDify');
          return this.searchDify(query, userId, difyConfig);
        } catch (error) {
          console.error('RAGFlow search error, falling back to Dify:', error);
          const difyConfig = KnowledgeSearcher.getApiConfig('knowledgeFromDify');
          return this.searchDify(query, userId, difyConfig);
        }
      } else {
        return this.searchDify(query, userId, apiConfig);
      }
    } catch (error) {
      console.error(`Knowledge search failed for channel ${channel}:`, error);
      return [];
    }
  }

  private async searchRAGFlow(query: string, apiConfig: typeof KNOWLEDGE_CONFIGS.knowledgeFromRAGFlow): Promise<Document[]> {
    const response = await axios.post<RAGFlowResponse>(
      apiConfig.url,
      {
        question: query,
        dataset_ids: apiConfig.dataset_ids,
        top_k: apiConfig.top_k || 8,
        similarity_threshold: apiConfig.similarity_threshold || 0.2,
        vector_similarity_weight: apiConfig.vector_similarity_weight || 0.3,
        rerank_id: apiConfig.rerank_id || ''
      },
      {
        headers: {
          'Authorization': `Bearer ${apiConfig.key}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000  // 5 秒超时
      }
    );

    return this.convertRAGFlowToDocuments(response.data);
  }

  private async searchDify(
    query: string, 
    userId: string,
    apiConfig: typeof KNOWLEDGE_CONFIGS.knowledgeFromDify
  ): Promise<Document[]> {
    const response = await axios.post<DifyResponse>(
      apiConfig.url,
      {
        inputs: { query },
        response_mode: "blocking",
        user: userId
      },
      {
        headers: {
          'Authorization': `Bearer ${apiConfig.key}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return this.convertDifyToDocuments(response.data);
  }

  /**
   * 将搜索结果转换为 Document 格式
   */
  private convertRAGFlowToDocuments(response: RAGFlowResponse): Document[] {
    console.log(response);
    if (response.code !== 0 || !response.data.chunks?.length) {
      return [];
    }

    const documents = response.data.chunks.map(chunk => new Document({
      pageContent: chunk.content,
      metadata: {
        title: "灵通管理经验与方法论",
        document_id: chunk.document_id,
        score: chunk.similarity,
        source: 'knowledge_base',
        document_keyword: chunk.document_keyword
      }
    }));

    return documents.sort((a, b) => 
      (b.metadata.score as number) - (a.metadata.score as number)
    );
  }

  private convertDifyToDocuments(response: DifyResponse): Document[] {
    if (response.data.status !== 'succeeded' || 
        !response.data.outputs?.result?.length) {
        return [];
    }

    const documents = response.data.outputs.result.map(item => new Document({
        pageContent: item.content,
        metadata: {
            title: "灵通管理经验与方法论",
            document_id: item.metadata.document_id,
            score: item.metadata.score,
            source: 'knowledge_base'
        }
    }));

    return documents.sort((a, b) => 
        (b.metadata.score as number) - (a.metadata.score as number)
    );
  }

  private cleanTitle(title: string): string {
    if (!title) return '';
    
    // 移除所有特殊格式并按步骤处理
    return title
        // 移除 [优] 标记
        .replace(/\[优\]/g, '')
        // 移除文件名中的下划线及其后面的数字
        .replace(/_\d+/g, '')
        // 移除文件扩展名
        .replace(/\.md$/i, '')
        // 移除多余空格
        .trim();
  }
}