import { ChatOpenAI } from '@langchain/openai';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { Embeddings } from '@langchain/core/embeddings';
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
  PromptTemplate,
} from '@langchain/core/prompts';
import {
  RunnableLambda,
  RunnableMap,
  RunnableSequence,
} from '@langchain/core/runnables';
import { BaseMessage } from '@langchain/core/messages';
import { StringOutputParser } from '@langchain/core/output_parsers';
import LineListOutputParser from '../lib/outputParsers/listLineOutputParser';
import LineOutputParser from '../lib/outputParsers/lineOutputParser';
import { getDocumentsFromLinks } from '../utils/documents';
import { Document } from 'langchain/document';
import { searchSearxng } from '../lib/searxng';
import formatChatHistoryAsString from '../utils/formatHistory';
import eventEmitter from 'events';
import { StreamEvent } from '@langchain/core/tracers/log_stream';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { generateSummaryPrompt } from '../prompts/summarizer';
import { DocumentReranker } from './documentReranker';
import { KnowledgeSearcher } from './knowledgeSearcher';
import { KnowledgeChannel } from '../config/knowledge';

export interface MetaSearchAgentType {
  searchAndAnswer: (
    message: string,
    history: BaseMessage[],
    llm: BaseChatModel,
    summarizerLLM: BaseChatModel,  // 添加总结模型参数
    embeddings: Embeddings,
    optimizationMode: 'speed' | 'balanced' | 'quality',
    fileIds: string[],
  ) => Promise<eventEmitter>;
}

interface Config {
  searchWeb: boolean;
  searchKnowledge: boolean;
  knowledgeChannel: KnowledgeChannel;
  rerank: boolean;
  summarizer: boolean;
  rerankThreshold: number;
  queryGeneratorPrompt: string;
  responsePrompt: string;
  activeEngines: string[];
}

type BasicChainInput = {
  chat_history: BaseMessage[];
  query: string;
};

interface SearchResult {
  query: string;
  docs: Document[];
}

export class MetaSearchAgent implements MetaSearchAgentType {
  private config: Config;
  private userId: string;
  private strParser = new StringOutputParser();

  private documentReranker: DocumentReranker;
  private knowledgeSearcher: KnowledgeSearcher;
  
  private currentChatHistory: BaseMessage[] = [];

  private static readonly MAX_DOCS_PER_URL = 20; // 每个 URL 允许的最大文档数量

  constructor(config: Config, userId: string = 'default-user') {
    this.config = config;
    this.userId = userId;
    this.documentReranker = new DocumentReranker(config.rerankThreshold);
    this.knowledgeSearcher = new KnowledgeSearcher();
  }

  private async createQueryRewriterChain(summarizerLLM: BaseChatModel) {
    // 设置总结模型的温度为0，确保输出的一致性
    (summarizerLLM as unknown as ChatOpenAI).temperature = 0;

    return RunnableSequence.from([
      PromptTemplate.fromTemplate(this.config.queryGeneratorPrompt),
      summarizerLLM,
      this.strParser,
      RunnableLambda.from(async (input: string) => {
        // 解析问题
        const { links, question } = await this.parseInputContent(input);

        // 如果是not_needed，返回空字符串
        if (question === 'not_needed') {
          return { links: [], question: '' };
        }

        // 返回重述后的问题
        return { links, question };
      }),
    ]);
  }

  // 解析输入内容，提取链接和问题
  private async parseInputContent(input: string) {
    const linksOutputParser = new LineListOutputParser({ key: 'links' });
    const questionOutputParser = new LineOutputParser({ key: 'question' });

    const [links, parsedQuestion] = await Promise.all([
      linksOutputParser.parse(input),
      this.config.summarizer ? questionOutputParser.parse(input) : input,
    ]);

    return { links, question: parsedQuestion };
  }

  // 处理链接模式
  private async handleLinksMode(
    links: string[],
    question: string,
    summarizerLLM: BaseChatModel
  ): Promise<SearchResult> {
    // 如果问题为空，默认使用总结模式
    const finalQuestion = question.length === 0 ? 'summarize' : question;
    
    // 获取链接文档
    const linkDocs = await getDocumentsFromLinks({ links });
    
    // 对文档进行分组处理
    const docGroups = await this.groupDocumentsByUrl(linkDocs);
    
    // 处理文档内容
    const processedDocs = await this.processDocGroups(docGroups, finalQuestion, summarizerLLM);

    return { 
      query: finalQuestion, 
      docs: processedDocs 
    };
  }

  // 按URL对文档进行分组
  private async groupDocumentsByUrl(docs: Document[]): Promise<Document[]> {
    const docGroups: Document[] = [];

    docs.forEach((doc) => {
      const URLDocExists = docGroups.find(
        (d) =>
          d.metadata.url === doc.metadata.url &&
          d.metadata.totalDocs < MetaSearchAgent.MAX_DOCS_PER_URL,
      );

      if (!URLDocExists) {
        docGroups.push({
          ...doc,
          metadata: {
            ...doc.metadata,
            totalDocs: 1,
          },
        });
        return;
      }

      const docIndex = docGroups.findIndex(
        (d) =>
          d.metadata.url === doc.metadata.url &&
          d.metadata.totalDocs < MetaSearchAgent.MAX_DOCS_PER_URL,
      );

      if (docIndex !== -1) {
        docGroups[docIndex].pageContent += `\n\n${doc.pageContent}`;
        docGroups[docIndex].metadata.totalDocs += 1;
      }
    });

    return docGroups;
  }

  // 处理文档组
  private async processDocGroups(
    docGroups: Document[], 
    question: string,
    summarizerLLM: BaseChatModel
  ): Promise<Document[]> {
    return Promise.all(
      docGroups.map(async (doc) => {
        return new Document({
          pageContent: doc.pageContent,
          metadata: {
            title: doc.metadata.title,
            url: doc.metadata.url,
          },
        });
      })
    );
  }

  // 处理搜索模式
  private async handleSearchMode(question: string, history: BaseMessage[] = []): Promise<SearchResult> {
    const res = await searchSearxng(question, {
      language: 'zh',
      engines: this.config.activeEngines,
      pageno: 1
    });

    const documents = res.results.map((result) => 
      new Document({
        pageContent: this.getSearchResultContent(result),
        metadata: {
          title: result.title,
          url: result.url,
          ...(result.img_src && { img_src: result.img_src }),
        },
      })
    );

    return { query: question, docs: documents };
  }

  // 获取搜索结果内容
  private getSearchResultContent(result: any): string {
    return result.content || 
      (this.config.activeEngines.includes('youtube') ? result.title : '');
  }

  private async createAnsweringChain(
    llm: BaseChatModel,
    summarizerLLM: BaseChatModel,
    fileIds: string[],
    embeddings: Embeddings,
    optimizationMode: 'speed' | 'balanced' | 'quality',
  ) {
    return RunnableSequence.from([
      RunnableMap.from({
        query: (input: BasicChainInput) => input.query,
        chat_history: (input: BasicChainInput) => {
          // 保存当前聊天历史供后续使用
          this.currentChatHistory = input.chat_history;
          return input.chat_history;
        },
        date: () => new Date().toISOString(),
        context: RunnableLambda.from(async (input: BasicChainInput) => {
          const processedHistory = formatChatHistoryAsString(
            input.chat_history,
          );

          
          let docs: Document[] | null = null;
          let query = input.query;
          console.log('原始 Query:', query);
          
          // 创建问题重写链
          const queryRewriterChain = await this.createQueryRewriterChain(summarizerLLM);

          // 使用问题重写链处理查询
          let rewrittenQuery = '';
          if (this.config.queryGeneratorPrompt !== '') {
            const { links, question } = await queryRewriterChain.invoke({
              chat_history: processedHistory,
              query,
            });
            
            // 处理链接模式
            if (links.length > 0) {
              console.log('处理链接模式：', links);
              const linkResult = await this.handleLinksMode(links, query, summarizerLLM);
              docs = linkResult.docs;
              query = linkResult.query;
              
              // 直接返回处理后的文档，跳过后续搜索步骤
              const sortedDocs = await this.documentReranker.rerankDocs(
                query,
                docs,
                fileIds,
                embeddings,
                optimizationMode,
                []  // 链接模式下不使用知识库文档
              );
              
              return sortedDocs;
            }

            if (question) {
              rewrittenQuery = question;
              console.log('优化后的 Query:', rewrittenQuery);
            }
          }

          // 处理网络搜索
          if (this.config.searchWeb && rewrittenQuery) {
            // 执行网络搜索
            const searchResult = await this.handleSearchMode(rewrittenQuery, input.chat_history);
            docs = searchResult.docs;
          }

          // 获取知识库搜索结果
          let knowledgeDocs = [];
          if (this.config.searchKnowledge && rewrittenQuery) {
            knowledgeDocs = await this.knowledgeSearcher.search(rewrittenQuery, this.userId, this.config.knowledgeChannel);
          }

          // 传入知识库文档进行重排序
          const sortedDocs = await this.documentReranker.rerankDocs(
            rewrittenQuery,
            docs ?? [],
            fileIds,
            embeddings,
            optimizationMode,
            knowledgeDocs
          );

          return sortedDocs;
        })
          .withConfig({
            runName: 'FinalSourceRetriever',
          })
          .pipe(this.processDocs),
      }),
      
      ChatPromptTemplate.fromMessages([
        ['system', (() => {
          // console.log('Response Prompt:', this.config.responsePrompt);
          return this.config.responsePrompt;
        })()],
        new MessagesPlaceholder('chat_history'),
        ['user', '{query}'],
      ]),
      llm,
      this.strParser,
    ]).withConfig({
      runName: 'FinalResponseGenerator',
    });
  }

  private processDocs(docs: Document[]) {
    return docs
      .map(
        (_, index) =>
          `${index + 1}. ${docs[index].metadata.title} ${docs[index].pageContent}`,
      )
      .join('\n');
  }

  private async handleStream(
    stream: IterableReadableStream<StreamEvent>,
    emitter: eventEmitter,
  ) {
    for await (const event of stream) {
      if (
        event.event === 'on_chain_end' &&
        event.name === 'FinalSourceRetriever'
      ) {
        emitter.emit(
          'data',
          JSON.stringify({ type: 'sources', data: event.data.output }),
        );
      }
      // 处理所有 LLM 的流式输出
      if (event.event === 'on_llm_stream') {
        const message = event.data.chunk.message;
        // 处理 ChatDeepSeek 的推理内容
        if (event.name === 'ChatDeepSeek' && message?.additional_kwargs?.reasoning_content) {
          emitter.emit(
            'data',
            JSON.stringify({
              type: 'reasoning',
              data: message.additional_kwargs.reasoning_content,
            }),
          );
        }
        // 处理所有模型的常规内容
        if (event.data.chunk.text) {
          emitter.emit(
            'data',
            JSON.stringify({ type: 'response', data: event.data.chunk.text }),
          );
        } else if (message?.content) {
          emitter.emit(
            'data',
            JSON.stringify({ type: 'response', data: message.content }),
          );
        }
      }
      if (
        event.event === 'on_chain_end' &&
        event.name === 'FinalResponseGenerator'
      ) {
        emitter.emit('end');
      }
    }
  }

  async searchAndAnswer(
    message: string,
    history: BaseMessage[],
    llm: BaseChatModel,
    summarizerLLM: BaseChatModel,  // 添加 summarizerLLM 参数
    embeddings: Embeddings,
    optimizationMode: 'speed' | 'balanced' | 'quality',
    fileIds: string[],
  ) {
    const emitter = new eventEmitter();

    const answeringChain = await this.createAnsweringChain(
      llm,
      summarizerLLM,  // 传入 summarizerLLM
      fileIds,
      embeddings,
      optimizationMode,
    );

    const stream = answeringChain.streamEvents(
      {
        chat_history: history,
        query: message,
      },
      {
        version: 'v1',
      },
    );

    this.handleStream(stream, emitter);

    return emitter;
  }
}

export default MetaSearchAgent;
