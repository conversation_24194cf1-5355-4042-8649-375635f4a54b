import { Document } from 'langchain/document';
import { getJMBaseURL } from '../config';

interface TrainingItem {
  id: number; // 训练ID
  title: string; // 主标题
  sub_title: string; // 子标题
  in_process: boolean; // 是否进行中
  is_free: boolean; // 是否限免
  ai_visible: boolean; // 是否适用于AI
  ai_star: number; // 星级
}

interface TrainingListResponse {
  total: number;
  list: TrainingItem[];
}

export class TrainingThemeSearcher {
  private static readonly API_VERSION = 'v=1.0';
  private static readonly API_PATH = '/api/v/training/list';
  private static readonly DEFAULT_LIMIT = 20;

  /**
   * 获取训练主题列表并转为 Document[]
   * @param page 页码，默认1
   * @param limit 每页数量，默认20
   * @param sampleCount 随机抽取数量，默认6
   */
  async search(page: number = 1, limit: number = TrainingThemeSearcher.DEFAULT_LIMIT, sampleCount: number = 6): Promise<Document[]> {
    try {
      const requestBody = {
        page,
        limit
        // cate_id, module_id 可不传
      };

      const baseUrl = getJMBaseURL();
      const url = `${baseUrl}${TrainingThemeSearcher.API_PATH}?${TrainingThemeSearcher.API_VERSION}`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`训练主题列表请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as { data: TrainingListResponse };
      const data = result.data;
      if (!data?.list?.length) return [];

      // 随机抽取 sampleCount 个
      const shuffled = data.list.slice().sort(() => Math.random() - 0.5);
      const sampled = shuffled.slice(0, sampleCount);

      return sampled.map(item => new Document({
        pageContent: item.sub_title || '',
        metadata: {
          id: item.id,
          title: item.title,
          sub_title: item.sub_title,
          in_process: item.in_process,
          is_free: item.is_free,
          ai_visible: item.ai_visible,
          ai_star: item.ai_star,
          training_id: item.id
        }
      }));
    } catch (error) {
      console.error('Training theme search failed:', error);
      return [];
    }
  }
}
