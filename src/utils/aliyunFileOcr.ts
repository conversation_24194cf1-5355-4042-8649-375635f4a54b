import { sleep } from ".";
import cfg from "../config";

const Client = require('@alicloud/docmind-api20220711');

export default class OcrFileClient {
    static createClient() {
        const cred = {
            credential: {
                // 凭证类型。
                type: 'access_key',
                // 设置为AccessKey ID值。
                accessKeyId: cfg.FILE_OCR.ALIBABA_CLOUD_ACCESS_KEY_ID,
                // 设置为AccessKey Secret值。
                accessKeySecret: cfg.FILE_OCR.ALIBABA_CLOUD_ACCESS_KEY_SECRET,
            }
        };
        // 调用接口时，程序直接访问凭证，读取您的访问密钥（即AccessKey）并自动完成鉴权。
        // 运行本示例前，请先完成步骤二：配置身份认证。
        // 本示例使用默认配置文件方式，通过配置Credentials文件创建默认的访问凭证。
        // 使用默认凭证初始化Credentials Client。
        const client = new Client.default({
            // 访问的域名，支持IPv4和IPv6两种方式，IPv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com。
            endpoint: 'docmind-api.cn-hangzhou.aliyuncs.com',
            // 通过credentials获取配置中的AccessKey ID。
            accessKeyId: cred.credential.accessKeyId,
            // 通过credentials获取配置中的AccessKey Secret。
            accessKeySecret: cred.credential.accessKeySecret,
            // 若您需要识别的文件为大文件,耗时较长。您可设置以下connectTimeout和readTimeout属性
            // 建立连接超时时间
            connectTimeout: 60000,
            // 读取资源超时时间
            readTimeout: 60000,
            type: 'access_key',
            regionId: 'cn-hangzhou'
        });
        return client;
    }

    static async handleAliyunOcr({ url }: any) {
        // const text = await OcrFileClient.handleAliyunOcrDigitalDoc({ url }); // 电子文档解析 0.005元/页
        const text = await OcrFileClient.handleAliyunOcrLargeModel({ url }); // 文档解析 （大模型版） 0.02元/页
        // const text = await OcrFileClient.handleAliyunOcrSubmitDoc({ url }); // 文档智能解析 0.25元/页
        return text;
    }

    // 文档智能解析
    static async handleAliyunOcrSubmitDoc({ url }: any) {
        const client = OcrFileClient.createClient() as any;
        const request = new Client.SubmitDocStructureJobRequest();
        request.fileName = 'example.pdf';
        request.fileUrl = url;
        const response = await client.submitDocStructureJob(request);
        const text = await OcrFileClient.getOcrResultSubmitDoc({ id: response.body.data.id });
        return text;
    }

    static async getOcrResultSubmitDoc({ id }) {
        return new Promise(async (resolve, reject) => {
            try {
                while (true) {
                    const res = await OcrFileClient.getResultSubmitDoc(id) as any;
                    if (res.completed) {
                        const text = OcrFileClient.getLayoutText(res.data);
                        resolve(text);
                        return;
                    }
                    // 休眠时间
                    await sleep(3000);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    static async getResultSubmitDoc(id: string) {
        const client = OcrFileClient.createClient() as any;
        const resultRequest = new Client.GetDocStructureResultRequest();
        resultRequest.id = id;
        const response = await client.getDocStructureResult(resultRequest);

        return response.body;
    }

    // 电子文档解析
    static async handleAliyunOcrDigitalDoc({ url }: any) {
        const client = OcrFileClient.createClient() as any;
        const request = new Client.SubmitDigitalDocStructureJobRequest();
        request.fileName = 'example.pdf';
        request.fileUrl = url;
        const response = await client.submitDigitalDocStructureJob(request);
        const text = await OcrFileClient.getLayoutText(response.body.data);
        return text;
    }

    static async getResultDigitalDoc(id: string) {
        // 使用默认凭证初始化Credentials Client
        const client = OcrFileClient.createClient() as any;
        const resultRequest = new Client.GetDocStructureResultRequest();
        resultRequest.id = id;
        const response = await client.getDocStructureResult(resultRequest);
        return response.body;
    }

    // 文档解析 （大模型版）
    static async handleAliyunOcrLargeModel({ url }: any) {
        const client = OcrFileClient.createClient() as any;
        const request = new Client.SubmitDocParserJobRequest();
        request.fileName = 'example.pdf';
        request.fileUrl = url;
        const response = await client.submitDocParserJob(request);
        return await OcrFileClient.getOcrResultLargeModel({ id: response.body.data.id });
    }

    static getLayoutText(res: any) {
        let text = '';
        if (res?.layouts) {
            const layouts = res.layouts;
            layouts.forEach(item => {
                text += item.text + '\n';
            });
        }
        return text;
    }

    static async getOcrResultLargeModel({ id }) {
        return new Promise(async (resolve, reject) => {
            try {
                while (true) {
                    const queryRes = await OcrFileClient.queryResultLargeModel(id) as any;
                    if (queryRes.status === 'success') {
                        let layoutNum: number = 0;
                        const resText = []
                        while (true) {
                            const res = await OcrFileClient.getResultLargeModel(id, layoutNum) as any;
                            const text = OcrFileClient.getLayoutText(res);
                            if (!text) {
                                break;
                            }
                            resText.push(text);
                            layoutNum += 3000;
                        }
                        resolve(resText.length > 0 ? resText.join('\n') : '');
                        return;
                    }
                    // 休眠时间
                    await sleep(3000);
                }
            } catch (error) {
                reject(error);
            }
        });
    }


    static async queryResultLargeModel(id: string) {
        const client = OcrFileClient.createClient() as any;
        const resultRequest = new Client.QueryDocParserStatusRequest();
        resultRequest.id = id;
        const response = await client.queryDocParserStatus(resultRequest);

        return response.body.data;
    }

    static async getResultLargeModel(id: string, layoutNum = 0) {
        const client = OcrFileClient.createClient() as any;
        const resultRequest = new Client.GetDocParserResultRequest();
        resultRequest.id = id;
        resultRequest.layoutStepSize = 3000;
        resultRequest.layoutNum = layoutNum;
        const response = await client.getDocParserResult(resultRequest);
        return response.body.data;
    }
}