import cfg from "../config";

// This file is auto-generated, don't edit it
// 依赖的模块可通过下载工程中的模块依赖文件或右上角的获取 SDK 依赖信息查看
const ocr_api20210707 = require('@alicloud/ocr-api20210707');
const OpenApi = require('@alicloud/openapi-client');
const Util = require('@alicloud/tea-util');

export default class Client {

    /**
     * 使用AK&SK初始化账号Client
     * @return Client
     * @throws Exception
     */
    static createClient() {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378664.html。
        let config = new OpenApi.Config({
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
            accessKeyId: cfg.OCR.ALIBABA_CLOUD_ACCESS_KEY_ID,
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
            accessKeySecret: cfg.OCR.ALIBABA_CLOUD_ACCESS_KEY_SECRET,
        });
        console.log("🚀 ~ Client ~ createClient ~ config:", config)
        // Endpoint 请参考 https://api.aliyun.com/product/ocr-api
        config.endpoint = `ocr-api.cn-hangzhou.aliyuncs.com`;
        return new ocr_api20210707.default(config);
    }

    // 阿里云OCR识别 https://help.aliyun.com/zh/ocr/developer-reference/api-ocr-api-2021-07-07-recognizealltext?scm=20140722.S_help%40%40%E6%96%87%E6%A1%A3%40%402629927.S_BB2%40bl%2BRQW%40ag0%2BBB1%40ag0%2Bos0.ID_2629927-RL_RecognizeAllTextRequest-LOC_doc%7EUND%7Eab-OR_ser-PAR1_2102029c17458296541338013d68d1-V_4-P0_1-P1_0&spm=a2c4g.11186623.help-search.i10
    static async handleAliyunOcr({ url = '', type = 'Advanced' }) {
        let client = Client.createClient();
        let recognizeAllTextRequest = new ocr_api20210707.RecognizeAllTextRequest({ url, type });
        let runtime = new Util.RuntimeOptions({});
        try {
            let resp = await client.recognizeAllTextWithOptions(recognizeAllTextRequest, runtime);
            console.log("🚀 ~ Client ~ handleAliyunOcr ~ resp:", resp.body.data.content)
            return resp.body.data.content;
        } catch (error) {
            console.log("🚀 ~ Client ~ handleAliyunOcr ~ error:", error)
        }
        return null;
    }
}