import logger from './logger';
import { db } from '../db';
import { eq, and } from 'drizzle-orm';
import {  messages } from '../db/schema';
import { getJMBaseURL, getPaytEnable, getPayChannels, getPayPointsByType , TaskType} from '../config';
interface DeductBillingParams {
  chatId: string; // 聊天ID
  channelId: string; // 消息渠道
  userId?: string; // 用户ID
  taskType?: TaskType; // 任务类型
}

interface BillingResponse {
  code: number;
  data: {
    points: number;
  };
  message: string;
  request_id: string;
}

interface MemberResponse {
  code: number;
  data: {
    info: {
      valid: boolean;
      expire_time: number;
    }
  };
  message: string;
  request_id: string;
}

export class BillingUtils {
  private static readonly API_VERSION = 'v=1.0';
  private static readonly SECRET = '92d695bb9881530250208d525c422965';

  /**
   * 查询用户电量
   * @param userId 用户ID
   * @returns 用户当前电量
   */
  public static async getUserPoints(userId: string): Promise<number> {
    try {
      if (!userId) {
        logger.warn('查询电量请求缺少用户ID');
        return 0;
      }

      const baseUrl = getJMBaseURL();
      const response = await fetch(`${baseUrl}/api/v/training/user/assets/ai?${this.API_VERSION}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Secret': this.SECRET,
        },
        body: JSON.stringify({ 
          user_id:  parseInt(`${userId}`, 10) // 转换为数字类型
        }),
      });

      if (!response.ok) {
        throw new Error(`查询电量请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as BillingResponse;
      
      if (result.code === 0) {
        return result.data.points;
      } else {
        logger.warn(`用户查询电量失败 - userId: ${userId}, reason: ${result.message}`);
        return 0;
      }
    } catch (error) {
      logger.error(`查询电量过程发生错误: ${error.message}`);
      return 0;
    }
  }

/* 任务类型到数字的映射，供接口使用
  1. AI搜索(webSearch) 2.管理大师(managementMaster) 3.简历分析(resumeAnalyzer) 4. AI陪练(careerCoach) 5. AI灵感(careerCoachAdvise)
*/
private static getAgentTypeNumber(taskType: TaskType): number {
  const agentTypeMap: Record<TaskType, number> = {
    webSearch: 1,
    managementMaster: 2,
    resumeAnalyzer: 3,
    careerCoach: 4,
    careerCoachAdvise: 5
  };
  return agentTypeMap[taskType] || 1; // 默认返回1（AI搜索）
}

  /**
   * 扣减用户费用
   * @param params 扣除电量参数
   * @returns 是否扣除电量成功
   */
  public static async deductUserBilling(params: DeductBillingParams): Promise<boolean> {
    try {
      const { chatId, channelId, userId, taskType } = params;
      
      // 检查是否是允许的扣费渠道
      const allowedChannels = getPayChannels();
      if (!allowedChannels.includes(channelId)) {
        logger.warn(`非允许的扣费渠道 - channelId: ${channelId}`);
        return false;
      }
      
      // 如果没有用户ID，记录日志并返回
      if (!userId) {
        logger.warn(`扣除电量请求缺少用户ID，source: ${channelId}`);
        return false;
      }

      // 计算需要扣除的点数
      const points = await this.calculateDeductPoints(chatId, taskType);
      logger.info(`用户扣除电量 - userId: ${userId}, channelId: ${channelId}, taskType: ${taskType}, points: ${points}`);
      
      // 验证扣减点数必须大于0
      if (points <= 0) {
        return false;
      }

      const baseUrl = getJMBaseURL();
      const response = await fetch(`${baseUrl}/api/v/training/user/cost/ai?${this.API_VERSION}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Secret': this.SECRET,
        },
        body: JSON.stringify({
          user_id: parseInt(`${userId}`, 10), // 转换为数字类型
          points,
          agent_type: this.getAgentTypeNumber(taskType),
        }),
      });

      if (!response.ok) {
        throw new Error(`扣除电量请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as BillingResponse;
      
      if (result.code === 0) {
        logger.info(`用户扣除电量成功 - userId: ${userId}, channelId: ${channelId}, taskType: ${taskType}, points: ${points}`);
        return true;
      } else {
        logger.warn(`用户扣除电量失败 - userId: ${userId}, channelId: ${channelId}, taskType: ${taskType}, reason: ${result.message}`);
        return false;
      }
    } catch (error) {
      logger.error(`扣除电量过程发生错误: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查用户电量是否足够
   * @param userId 用户ID
   * @param channelId 通道ID
   * @param taskType 任务类型（可选）
   * @returns 返回包含是否足够和剩余电量的对象
   */
  public static async checkUserPoints(userId: string, channelId: string, taskType?: TaskType): Promise<{
    isEnough: boolean;
    remainingPoints: number;
    available: boolean;
  }> {
    try {
      // 检查扣费功能是否启用
      const deductEnable = getPaytEnable();
      const deductChannels = getPayChannels();
      const canDeduct = deductEnable && deductChannels.includes(channelId);

      if (!canDeduct) {
        logger.info(`扣费功能未启用或非允许渠道 - channelId: ${channelId}`);
        return { isEnough: true, remainingPoints: 0, available: false };
      }

      if (!userId) {
        logger.warn('检查电量请求缺少用户ID');
        // 用户id为空时，功能可用
        return { isEnough: true, remainingPoints: 0, available: false };
      }

      // 获取当前用户电量
      const currentPoints = await this.getUserPoints(userId);
      
      // 获取任务需要的电量，如果taskType为空则默认消耗0点
      const requiredPoints = taskType ? getPayPointsByType(taskType) : 0;

      return {
        isEnough: currentPoints >= requiredPoints,
        remainingPoints: currentPoints,
        available: true
      };
    } catch (error) {
      logger.error(`检查电量过程发生错误: ${error.message}`);
      return { isEnough: false, remainingPoints: 0, available: false };
    }
  }

  /**
   * 查询用户会员信息
   * @param userId 用户ID
   * @returns 会员信息
   */
  public static async getUserMember(userId: string): Promise<{
    valid: boolean;
    expire_time: number;
  }> {
    try {
      if (!userId) {
        logger.warn('查询会员信息请求缺少用户ID');
        return {
          valid: false,
          expire_time: 0
        };
      }

      const baseUrl = getJMBaseURL();
      const response = await fetch(`${baseUrl}/api/v/training/user/member?${this.API_VERSION}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Secret': this.SECRET,
        },
        body: JSON.stringify({ 
          user_id: parseInt(`${userId}`, 10)
        }),
      });

      if (!response.ok) {
        throw new Error(`查询会员信息请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as MemberResponse;
      
      if (result.code === 0) {
        return result.data.info;
      } else {
        logger.warn(`用户查询会员信息失败 - userId: ${userId}, reason: ${result.message}`);
        return {
          valid: false,
          expire_time: 0
        };
      }
    } catch (error) {
      logger.error(`查询会员信息过程发生错误: ${error.message}`);
      return {
        valid: false,
        expire_time: 0
      };
    }
  }

  /**
   * 计算任务需要扣除的点数
   * @param taskType 任务类型
   * @param chatId 聊天ID（可选，仅在resumeAnalyzer类型时需要）
   * @returns 需要扣除的点数
   */
  public static async calculateDeductPoints(chatId: string, taskType: TaskType): Promise<number> {
    try {
      logger.info(`开始计算扣除点数 - taskType: ${taskType}, chatId: ${chatId}`);
      
      // 如果不是简历分析任务，直接返回对应任务类型的点数
      if (taskType !== 'resumeAnalyzer' || !chatId) {
        return getPayPointsByType(taskType);
      }
  
      // 查询user类型的消息数量
      const userMessages = await db.select()
        .from(messages)
        .where(and(
          eq(messages.chatId, chatId),
          eq(messages.role, 'user'),
          eq(messages.isDeleted, false)
        ));
  
      // 根据用户消息数量确定扣费点数
      switch (userMessages.length) {
        case 0: // 创建分析任务时，按照resumeAnalyzer类型的点数
          return getPayPointsByType('resumeAnalyzer');
        case 1: // 第一次任务执行完毕后的返回消息，不扣除
          return 0;
        default: // 后续任务执行，按照webSearch类型的点数
          return getPayPointsByType('webSearch');
      }
    } catch (error) {
      logger.error(`计算扣除点数时发生错误: ${error.message}`);
      // 发生错误时返回默认点数
      return getPayPointsByType(taskType);
    }
  }
}