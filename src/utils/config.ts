// https://ram.console.aliyun.com/profile/access-keys?spm=5176.11801677.0.0.328b25afq3SYni

// 抖音云账号
export const DY_APP = {
    APP_ID: "4491819207", // 抖音云账号AccessKey ID
    ACCESS_TOKEN: "JCGv-2yryGNmLjqzWUyqDuhDoiTvPhfJ", // 抖音云账号 Access Tokens
    SECRET_KEY: " nCf1J-Vnp2QmXTIEaTj8FAKEZXwiXFwJ", // 抖音云账号AccessKey Secret
    url: "https://openspeech.bytedance.com/api/v3/tts/unidirectional", // 双向流式API-支持复刻2.0/混音mix https://www.volcengine.com/docs/6561
    SOUND_ID: "S_1e8utrAt1", // 声音ID，用于指定发音人
    CLUSTER_ID: "volcano_icl", // 集群ID，用于指定集群
}

// 腾讯云账号
export const TX_APP = {
    SECRET_ID: "AKIDG2iANQGrEUdZHwV1NZipmUxMZYJuIWZO", // 腾讯云账号AccessKey ID
    SECRET_KEY: "hGfEdpqfFew3wdBXeWAcqsjNp57yjIdo", // 腾讯云账号AccessKey ID
    url: "https://tts.tencentcloudapi.com", // 腾讯云账号API URL
    REGION: "ap-beijing", // 腾讯云账号Region
}

// 个人账号
// export const DY_APP = {
//     APP_ID: "9343858984", // 抖音云账号AccessKey ID
//     ACCESS_TOKEN: "CbhgQ8UVqR6er91R9-gUu3UqQzS01ORE", // 抖音云账号	Access Token
//     SECRET_KEY: " VY9vmdcC1otN_34hki__7YyfBAPPJM0g", // 抖音云账号 AccessKey Secret
//     url: "https://openspeech.bytedance.com/api/v3/tts/unidirectional", // 双向流式API-支持复刻2.0/混音mix https://www.volcengine.com/docs/6561
//     SOUND_ID: "S_1e8utrAt1", // 声音ID，用于指定发音人
//     CLUSTER_ID: "volcano_icl", // 集群ID，用于指定集群
// }
