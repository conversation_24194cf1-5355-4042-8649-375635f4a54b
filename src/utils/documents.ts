import axios from 'axios';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { Document } from '@langchain/core/documents';
import logger from './logger';
import { eq } from 'drizzle-orm';
import { db } from '../db';
import crypto from 'crypto';
import { linkContents } from '../db/schema';
import { WebScraper } from '../scrape/scraper';

interface LinkContent {
  contents: string[];
  title: string;
  timestamp?: string;
  tool?: string;
}

const getContentsFromCache = async (link: string): Promise<LinkContent | null> => {
  try {
    const result = await db.select()
      .from(linkContents)
      .where(eq(linkContents.link, link))
      .limit(1);

    if (result.length > 0) {
      return {
        title: result[0].title,
        contents: result[0].contents || [],
        timestamp: result[0].timestamp?.toISOString(),
        tool: result[0].tool
      };
    }
    return null;
  } catch (err) {
    logger.error(
      `[DB] Error at fetch link content from database: ${err.message}`,
    );
    return null;
  }
};

const saveContentsToCache = async (
  link: string, 
  title: string, 
  contents: string[],
  timestamp?: string,
  tool?: string 
) => {
  try {
    const id = crypto.createHash('md5').update(link).digest('hex');
    const now = new Date();
    
    const insertData = {
      id,
      link,
      title,
      contents,
      timestamp: timestamp ? new Date(timestamp) : now,
      ...(tool && { tool }),
      createdAt: now,
      updatedAt: now,
    };

    const { id: _, link: __, createdAt: ___, ...updateData } = insertData;

    await db
      .insert(linkContents)
      .values(insertData)
      .onConflictDoUpdate({
        target: linkContents.id,
        set: updateData, 
      });
  } catch (err) {
    logger.error(`[DB] Error at save link content to database: ${err.message}`);
  }
};

export const fetchContentWithLink = async (
  link: string, 
  splitter: RecursiveCharacterTextSplitter,
  options: { 
    useAxios?: boolean; 
  } = {}
): Promise<{
  texts: string[];
  title: string;
  url: string;
  timestamp?: string;
}> => {
  const normalizedLink = link.startsWith('http://') || link.startsWith('https://')
    ? link
    : `https://${link}`;

  // 检查缓存
  const cached = await getContentsFromCache(normalizedLink);
  if (cached) {
    return {
      texts: cached.contents,
      title: cached.title,
      url: normalizedLink,
      timestamp: cached.timestamp
    };
  }

  try {
    let content, title, timestamp, tool;

    if (!content || !title) {
      logger.info(`Using local scraper: ${normalizedLink}`);
      const scraper = WebScraper.getInstance();
      const result = await scraper.fetchContent(normalizedLink, {
        ...options,
      });
      content = result.content;
      title = result.title;
      timestamp = result.timestamp;
      tool = result.tool;  // 获取本地抓取工具信息
    }

    // 处理提取到的内容
    const parsedText = content
      .replace(/(\r\n|\n|\r)/gm, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    const splittedText = await splitter.splitText(parsedText);
    
    // 检查内容是否有效且不是 PDF 文件
    if (splittedText.length > 0 && 
        splittedText[0].length >= 100 && 
        !title.includes('PDF Document')) {
      await saveContentsToCache(normalizedLink, title, splittedText, timestamp, tool);
    }

    return {
      texts: splittedText,
      title,
      url: normalizedLink,
      timestamp
    };
  } catch (err) {
    logger.error(`Error processing link ${normalizedLink}: ${err.message}`);
    return {
      texts: [`Failed to retrieve content: ${err.message}`],
      title: 'Failed to retrieve content',
      url: normalizedLink,
      timestamp: new Date().toISOString()  // 失败时使用当前时间
    };
  }
};

export const getDocumentsFromLinks = async ({ links }: { links: string[] }) => {
  const splitter = new RecursiveCharacterTextSplitter();
  const batchSize = 5;
  const batches = [];
  let docs: Document[] = [];

  // 将链接分批
  for (let i = 0; i < links.length; i += batchSize) {
    batches.push(links.slice(i, i + batchSize));
  }

  // 逐批处理
  for (const batch of batches) {
    const batchResults = await Promise.all(
      batch.map(link => fetchContentWithLink(link, splitter, { useAxios: true }))
    );
    
    const batchDocs = batchResults.flatMap(({ texts, title, url, timestamp }) => 
      texts.map(text => new Document({
        pageContent: text,
        metadata: { 
          title, 
          url,
          timestamp
        }
      }))
    );
    
    docs = docs.concat(batchDocs);
  }

  return docs;
};