import { DocumentProcessorResult } from './documentProcessor';
import { Buffer } from 'buffer';
import aliyunOcr from "./aliyunOcr";
import aliyunFileOcr from "./aliyunFileOcr";
import logger from './logger';

export interface FileContent {
    buffer: ArrayBuffer;
    contentType: string;
}

export async function getFileFromOSS(id: string): Promise<FileContent> {
    const response = await fetch(`https://jmso.cyjiaomu.com/ai/files/file/${id}`);

    if (!response.ok) {
        throw new Error(`OSS请求失败: ${response.status} ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    let contentType = response.headers.get('content-type') || '';

    return { buffer, contentType };
}

/**
 * 验证文本是否为有效的简历内容
 * @param text 待验证的文本
 * @returns {boolean} 是否有效
 */
export function isValidResumeText(text: string, checkKeywords: boolean = false): { isValid: boolean; reason?: string } {
    if (!text || text.trim().length === 0) {
        return { isValid: false, reason: '文本内容为空' };
    }

    // 检查文本长度是否合理（至少100个字符）
    if (text.trim().length < 100) {
        return { isValid: false, reason: '文本内容过短' };
    }

    // 检查乱码比例
    const totalChars = text.length;
    let invalidChars = 0;
    const validCharPattern = /[\u4e00-\u9fa5a-zA-Z0-9\s.,;:'"!?()[\]{}@#$%&*+-=/\\|<>]+/;

    for (const char of text) {
        if (!validCharPattern.test(char)) {
            invalidChars++;
        }
    }

    const invalidRatio = invalidChars / totalChars;
    if (invalidRatio > 0.3) { // 如果超过30%的字符是乱码
        return { isValid: false, reason: '文本包含大量无效字符，可能存在乱码' };
    }

    if (checkKeywords) {
        // 检查是否包含基本的简历关键词
        const resumeKeywords = ['姓名', '教育', '工作', '经验', '技能', '项目', '联系', '电话', '邮箱'];
        const keywordCount = resumeKeywords.filter(keyword => text.includes(keyword)).length;
        if (keywordCount < 3) { // 至少包含3个关键词
            return { isValid: false, reason: '文本缺少基本的简历关键信息' };
        }
    }

    return { isValid: true };
}

export async function parseFileContent(fileContent: FileContent, url: string): Promise<{
    text: string;
    processMethod: 'aliyun_ocr' | 'aliyun_file_ocr' | 'textract' | 'unknown';
}> {
    const { buffer, contentType } = fileContent;

    let text: string | null = null;
    let processMethod: any = 'unknown';
    try {
        // 如果是图片文件，尝试使用node-easyocr处理
        if (contentType && contentType.startsWith('image/')) {
            // const contentBuffer: any = Buffer.isBuffer(buffer) ? buffer : Buffer.from(buffer);
            // 使用node-easyocr作为首选OCR方案
            // try {
            //     const { createWorker } = require('tesseract.js');
            //     const worker = await createWorker('chi_sim');
            //     const { data = {} } = await worker.recognize(contentBuffer);
            //     text = data.text;
            //     await worker.terminate();
            // } catch (easyocrError) {
            //     console.error('node-easyocr识别失败:', easyocrError);
            // }
            text = text || (await aliyunOcr.handleAliyunOcr({ url }) as any) || '';
            processMethod = 'aliyun_ocr';
        } else if (contentType) {
            // 非图片文件使用textract处理
            try {
                text = await new Promise<string>((resolve, reject) => {
                    const contentBuffer = Buffer.isBuffer(buffer) ? buffer : Buffer.from(buffer);
                    const options = {
                        preserveLineBreaks: true,
                        exec: { maxBuffer: 1024 * 1024 * 50 },
                        type: contentType.split('/')[1]
                    };

                    const textract = require('textract');
                    textract.fromBufferWithMime(
                        contentType,
                        contentBuffer,
                        options,
                        (err, text) => {
                            if (err) reject(err);
                            else resolve(text);
                        }
                    );
                });

                processMethod = 'textract';
            }  catch (textractError) {
                console.error('textract识别失败:', textractError);
            }
                
            const { isValid } = isValidResumeText(text, false);
            if (!isValid) {
                logger.info("通用方法识别失败，尝试使用阿里云文件识别");
                text = (await aliyunFileOcr.handleAliyunOcr({ url }) as any) || '';
                processMethod = 'aliyun_file_ocr';
            }
        }

        if (!text || text.trim() === '') {
            throw new Error('文件内容解析失败或为空');
        }
        return { text, processMethod };
    } catch (error) {
        logger.error("文件处理失败:", error);
        throw error;
    }
}

export async function processOSSFile(ids: string[], sendSSEMessage?: Function, res?): Promise<{
    text: string;
    metadata: DocumentProcessorResult['metadata'];
    processMethod: string;
}[]> {
    const total = ids.length;
    const results = await Promise.all(ids.map(async (id, index) => {
        const fileContent = await getFileFromOSS(id);
        const { text, processMethod } = await parseFileContent(fileContent, `https://jmso.cyjiaomu.com/ai/files/file/${id}`);
        sendSSEMessage?.(res, {
            id,
            status: 'processing', 
            message: '正在获取文件...',
            current: index + 1,
            total
        })
        return {
            text,
            processMethod,
            metadata: {
                title: id,
                type: fileContent.contentType.split('/')[1] || 'unknown',
                pageCount: 1
            }
        };
    }));

    return results;
}
