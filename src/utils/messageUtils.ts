import { db } from '../db';
import { messages as messagesSchema, chats } from '../db/schema';
import crypto from 'crypto';
import logger from './logger';
import { taskQueue } from '../db/schema';
import { desc, eq, and, or } from 'drizzle-orm';
import { BillingUtils } from './billingUtils';
import { TaskType } from '../config'

interface MessageMetadata {
  createdAt: string;
  sources?: any[];
  reasoning_content?: string;
  analysisPayload?: {
    generalAnalysis?: string;
    jobMatchAnalysis?: string;
  };
  recognizerPayload?: {
    resumeText?: string;
    jobText?: string;
    resumeFileId?: string;
    jobFileId?: string;
    resumeFileName?: string;
    jobFileName?: string;
    mode: 'text' | 'mixed';
  };
  [key: string]: any;
}

/**
 * 消息工具类
 * 提供创建和存储消息的公共方法
 */
export class MessageUtils {
  /**
   * 生成唯一的消息ID
   * @param length 可选参数，指定生成的随机字节长度，默认为7
   * @param encoding 可选参数，指定编码方式，默认为'hex'
   * @returns 消息ID
   */
  public static generateMessageId(length: number = 7, encoding: BufferEncoding = 'hex'): string {
    return crypto.randomBytes(length).toString(encoding);
  }

  /**
   * 处理AI助手回复的扣费逻辑
   * @param chatId 会话ID
   * @returns Promise<void>
   */
  private static async handleAssistantBilling(chatId: string): Promise<void> {
    try {
      const chatInfo = await db.select()
        .from(chats)
        .where(eq(chats.id, chatId))
        .limit(1);

      if (chatInfo.length > 0) {
        await BillingUtils.deductUserBilling({
          chatId,
          channelId: chatInfo[0].source,
          userId: chatInfo[0].userId,
          taskType: chatInfo[0].focusMode as TaskType,
        });
      }
    } catch (error) {
      logger.error(`处理扣费失败: ${error.message}`);
    }
  }

  /* 创建并存储消息
  * @param content 消息内容
  * @param role 角色 (user/assistant)
  * @param metadata 元数据
  * @param messageId 可选的消息ID，如果不提供则自动生成
  * @returns 消息ID
  */
  public static async createMessage(
    content: string,
    chatId: string,
    role: 'user' | 'assistant',
    metadata: Partial<MessageMetadata> = {},
    messageId?: string
  ): Promise<string> {
    try {
      // 如果没有提供messageId，则生成一个新的
      messageId = messageId || this.generateMessageId();
      
      // 确保元数据中包含创建时间
      const fullMetadata: MessageMetadata = {
        createdAt: new Date().toISOString(),
        ...metadata
      };

      const values = {
        content,
        chatId,
        messageId,
        role,
        metadata: JSON.stringify(fullMetadata)
      }
      await db.insert(messagesSchema).values(values);

      // 收到用户信息后，即进行扣费
      if (role === 'user') {
        await this.handleAssistantBilling(chatId);
      }

      return messageId;
    } catch (error) {
      logger.error(`创建消息失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建用户和AI消息对
   * @param userContent 用户消息内容
   * @param aiContent AI回复内容
   * @param chatId 会话ID
   * @param aiMetadata AI消息元数据
   * @returns 包含用户和AI消息ID的对象
   */
  public static async createMessagePair(
    userContent: string,
    aiContent: string,
    chatId: string,
    aiMetadata: Partial<MessageMetadata> = {},
    userMetadata: Partial<MessageMetadata> = {}
  ): Promise<{ userMessageId: string; aiMessageId: string }> {
    try {
      const userMessageId = await this.createMessage(
        userContent,
        chatId,
        'user',
        userMetadata
      );
      
      const aiMessageId = await this.createMessage(
        aiContent,
        chatId,
        'assistant',
        aiMetadata
      );

      return { userMessageId, aiMessageId };
    } catch (error) {
      logger.error(`创建消息对失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建或获取会话
   * @param params 会话参数
   * @returns 包含会话ID和是否为新创建的标识的对象
   */
  public static async createOrGetChat(params: {
    chatId?: string;
    title?: string;
    focusMode: string;
    deviceId?: string;
    userId?: string;
    source?: string;
    files?: any[];
    hotQuestionId?: number | null;
  }): Promise<{ chatId: string; isNew: boolean }> {
    try {
      // 如果提供了chatId，检查会话是否存在
      const chatId = params.chatId || this.generateMessageId(20);
      
      if (params.chatId) {
        const existingChat = await db.select()
          .from(chats)
          .where(eq(chats.id, chatId))
          .limit(1);
        
        // 如果会话已存在，直接返回会话ID和isNew=false
        if (existingChat.length > 0) {
          return { chatId, isNew: false };
        }
      }
      
      // 创建新会话
      const values = {
        id: chatId,
        title: params.title || '新会话',
        createdAt: new Date(),
        focusMode: params.focusMode,
        deviceId: params.deviceId,
        userId: params.userId,
        source: params.source || 'web',
        files: params.files || [],
        hotQuestionId: params.hotQuestionId || null,
      };
      await db.insert(chats).values(values);
      
      // 返回会话ID和isNew=true表示这是新创建的会话
      return { chatId, isNew: true };
    } catch (error) {
      logger.error(`创建会话失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查会话是否有正在进行的任务
   * @param chatId 会话ID
   * @returns 如果有正在进行的任务，返回任务信息；否则返回null
   */
  public static async getActiveTask(chatId: string): Promise<{
    taskId: bigint;
    status: string;
  } | null> {
    try {
      const existingTasks = await db.select()
        .from(taskQueue)
        .where(eq(taskQueue.chatId, chatId))
        .orderBy(desc(taskQueue.createdAt))
        .limit(1);

      if (existingTasks.length > 0) {
        const task = existingTasks[0];
        logger.info(`会话 ${chatId} 存在任务: ${task.id}`);
        return {
          taskId: task.id,
          status: task.status
        };
      }

      return null;
    } catch (error) {
      logger.error(`检查会话任务状态失败: ${error.message}`);
      throw error;
    }
  }
}