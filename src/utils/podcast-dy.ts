import axios from 'axios';
import ffmpeg from 'fluent-ffmpeg';
import fs from 'fs';
import path from 'path';
import logger from './logger';
import FormData from 'form-data';
import { DY_APP } from "./config";
import { v4 as uuidv4 } from 'uuid';

// Helper function to upload audio to OSS
export const saveOssAudio = async ({ files = [], signature }: any): Promise<any> => {
    if (!signature) {
        return false;
    }

    for (let index = 0; index < files.length; index++) {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(files[index]));
        formData.append('serialNo', (index + 1).toString());
        formData.append('num', files.length.toString());

        await axios.post(`https://f.foundingaz.cn/api/files/1-audio/upload/${signature}`, formData, {
            headers: formData.getHeaders(),
        });
        console.log(
            `Uploaded part ${index + 1} of ${files.length} for signature ${signature}`,
        );
    }
    const res = await axios.post(
        `https://f.foundingaz.cn/api/files/1-audio/end/${signature}`,
        {
            num: files.length,
        },
    );
    console.log(' ~ saveOssAudio ~ res:', res.data);
    return res.data;
};


export async function fetchGeneratedAudio(individualAudioUrl: string, i: number): Promise<ArrayBuffer> {
    logger.info(`Fetching individual audio from: ${individualAudioUrl}`);
    const audioDataRequestStartTime = Date.now();
    logger.info(`Starting audio data request for item ${i} at ${audioDataRequestStartTime}ms.`);
    const audioDataResponse = await axios.get(individualAudioUrl, {
        responseType: 'arraybuffer', // Fetch the actual audio data
    });
    const audioDataRequestEndTime = Date.now();
    logger.info(`Received audio data response for item ${i}. Took ${audioDataRequestEndTime - audioDataRequestStartTime}ms.`);
    return audioDataResponse.data;
}

const { ACCESS_TOKEN, APP_ID, SOUND_ID, SECRET_KEY } = DY_APP;

export async function dyTtsGeneration(text: string, voice: any, i: number): Promise<Uint8Array> {
    logger.info(`Starting Douyin TTS request for item ${i} - ${text}.`);
    const audio_params: any = {
        format: "mp3", sample_rate: 16000, loudness_rate: 100
    };
    if (voice?.voice) {
        audio_params.voice = voice.voice;
    }

    const reqId = uuidv4();
    logger.info(`Sending TTS request ${reqId} for text: ${text} `);
    const response = await axios.post("https://openspeech.bytedance.com/api/v3/tts/unidirectional", {
        req_params: {
            text: text,
            speaker: voice?.voice || SOUND_ID,
            audio_params,
            additions: JSON.stringify({ disable_markdown_filter: true, enable_language_detector: true, enable_latex_tn: true, disable_default_bit_rate: true, max_length_to_filter_parenthesis: 0, cache_config: { text_type: 1, use_cache: true, }, }),
        }
    }, {
        headers: {
            "X-Api-App-Id": APP_ID,
            "X-Api-Access-Key": ACCESS_TOKEN,
            "X-Api-Resource-Id": voice?.resourceId || "volc.megatts.default",
            "X-Api-App-Key": SECRET_KEY,
            "X-Api-Request-Id": reqId,
        }
    }) as any;
    logger.info(`Received raw TTS response: ${JSON.stringify(response?.data)}`);
    let concatenatedBase64Data = "";
    if (response?.data) {
        logger.info(`🚀 ~ dyTtsGeneration ~ response.data type: ${typeof response.data}, length: ${response.data.length}`);
        if (typeof response.data === 'object' && response.data.code != 20000000) {
            throw new Error(`${JSON.stringify(response?.data)}. Text: ${text}`);
        }
        if (typeof response.data === 'string') {
            const lines = response.data.split('\n');
            for (const line of lines) {
                if (line.trim() === "") continue;
                logger.info(`Processing line: ${line.substring(0, 100)}...`); // Log first 100 chars
                try {
                    const parsedData = JSON.parse(line);
                    if (parsedData.code === 0 && parsedData.data) {
                        logger.info(`Original data length: ${parsedData.data.length}`);
                        // Remove any non-base64 characters from the beginning of the string
                        const cleanedData = parsedData.data.replace(/^[\s\S]*?(?=[A-Za-z0-9+/=])/, '');
                        logger.info(`Cleaned data length: ${cleanedData.length}`);
                        concatenatedBase64Data += cleanedData;
                    }
                } catch (e) {
                    logger.error(`Failed to parse JSON string from line: ${line}, Error: ${e}`);
                }
            }
        }
    }

    logger.info(`Concatenated base64 data length: ${concatenatedBase64Data.length}`);
    const audioBuffer = Buffer.from(concatenatedBase64Data, 'base64');
    logger.info(`Audio buffer length: ${audioBuffer.length}`);
    const uint8Array = new Uint8Array(audioBuffer);
    logger.info(`Received Douyin TTS response for item ${i}. Audio data length: ${uint8Array.byteLength}`);
    logger.info(`Uint8Array byte length: ${uint8Array.byteLength}`);
    return uint8Array;
}

export async function generateAndSaveIndividualAudio(item: any, i: number, signature: string, tempDir: string): Promise<string | null> {
    const { text, role } = item;
    const voice = role === '0' ? {
        voice: 'zh_female_yingyujiaoyu_mars_bigtts',
        resourceId: 'volc.service_type.10029'
    } : {
        voice: 'zh_male_ruyayichen_emo_v2_mars_bigtts',
        resourceId: 'volc.service_type.10029'
    };
    const audioData = await dyTtsGeneration(text, voice, i);

    if (audioData.byteLength === 0) {
        logger.warn(`No audio data received for item ${i}. Skipping file save. Text: ${text}`);
        return null; // Return null if no audio data
    }

    const tempAudioPath = path.join(tempDir, `audio_${i}.mp3`); // Changed to .mp3 as dyTtsGeneration returns mp3
    fs.writeFileSync(tempAudioPath, audioData); // Write the fetched audio data
    logger.info(`Saved temporary audio file: ${tempAudioPath}`);
    return tempAudioPath;
}

export async function combineAudioFiles(audioFilePaths: string[], tempDir: string, signature: string): Promise<string> {
    const concatenatedFilePath = path.join(tempDir, `concatenated_${signature}.mp3`);
    const outputFilePath = path.join(
        tempDir,
        `combined_audio_${signature}.mp3`,
    );

    // Step 1: Concatenate all the speech audio files
    logger.info(`Starting FFmpeg concatenation to: ${concatenatedFilePath}`);
    await new Promise<void>((resolve, reject) => {
        const command = ffmpeg();
        audioFilePaths.forEach((p) => command.input(p));
        command
            .on('error', (err) => {
                logger.error(`FFmpeg concatenation error: ${err.message}`);
                reject(err);
            })
            .on('end', () => {
                logger.info('FFmpeg concatenation finished successfully.');
                resolve();
            })
            .mergeToFile(concatenatedFilePath, tempDir);
    });

    // Step 2: Download background audio
    const bgAudioUrl = 'https://f.foundingaz.cn/api/files/audio/podcast-bg';
    logger.info(`Downloading background audio from: ${bgAudioUrl}`);
    const bgAudioResponse = await axios.get(bgAudioUrl, { responseType: 'arraybuffer' });
    const bgAudioPath = path.join(tempDir, 'background_audio.mp3');
    fs.writeFileSync(bgAudioPath, bgAudioResponse.data);
    logger.info(`Saved background audio to: ${bgAudioPath}`);

    // Step 3: Mix concatenated audio with looping background audio
    logger.info(`Starting FFmpeg mixing to: ${outputFilePath}`);
    await new Promise<void>((resolve, reject) => {
        ffmpeg()
            .input(concatenatedFilePath)
            .input(bgAudioPath)
            .inputOptions(['-stream_loop', '-1']) // Loop the background audio indefinitely
            .complexFilter([
                // Set background audio volume to 0.1, and mix with speech.
                // The mixing will stop when the first stream (concatenated speech) ends.
                '[1:a]volume=0.1[a1];[0:a][a1]amix=inputs=2:duration=first'
            ])
            .on('error', (err) => {
                logger.error(`FFmpeg mixing error: ${err.message}`);
                reject(err);
            })
            .on('end', () => {
                logger.info('FFmpeg mixing finished successfully.');
                resolve();
            })
            .save(outputFilePath);
    });
    logger.info('FFmpeg mixing promise resolved.');

    return outputFilePath;
}

export async function uploadCombinedAudioToOss(outputFilePath: string, signature: string): Promise<any> {
    const combinedAudioUploadResult = await saveOssAudio({
        files: [outputFilePath],
        signature: signature,
    });
    logger.info(`Combined audio uploaded to OSS. Result: ${JSON.stringify(combinedAudioUploadResult)}`);
    return combinedAudioUploadResult;
}

export function cleanupTemporaryFiles(tempDir: string) {
    if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
        logger.info(`Deleted temporary directory: ${tempDir}`);
    }
}