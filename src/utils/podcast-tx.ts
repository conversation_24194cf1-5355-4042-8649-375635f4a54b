import logger from './logger';
import { TX_APP } from "./config";
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import ffmpeg from 'fluent-ffmpeg';
import fs from 'fs';
import path from 'path';
import FormData from 'form-data';

const delay = (ms: number) => new Promise(res => setTimeout(res, ms));

// Helper function to upload audio to OSS
export const saveOssAudioTx = async ({ files = [], signature }: any): Promise<any> => {
    if (!signature) {
        return false;
    }

    for (let index = 0; index < files.length; index++) {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(files[index]));
        formData.append('serialNo', (index + 1).toString());
        formData.append('num', files.length.toString());

        await axios.post(`https://f.foundingaz.cn/api/files/1-audio/upload/${signature}`, formData, {
            headers: formData.getHeaders(),
        });
        logger.info(
            `Uploaded part ${index + 1} of ${files.length} for signature ${signature}`,
        );
    }
    const res = await axios.post(
        `https://f.foundingaz.cn/api/files/1-audio/end/${signature}`,
        {
            num: files.length,
        },
    );
    logger.info(' ~ saveOssAudioTx ~ res:', res.data);
    return res.data;
};

export async function fetchGeneratedAudioTx(individualAudioUrl: string, i: number): Promise<ArrayBuffer> {
    logger.info(`Fetching individual audio from: ${individualAudioUrl}`);
    const audioDataRequestStartTime = Date.now();
    logger.info(`Starting audio data request for item ${i} at ${audioDataRequestStartTime}ms.`);
    const audioDataResponse = await axios.get(individualAudioUrl, {
        responseType: 'arraybuffer', // Fetch the actual audio data
    });
    const audioDataRequestEndTime = Date.now();
    logger.info(`Received audio data response for item ${i}. Took ${audioDataRequestEndTime - audioDataRequestStartTime}ms.`);
    return audioDataResponse.data;
}

// Depends on tencentcloud-sdk-nodejs version 4.0.3 or higher
const tencentcloud = require("tencentcloud-sdk-nodejs-tts");

const TtsClient = tencentcloud.tts.v20190823.Client;

export async function txTtsGeneration(text: string, voice: any, i: number): Promise<Uint8Array> {
    logger.info(`Starting Tencent Cloud TTS request for item ${i} - ${text}.`);

    const { SECRET_ID, SECRET_KEY, REGION } = TX_APP;

    logger.info(`DEBUG: TENCENTCLOUD_SECRET_ID value: ${SECRET_ID}`);
    logger.info(`DEBUG: TENCENTCLOUD_SECRET_KEY value: ${SECRET_KEY}`);

    const service = 'tts';

    const clientConfig = {
        credential: {
            secretId: SECRET_ID,
            secretKey: SECRET_KEY,
        },
        region: REGION,
        profile: {
            httpProfile: {
                endpoint: "tts.tencentcloudapi.com",
            },
        },
    };

    const client = new TtsClient(clientConfig);

    const params = {
        "Text": text,
        "SessionId": `tx-session-${uuidv4()}`,
        "VoiceType": voice?.voice || 101000, // Default voice type, adjust as needed
        "Codec": "mp3", // Ensure MP3 format
        "SampleRate": 16000, // Ensure 16kHz sample rate
    };

    try {
        const data = await client.TextToVoice(params);
        if (data && data.Audio) {
            logger.info(`Received Tencent Cloud TTS response for item ${i}. Audio length: ${data.Audio.length}`);
            const audioBuffer = Buffer.from(data.Audio, 'base64');
            const uint8Array = new Uint8Array(audioBuffer);
            logger.info(`Received Tencent Cloud TTS response for item ${i}. Audio data length: ${uint8Array.byteLength}`);
            return uint8Array;
        } else {
            logger.warn(`Tencent Cloud TTS response for item ${i} has no Audio or is empty. Response: ${JSON.stringify(data)}`);
            return new Uint8Array(0);
        }
    } catch (err: any) {
        logger.error(`Error during Tencent Cloud TTS request for item ${i} - ${text}: ${err.message}`);
        if (err.code) {
            logger.error(`Tencent Cloud TTS error code: ${err.code}`);
        }
        if (err.requestId) {
            logger.error(`Tencent Cloud TTS request ID: ${err.requestId}`);
        }
        return new Uint8Array(0); // Return empty Uint8Array on error
    }
}

export async function generateAndSaveIndividualAudioTx(item: any, i: number, signature: string, tempDir: string): Promise<string | null> {
    const { text, role } = item;
    // https://cloud.tencent.com/document/product/1073/92668
    const voice = role === '0' ? {
        voice: 601007, // 601007 爱小叶 聊天女声 大模型音色 中文 8k/16k/24k 中性、悲伤、高兴、生气、恐惧、撒娇、震惊、厌恶、平静
    } : {
        voice: 601004, // 爱小树 资讯男声 大模型音色 中文 8k/16k/24k 中性、悲伤、高兴、生气、恐惧、撒娇、震惊、厌恶、平静
    };

    // Add a delay before calling txTtsGeneration to avoid rate limiting
    await delay(200);

    const audioData = await txTtsGeneration(text, voice, i);

    if (audioData.byteLength === 0) {
        logger.warn(`No audio data received for item ${i}. Skipping file save. Text: ${text}`);
        return null; // Return null if no audio data
    }

    const tempAudioPath = path.join(tempDir, `audio_${i}.mp3`); // Changed to .mp3 as txTtsGeneration returns mp3
    fs.writeFileSync(tempAudioPath, audioData); // Write the fetched audio data
    logger.info(`Saved temporary audio file: ${tempAudioPath}`);
    return tempAudioPath;
}

export async function combineAudioFilesTx(audioFilePaths: string[], tempDir: string, signature: string): Promise<string> {
    const concatenatedFilePath = path.join(tempDir, `concatenated_${signature}.mp3`);
    const outputFilePath = path.join(
        tempDir,
        `combined_audio_${signature}.mp3`,
    );

    // Step 1: Concatenate all the speech audio files
    logger.info(`Starting FFmpeg concatenation to: ${concatenatedFilePath}`);
    await new Promise<void>((resolve, reject) => {
        const command = ffmpeg();
        audioFilePaths.forEach((p) => command.input(p));
        command
            .on('error', (err) => {
                logger.error(`FFmpeg concatenation error: ${err.message}`);
                reject(err);
            })
            .on('end', () => {
                logger.info('FFmpeg concatenation finished successfully.');
                resolve();
            })
            .mergeToFile(concatenatedFilePath, tempDir);
    });

    // Step 2: Download background audio
    const bgAudioUrl = 'https://f.foundingaz.cn/api/files/audio/podcast-bg';
    logger.info(`Downloading background audio from: ${bgAudioUrl}`);
    const bgAudioResponse = await axios.get(bgAudioUrl, { responseType: 'arraybuffer' });
    const bgAudioPath = path.join(tempDir, 'background_audio.mp3');
    fs.writeFileSync(bgAudioPath, bgAudioResponse.data);
    logger.info(`Saved background audio to: ${bgAudioPath}`);

    // Step 3: Mix concatenated audio with looping background audio
    logger.info(`Starting FFmpeg mixing to: ${outputFilePath}`);
    await new Promise<void>((resolve, reject) => {
        ffmpeg()
            .input(concatenatedFilePath)
            .input(bgAudioPath)
            .inputOptions(['-stream_loop', '-1']) // Loop the background audio indefinitely
            .complexFilter([
                // Set background audio volume to 0.1, and mix with speech.
                // The mixing will stop when the first stream (concatenated speech) ends.
                '[1:a]volume=0.1[a1];[0:a][a1]amix=inputs=2:duration=first'
            ])
            .on('error', (err) => {
                logger.error(`FFmpeg mixing error: ${err.message}`);
                reject(err);
            })
            .on('end', () => {
                logger.info('FFmpeg mixing finished successfully.');
                resolve();
            })
            .save(outputFilePath);
    });
    logger.info('FFmpeg mixing promise resolved.');

    return outputFilePath;
}

export async function uploadCombinedAudioToOssTx(outputFilePath: string, signature: string): Promise<any> {
    const combinedAudioUploadResult = await saveOssAudioTx({
        files: [outputFilePath],
        signature: signature,
    });
    logger.info(`Combined audio uploaded to OSS. Result: ${JSON.stringify(combinedAudioUploadResult)}`);
    return combinedAudioUploadResult;
}

export function cleanupTemporaryFilesTx(tempDir: string) {
    if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
        logger.info(`Deleted temporary directory: ${tempDir}`);
    }
}