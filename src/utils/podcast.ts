import axios from 'axios';
import ffmpeg from 'fluent-ffmpeg';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import logger from './logger';
import FormData from 'form-data';

// Helper function to upload audio to OSS
export const saveOssAudio = async ({ files = [], signature }: any): Promise<any> => {
    if (!signature) {
        return false;
    }

    for (let index = 0; index < files.length; index++) {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(files[index]));
        formData.append('serialNo', (index + 1).toString());
        formData.append('num', files.length.toString());

        await axios.post(`https://f.foundingaz.cn/api/files/1-audio/upload/${signature}`, formData, {
            headers: formData.getHeaders(),
        });
        console.log(
            `Uploaded part ${index + 1} of ${files.length} for signature ${signature}`,
        );
    }
    const res = await axios.post(
        `https://f.foundingaz.cn/api/files/1-audio/end/${signature}`,
        {
            num: files.length,
        },
    );
    console.log(' ~ saveOssAudio ~ res:', res.data);
    return res.data;
};


export async function triggerTtsGeneration(ttsUrl: string, text: string, i: number): Promise<void> {
    const ttsRequestStartTime = Date.now();
    logger.info(`Starting TTS request for item ${i} at ${ttsRequestStartTime}ms.`);
    const ttsResponse = await axios.post(ttsUrl, text, {
        headers: {
            'Content-Type': 'application/octet-stream',
        },
        responseType: 'text', // Expecting text response, or empty
        timeout: 10000, // Set a timeout of 10 seconds
    });
    const ttsRequestEndTime = Date.now();
    logger.info(`Received TTS response for item ${i}. Status: ${ttsResponse.status}. Took ${ttsRequestEndTime - ttsRequestStartTime}ms. Data length: ${ttsResponse.data ? ttsResponse.data.length : 'N/A'}`);
}

export async function fetchGeneratedAudio(individualAudioUrl: string, i: number): Promise<ArrayBuffer> {
    logger.info(`Fetching individual audio from: ${individualAudioUrl}`);
    const audioDataRequestStartTime = Date.now();
    logger.info(`Starting audio data request for item ${i} at ${audioDataRequestStartTime}ms.`);
    const audioDataResponse = await axios.get(individualAudioUrl, {
        responseType: 'arraybuffer', // Fetch the actual audio data
    });
    const audioDataRequestEndTime = Date.now();
    logger.info(`Received audio data response for item ${i}. Took ${audioDataRequestEndTime - audioDataRequestStartTime}ms.`);
    return audioDataResponse.data;
}

export async function generateAndSaveIndividualAudio(item: any, i: number, signature: string, tempDir: string): Promise<string> {
    const { text, role } = item;
    const voice = role === '0' ? 'x4_yezi' : 'x4_zhanglijun';
    const id = `podcast_xf_${voice}_${signature}_role_${role}_${i}`;
    const ttsUrl = `https://f.foundingaz.cn/api/files/xf-tts/${id}?jn=${voice}`;
    const individualAudioUrl = `https://f.foundingaz.cn/api/files/audio/${id}`;

    await triggerTtsGeneration(ttsUrl, text, i);
    const audioData = await fetchGeneratedAudio(individualAudioUrl, i) as any;

    const tempAudioPath = path.join(tempDir, `audio_${i}.wav`);
    fs.writeFileSync(tempAudioPath, audioData); // Write the fetched audio data
    logger.info(`Saved temporary audio file: ${tempAudioPath}`);
    return tempAudioPath;
}

export async function combineAudioFiles(audioFilePaths: string[], tempDir: string, signature: string): Promise<string> {
    const concatenatedFilePath = path.join(tempDir, `concatenated_${signature}.wav`);
    const outputFilePath = path.join(
        tempDir,
        `combined_audio_${signature}.wav`,
    );

    // Step 1: Concatenate all the speech audio files
    logger.info(`Starting FFmpeg concatenation to: ${concatenatedFilePath}`);
    await new Promise<void>((resolve, reject) => {
        const command = ffmpeg();
        audioFilePaths.forEach((p) => command.input(p));
        command
            .on('error', (err) => {
                logger.error(`FFmpeg concatenation error: ${err.message}`);
                reject(err);
            })
            .on('end', () => {
                logger.info('FFmpeg concatenation finished successfully.');
                resolve();
            })
            .mergeToFile(concatenatedFilePath, tempDir);
    });

    // Step 2: Download background audio
    const bgAudioUrl = 'https://f.foundingaz.cn/api/files/audio/podcast-bg';
    logger.info(`Downloading background audio from: ${bgAudioUrl}`);
    const bgAudioResponse = await axios.get(bgAudioUrl, { responseType: 'arraybuffer' });
    const bgAudioPath = path.join(tempDir, 'background_audio.mp3');
    fs.writeFileSync(bgAudioPath, bgAudioResponse.data);
    logger.info(`Saved background audio to: ${bgAudioPath}`);

    // Step 3: Mix concatenated audio with looping background audio
    logger.info(`Starting FFmpeg mixing to: ${outputFilePath}`);
    await new Promise<void>((resolve, reject) => {
        ffmpeg()
            .input(concatenatedFilePath)
            .input(bgAudioPath)
            .inputOptions(['-stream_loop', '-1']) // Loop the background audio indefinitely
            .complexFilter([
                // Set background audio volume to 0.1, and mix with speech.
                // The mixing will stop when the first stream (concatenated speech) ends.
                '[1:a]volume=0.1[a1];[0:a][a1]amix=inputs=2:duration=first'
            ])
            .on('error', (err) => {
                logger.error(`FFmpeg mixing error: ${err.message}`);
                reject(err);
            })
            .on('end', () => {
                logger.info('FFmpeg mixing finished successfully.');
                resolve();
            })
            .save(outputFilePath);
    });
    logger.info('FFmpeg mixing promise resolved.');

    return outputFilePath;
}

export async function uploadCombinedAudioToOss(outputFilePath: string, signature: string): Promise<any> {
    const combinedAudioUploadResult = await saveOssAudio({
        files: [outputFilePath],
        signature: signature,
    });
    logger.info(`Combined audio uploaded to OSS. Result: ${JSON.stringify(combinedAudioUploadResult)}`);
    return combinedAudioUploadResult;
}

export function cleanupTemporaryFiles(tempDir: string) {
    if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
        logger.info(`Deleted temporary directory: ${tempDir}`);
    }
}
