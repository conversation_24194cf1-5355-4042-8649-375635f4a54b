import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { getSummarizerModel } from '../lib/providers';
import { BaseMessage } from '@langchain/core/messages';
import formatChatHistoryAsString from './formatHistory';
import logger from './logger';

type OptimizeType = 'search' | 'knowledge';

const PROMPTS = {
  search: `你是一个搜索引擎专家。请根据用户的问题和聊天历史，提取最核心的搜索关键词（4-6个），并确保：
    1. 保持重要概念的完整性，如"特斯拉新能源汽车"应作为一个整体
    2. 忽略问题中的修饰词（如"怎么样"、"如何"、"是什么"等）
    3. 忽略要求词（如"分析"、"帮忙"、"需要"等）
    4. 忽略格式相关词（如"列表"、"图片"、"视频"等）
    5. 分析聊天历史中的相关信息：
       - 如果当前问题是对历史问题的延续，提取相关的关键词
       - 如果历史中包含重要的上下文信息，将其纳入关键词
    6. 按重要性排序返回：
       - 完整的核心概念（如"特斯拉新能源汽车"）
       - 核心概念的关键属性（如"性能"、"续航"）
       - 历史对话中的相关核心概念
       - 1-2个相关的同类概念（如"电动汽车"）

    聊天历史：
    {history}
    
    当前问题：{query}
    只需返回关键词组合，用空格分隔，不要其他解释。`,
  
  knowledge: `你是一个管理领域的专家。请根据用户的问题和聊天历史，提取关键词（6-8个），并确保：
    1. 首要保留问题中的核心概念和关键词（如"创业酵母"这样的具体主体）
    2. 保持重要概念的完整性（如"数字化转型战略"作为一个整体）
    3. 忽略修饰词、要求词（如"分析"、"帮忙"、"需要"等）和格式相关词（如"图表"、"流程图"等）
    4. 分析聊天历史中的相关概念：
       - 如果当前问题提到的概念在历史中有详细讨论，提取相关的关键词
       - 如果历史中包含当前问题相关的具体案例或实践，提取这些案例中的关键词
    5. 按以下顺序返回关键词：
       - 原问题中的完整核心概念（如"创业酵母经营策略"）
       - 原问题中的关键主体（如"创业酵母"）
       - 原问题中的管理主题（如"经营策略"）
       - 聊天历史中提到的相关具体概念
       - 1-2个管理领域通用词（如"战略管理"）

    聊天历史：
    {history}

    当前问题：{query}
    只需返回关键词组合，用空格分隔，不要其他解释。`
};

export const optimizeSearchQuery = async (
  query: string, 
  type: OptimizeType = 'search',
  history: BaseMessage[] = []
): Promise<string> => {
  const llm = await getSummarizerModel();
  const prompt = PromptTemplate.fromTemplate(PROMPTS[type]);
  const chain = prompt.pipe(llm).pipe(new StringOutputParser());
  
  // 获取最近的2组完整对话记录
  const recentHistory = history
    .slice(-5)  // 获取最后5条消息
    .filter((_, index, array) => index < array.length - 1);  // 排除最后一条消息（当前提问）
  
  const result = await chain.invoke({ 
    query,
    history: formatChatHistoryAsString(recentHistory)
  });

  return result;
};