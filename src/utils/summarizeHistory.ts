import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import logger from './logger';

type HistoryMessage = [string, string];

export const summarizeHistory = async (
  messages: HistoryMessage[],
  maxCompletePairs: number = 2,  // 获取最近的2组完整对话记录
) => {
  try {
    if (!messages?.length) return messages;
    
    // 如果消息数量为奇数，说明最后有一个单独的human消息
    const hasIncompleteMessage = messages.length % 2 === 1;
    
    // 计算需要返回的消息数量（每组对话包含一个问题和一个回答）
    const maxMessages = maxCompletePairs * 2;
    
    // 如果有单独的最后一条消息，我们从倒数第二条开始截取
    const startIndex = hasIncompleteMessage ? -maxMessages - 1 : -maxMessages;
    
    // 只返回完整的对话组
    return messages.slice(startIndex, hasIncompleteMessage ? -1 : undefined);
  } catch (error) {
    logger.error('获取历史消息失败:', error);
    return messages;
  }
};