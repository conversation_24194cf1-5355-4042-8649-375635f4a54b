import { WebSocket } from 'ws';
import { handleMessage } from './messageHandler';
import {
  getChatModel,
  getEmbeddingModel,
} from '../lib/providers';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { Embeddings } from '@langchain/core/embeddings';
import type { IncomingMessage } from 'http';
import logger from '../utils/logger';
import { ChatOpenAI } from '@langchain/openai';

const connections = new Map<string, WebSocket>();

const HEARTBEAT_INTERVAL = 30000; // 30秒发送一次心跳

export const getConnection = (clientId: string): WebSocket | undefined => {
  return connections.get(clientId);
};

export const sendMessageToClient = (clientId: string, message: any) => {
  const ws = getConnection(clientId);
  console.log("🚀 ~ sendMessageToClient ~ ws:", ws)
  if (ws && ws.readyState === ws.OPEN) {
    ws.send(JSON.stringify(message));
  }
};

export const handleConnection = async (
  ws: WebSocket,
  request: IncomingMessage,
) => {
  try {
    const [llm, embeddings] = await Promise.all([
      getChatModel(),
      getEmbeddingModel()
    ]);

    if (!llm || !embeddings) {
      ws.send(JSON.stringify({
        type: 'error',
        data: 'Failed to load required models, please refresh the page and try again.',
        key: 'FAILED_TO_LOAD_MODELS',
      }));
      ws.close();
      return;  // 添加 return 防止继续执行
    }

    // 添加心跳检测
    let isAlive = true;
    const heartbeat = setInterval(() => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
      } else {
        clearInterval(heartbeat);
      }
    }, HEARTBEAT_INTERVAL);

    const interval = setInterval(() => {
      if (ws.readyState === ws.OPEN) {
        ws.send(
          JSON.stringify({
            type: 'signal',
            data: 'open',
          }),
        );
        clearInterval(interval);
      }
    }, 5);

    ws.on('message', async (message) => {
      const data = JSON.parse(message.toString());
      if (data.type === 'pong') {
        isAlive = true;
        return;
      }
      await handleMessage(message.toString(), ws, llm, embeddings);
    });

    ws.on('close', () => logger.debug('Connection closed'));
  } catch (err) {
    ws.send(
      JSON.stringify({
        type: 'error',
        data: 'Internal server error.',
        key: 'INTERNAL_SERVER_ERROR',
      }),
    );
    ws.close();
    logger.error(err);
  }
};
