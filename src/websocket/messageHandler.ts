import { EventEmitter, WebSocket } from 'ws';
import { BaseMessage, AIMessage, HumanMessage } from '@langchain/core/messages';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { Embeddings } from '@langchain/core/embeddings';
import { ChatOpenAI } from '@langchain/openai';
import logger from '../utils/logger';
import { db } from '../db';
import { chats, messages as messagesSchema } from '../db/schema';
import { eq, asc, gt, and } from 'drizzle-orm';
import crypto from 'crypto';
import { getFileDetails } from '../utils/files';
import MetaSearchAgent, {
  MetaSearchAgentType,
} from '../search/metaSearchAgent';
import prompts from '../prompts';
import { getSummarizerModel } from '../lib/providers';
import { summarizeHistory } from '../utils/summarizeHistory';
import { MessageUtils } from '../utils/messageUtils';
import { taskQueueManager } from '../agent/taskQueue';

type Message = {
  messageId: string;
  chatId: string;
  content: string;
  deviceId: string; // 设备标识
  userId: string; // 用户标识
};

type WSMessage = {
  message: Message;
  optimizationMode: 'speed' | 'balanced' | 'quality';
  type: string;
  focusMode: string;
  history: Array<[string, string]>;
  files: Array<string>;
  source?: string;  // 消息来源
  hotQuestionId?: number;  // 热门问题ID
  taskId: bigint;
};

export const searchHandlers = {
  webSearch: new MetaSearchAgent({
    activeEngines: ['bing', 'duckduckgo', 'qwant'],
    queryGeneratorPrompt: prompts.webSearchRetrieverPrompt,
    responsePrompt: prompts.webSearchResponsePrompt,
    rerank: true,
    rerankThreshold: 0.3,
    searchWeb: true,
    searchKnowledge: false,
    knowledgeChannel: '',
    summarizer: true,
  }),
  academicSearch: new MetaSearchAgent({
    activeEngines: ['arxiv', 'google scholar', 'pubmed'],
    queryGeneratorPrompt: prompts.academicSearchRetrieverPrompt,
    responsePrompt: prompts.academicSearchResponsePrompt,
    rerank: true,
    rerankThreshold: 0,
    searchWeb: true,
    searchKnowledge: false,
    knowledgeChannel: '',
    summarizer: false,
  }),
  writingAssistant: new MetaSearchAgent({
    activeEngines: [],
    queryGeneratorPrompt: '',
    responsePrompt: prompts.writingAssistantPrompt,
    rerank: true,
    rerankThreshold: 0,
    searchWeb: false,
    searchKnowledge: false,
    knowledgeChannel:'',
    summarizer: false,
  }),
  managementMaster: new MetaSearchAgent({
    activeEngines: [],
    queryGeneratorPrompt: prompts.managementMasterRetrieverPrompt,
    responsePrompt: prompts.managementMasterPrompt,
    rerank: true,
    rerankThreshold: 0,
    searchWeb: false,
    searchKnowledge: true,
    knowledgeChannel: 'knowledgeFromDify',
    summarizer: true,
  }),
  managementMasterJM: new MetaSearchAgent({
    activeEngines: [],
    queryGeneratorPrompt: prompts.managementMasterRetrieverPrompt,
    responsePrompt: prompts.managementMasterJMPrompt,
    rerank: true,
    rerankThreshold: 0,
    searchWeb: false,
    searchKnowledge: true,
    knowledgeChannel: 'knowledgeFromDifyJM',
    summarizer: true,
  }),
  resumeAnalyzer: new MetaSearchAgent({
    activeEngines: [],
    queryGeneratorPrompt: '',
    responsePrompt: prompts.interactiveResumeAdvisorPrompt,
    rerank: false,
    rerankThreshold: 0,
    searchWeb: false,
    searchKnowledge: false,
    knowledgeChannel: '',
    summarizer: true,
  }),
};

const handleEmitterEvents = (
  emitter: EventEmitter,
  ws: WebSocket,
  messageId: string,
  chatId: string,
) => {
  let recievedMessage = '';
  let sources = [];
  let reasoningContent = '';

  emitter.on('data', (data) => {
    const parsedData = JSON.parse(data);
    if (parsedData.type === 'response') {
      ws.send(
        JSON.stringify({
          type: 'message',
          data: parsedData.data,
          messageId: messageId,
        }),
      );
      recievedMessage += parsedData.data;
    } else if (parsedData.type === 'sources') {
      ws.send(
        JSON.stringify({
          type: 'sources',
          data: parsedData.data,
          messageId: messageId,
        }),
      );
      sources = parsedData.data;
    } else if (parsedData.type === 'reasoning') {
      // 处理推理内容
      reasoningContent += parsedData.data;
      ws.send(
        JSON.stringify({
          type: 'reasoning',
          data: parsedData.data,
          messageId: messageId,
        }),
      );
    }
  });

  emitter.on('end', async () => {
    try {
      await MessageUtils.createMessage(
        recievedMessage,
        chatId,
        'assistant',
        {
          ...(sources?.length > 0 && { sources }),
          ...(reasoningContent && { reasoning_content: reasoningContent }),
        }
      );

      ws.send(JSON.stringify({ type: 'messageEnd', messageId: messageId }));
    } catch (err) {
      logger.error(`Error saving message: ${err.message}`);
    }
  });

  emitter.on('error', (data) => {
    const parsedData = JSON.parse(data);
    ws.send(
      JSON.stringify({
        type: 'error',
        data: parsedData.data,
        key: 'CHAIN_ERROR',
      }),
    );
  });
};

/**
 * 处理简历分析结果请求
 * @param parsedWSMessage WebSocket消息
 * @param ws WebSocket连接
 * @returns 是否成功处理
 */
const handleResumeAnalysisResult = async (
  parsedWSMessage: WSMessage,
  ws: WebSocket
): Promise<boolean> => {
  try {
    const taskId = parsedWSMessage.taskId;
    if (!taskId) {
      ws.send(JSON.stringify({
        type: 'error',
        data: '缺少任务ID',
        key: 'MISSING_TASK_ID',
      }));
      return false;
    }

    // 获取任务链上的所有结果
    const taskResults = await taskQueueManager.getTaskChainResults(taskId);
    
    // 检查是否有完成的分析结果
    if (!taskResults.completed || !taskResults.completed.result) {
      ws.send(JSON.stringify({
        type: 'error',
        data: '简历分析任务尚未完成或结果不可用',
        key: 'TASK_NOT_COMPLETED',
      }));
      return false;
    }

    // 获取分析结果
    const analysisResult = taskResults.completed.result;
    const chatId = parsedWSMessage.message.chatId;
    
    // 获取简历和职位文本
    const { resumeText, jobText } = await taskQueueManager.getResumeAnalysisTexts(taskId);

    const currentDate = new Date().toISOString();
    const customPrompt = prompts.interactiveResumeAdvisorPrompt
      .replace('{resumeText}', resumeText || '未提供简历内容')
      .replace('{jobText}', jobText || '未提供岗位描述')
      .replace('{analysisResults}', analysisResult.summaryAnalysis || '')
      .replace('{date}', currentDate);
    
    // 更新searchHandlers中resumeAnalyzer的responsePrompt
    searchHandlers.resumeAnalyzer = new MetaSearchAgent({
      activeEngines: [],
      queryGeneratorPrompt: '',
      responsePrompt: customPrompt,
      rerank: false,
      rerankThreshold: 0,
      searchWeb: false,
      searchKnowledge: false,
      knowledgeChannel: '',
      summarizer: true,
    });
    
    logger.info(`简历分析会话提示已准备，任务ID: ${taskId}, 会话ID: ${chatId}`);
    
    // 发送准备就绪信号给客户端
    ws.send(
      JSON.stringify({
        type: 'resumeAnalysisReady',
        data: {
          chatId: chatId,
          taskId: taskId
        },
        key: 'RESUME_ANALYSIS_READY',
      }),
    );
    
    return true;
  } catch (error) {
    logger.error(`简历分析会话提示准备失败: ${error.message}`);
    ws.send(
      JSON.stringify({
        type: 'error',
        data: `处理简历分析结果失败: ${error.message}`,
        key: 'RESUME_ANALYSIS_ERROR',
      }),
    );
    return false;
  }
};

/**
 * 处理普通消息请求
 * @param parsedWSMessage WebSocket消息
 * @param ws WebSocket连接
 * @param llm 语言模型
 * @param embeddings 嵌入模型
 * @returns 是否成功处理
 */
const handleNormalMessage = async (
  parsedWSMessage: WSMessage,
  ws: WebSocket,
  llm: BaseChatModel,
  embeddings: Embeddings,
  summarizerLLM: BaseChatModel
): Promise<boolean> => {
  try {
    const parsedMessage = parsedWSMessage.message;
    let optimizationMode = parsedWSMessage.optimizationMode;

    if (parsedWSMessage.files.length > 0) {
      // 如果上传了文件，则为写作助手模式，不进行网络搜索
      parsedWSMessage.focusMode = 'writingAssistant';
      optimizationMode = 'quality';
    }

    // 使用统一的方法生成消息ID
    const humanMessageId =
      parsedMessage.messageId ?? MessageUtils.generateMessageId();
    const aiMessageId = MessageUtils.generateMessageId();

    if (!parsedMessage.content) {
      ws.send(
        JSON.stringify({
          type: 'error',
          data: 'Invalid message format',
          key: 'INVALID_FORMAT',
        }),
      );
      return false;
    }

    // 处理历史消息
    const summarizedHistory = await summarizeHistory(parsedWSMessage.history);
    const history: BaseMessage[] = summarizedHistory.map((msg) => {
      if (msg[0] === 'human') {
        return new HumanMessage({
          content: msg[1],
        });
      } else {
        return new AIMessage({
          content: msg[1],
        });
      }
    });

    const handler: MetaSearchAgentType =
      searchHandlers[parsedWSMessage.focusMode];

    if (handler) {
      const emitter = await handler.searchAndAnswer(
        parsedMessage.content,
        history,
        llm,
        summarizerLLM,
        embeddings,
        optimizationMode,
        parsedWSMessage.files,
      );
      
      handleEmitterEvents(emitter, ws, aiMessageId, parsedMessage.chatId);
      
      await MessageUtils.createOrGetChat({
        chatId: parsedMessage.chatId,
        title: parsedMessage.content,
        focusMode: parsedWSMessage.focusMode,
        deviceId: parsedMessage.deviceId,
        userId: parsedMessage.userId,
        source: parsedWSMessage.source || 'web',
        files: parsedWSMessage.files.map(getFileDetails),
        hotQuestionId: parsedWSMessage.hotQuestionId || null
      });
      
      const messageExists = await db.select()
        .from(messagesSchema)
        .where(eq(messagesSchema.messageId, humanMessageId))
        .limit(1);

      if (!messageExists.length) {
        await MessageUtils.createMessage(
          parsedMessage.content,
          parsedMessage.chatId,
          'user'
        );
      } else {
        await db.delete(messagesSchema)
          .where(
            and(
              gt(messagesSchema.id, messageExists[0].id),
              eq(messagesSchema.chatId, parsedMessage.chatId),
            ),
          );
      }
      
      return true;
    } else {
      ws.send(
        JSON.stringify({
          type: 'error',
          data: 'Invalid focus mode',
          key: 'INVALID_FOCUS_MODE',
        }),
      );
      return false;
    }
  } catch (error) {
    logger.error(`处理普通消息失败: ${error.message}`);
    ws.send(
      JSON.stringify({
        type: 'error',
        data: `处理消息失败: ${error.message}`,
        key: 'MESSAGE_PROCESSING_ERROR',
      }),
    );
    return false;
  }
};

export const handleMessage = async (
  message: string,
  ws: WebSocket,
  llm: BaseChatModel,
  embeddings: Embeddings,
) => {
  try {
    const parsedWSMessage = JSON.parse(message) as WSMessage;
    
    // 获取总结模型实例
    const summarizerLLM = await getSummarizerModel() as unknown as BaseChatModel;
    
    // 根据消息类型分发到不同的处理函数
    if (parsedWSMessage.type === 'resumeAnalyzer') {
      await handleResumeAnalysisResult(parsedWSMessage, ws);
    } else if (parsedWSMessage.type === 'message') {
      await handleNormalMessage(parsedWSMessage, ws, llm, embeddings, summarizerLLM);
    } else {
      ws.send(
        JSON.stringify({
          type: 'error',
          data: '不支持的消息类型',
          key: 'UNSUPPORTED_MESSAGE_TYPE',
        }),
      );
    }
  } catch (err) {
    ws.send(
      JSON.stringify({
        type: 'error',
        data: 'Invalid message format',
        key: 'INVALID_FORMAT',
      }),
    );
    logger.error(`Failed to handle message: ${err}`);
  }
};
