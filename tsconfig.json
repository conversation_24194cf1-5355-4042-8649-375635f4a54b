{"compilerOptions": {"lib": ["ESNext", "es2020", "dom"], "module": "Node16", "moduleResolution": "Node16", "target": "ESNext", "outDir": "dist", "sourceMap": false, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "skipDefaultLibCheck": true}, "include": ["src"], "exclude": ["node_modules", "**/*.spec.ts"]}