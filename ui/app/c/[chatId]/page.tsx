'use client';

import ChatWindow from '@/components/ChatWindow';
import BackNavigationWrapper from '@/components/common/BackNavigationWrapper';
import Navbar from '@/components/Navbar';
import { useRef, useState } from 'react';
import { useParams } from 'next/navigation';

const Page = () => {
  const params = useParams() as any;
  const chatId = params.chatId as string;
  const [messages, setMessages] = useState([] as any[]);
  console.log("🚀 ~ Page ~ messages:", messages)
  const title = messages?.[0]?.content || '';
  console.log("🚀 ~ Page ~ title:", title)

  return (<div className="flex flex-col h-full">
    <div className="sticky top-0 z-10 bg-white dark:bg-dark-primary border-b border-gray-200 dark:border-gray-800 px-4">
      <div className="container mx-auto lg:px-4 py-1">
        <BackNavigationWrapper title={title}>
          <Navbar messages={messages} chatId={chatId} />
        </BackNavigationWrapper>
      </div>
    </div>
    <div className="flex-1 overflow-auto">
      <ChatWindow id={chatId} asyncMessages={setMessages} />
    </div>
  </div>);
};

export default Page;