import React from 'react';
import { Metadata } from 'next';
import BackNavigationWrapper from '@/components/common/BackNavigationWrapper';

export const metadata: Metadata = {
  title: '职场技能陪练 - 灵通',
  description: '通过职场技能陪练提升你的职场竞争力',
};

export default function CareerCoachLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col h-full">
      <div className="sticky top-0 z-10 bg-white dark:bg-dark-primary border-b border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4 py-2">
          <BackNavigationWrapper title="职场技能陪练" />
        </div>
      </div>
      <div className="flex-1 overflow-auto">
        {children}
      </div>
    </div>
  );
}
