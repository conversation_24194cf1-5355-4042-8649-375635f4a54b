'use client';

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useRouter } from 'next/navigation';
import careerCoachClient, { CareerCoachEvaluationResult } from '@/lib/careerCoachClient';
import { Play, CheckCircle, XCircle, RefreshCw, Shuffle, BookOpen } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import useScrollDetection from '@/hooks/useScrollDetection';

interface Topic {
  id: string;
  title: string;
  sub_title: string;
  module_id: string;
  module_name: string;
}

interface Question {
  id: string;
  query: string;
  subject: string;
  knowledge_points?: string;
  correct_answer?: string;
}

const CareerCoachPage = () => {
  const router = useRouter();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [knowledgeText, setKnowledgeText] = useState('');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [isEvaluating, setIsEvaluating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showKnowledge, setShowKnowledge] = useState(false);
  const [showQuestionDetail, setShowQuestionDetail] = useState(false);
  const [questionDetail, setQuestionDetail] = useState<{
    question: string;
    options: string;
    correct_answer: string;
  } | null>(null);
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  const [evaluationResult, setEvaluationResult] = useState<Partial<CareerCoachEvaluationResult> | null>(null);
  const [partialData, setPartialData] = useState('');
  const [showSection, setShowSection] = useState<{
    score: boolean;
    strengths: boolean;
    weaknesses: boolean;
    improvements: boolean;
    keyPoints: boolean;
    referenceAnswer: boolean;
    passed: boolean;
    starRating: boolean;
    summary: boolean;
  }>({
    score: false,
    strengths: false,
    weaknesses: false,
    improvements: false,
    keyPoints: false,
    referenceAnswer: false,
    passed: false,
    starRating: false,
    summary: false,
  });
  const [partialTexts, setPartialTexts] = useState<{
    score: string;
    strengths: string[];
    weaknesses: string[];
    improvements: string;
    keyPoints: string[];
    referenceAnswer: string;
    passed?: boolean;
    starRating?: number;
    summary?: string;
  }>({
    score: '',
    strengths: [],
    weaknesses: [],
    improvements: '',
    keyPoints: [],
    referenceAnswer: '', 
    starRating: 0,
    summary: '',
  });
  const resultContainerRef = useRef<HTMLDivElement>(null);
  const { userScrolled } = useScrollDetection('window');

  const [isGeneratingAnswer, setIsGeneratingAnswer] = useState(false);
  const [referenceAnswer, setReferenceAnswer] = useState('');
  const [partialReferenceAnswer, setPartialReferenceAnswer] = useState('');

  // 重置显示区域
  const resetShowSections = () => {
    setShowSection({
      score: false,
      strengths: false,
      weaknesses: false,
      improvements: false,
      keyPoints: false,
      referenceAnswer: false,
      passed: false,
      starRating: false,
      summary: false,
    });
  };

  // 加载主题和问题
  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setIsLoading(true);
        const response = await careerCoachClient.getTopics();
        if (response.status === 'success' && response.topics) {
          setTopics(response.topics);
          
          if (response.topics.length > 0) {
            const defaultTopic = response.topics[0];
            setSelectedTopic(defaultTopic);
            
            // 加载默认主题的问题
            await loadQuestionsAndKnowledge(defaultTopic.id);
          }
        } else {
          throw new Error('获取主题失败');
        }
      } catch (error) {
        console.error('加载主题和问题失败:', error);
        toast('加载主题和问题失败，请刷新页面重试');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTopics();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 加载问题和知识点
  const loadQuestionsAndKnowledge = async (topicId: string) => {
    try {
      setIsLoading(true);
      
      // 加载问题
      const questionsResponse = await careerCoachClient.getQuestions(topicId);
      if (questionsResponse.status === 'success' && questionsResponse.questions) {
        setQuestions(questionsResponse.questions);
        
        // 如果有问题，预加载第一个问题的详情
        if (questionsResponse.questions.length > 0) {
          await loadQuestionDetail(questionsResponse.questions[0].id, false);
        }
      } else {
        throw new Error('获取问题失败');
      }
      
      // 加载知识点
      const knowledgeResponse = await careerCoachClient.getKnowledge(topicId);
      if (knowledgeResponse.status === 'success') {
        setKnowledgeText(knowledgeResponse.knowledge_text || '');
      }
    } catch (error) {
      console.error('加载问题和知识点失败:', error);
      toast('加载问题和知识点失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 切换主题
  const handleTopicChange = async (topicId: string) => {
    const topic = topics.find(t => t.id == topicId);
    if (topic) {
      try {
        // 先设置加载状态
        setIsLoading(true);
        
        // 加载新主题的问题和知识点
        await loadQuestionsAndKnowledge(topicId);
        
        // 加载成功后再更新主题和重置状态
        setSelectedTopic(topic);
        setCurrentQuestionIndex(0);
        setUserAnswer('');
        setEvaluationResult(null);
        setPartialData('');
        resetShowSections();
        setShowKnowledge(false);
        setShowQuestionDetail(false);
        setQuestionDetail(null);
      } catch (error) {
        console.error('切换主题失败:', error);
        toast('切换主题失败，请重试');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // 生成随机答案
  const generateRandomAnswer = () => {
    if (!questions.length) return;

    const randomAnswers = [
      `我认为这个问题的关键在于理解职场中的沟通和协作。我会采取积极主动的态度，先了解情况，然后寻求最佳解决方案。`,
      `在这种情况下，我会先分析问题的根本原因，然后与团队成员沟通，共同制定解决方案。保持专业和尊重是关键。`,
      `我会采取系统性的方法解决这个问题。首先收集信息，然后评估可能的解决方案，最后实施并跟进结果。团队合作和有效沟通是成功的关键。`
    ];
    
    setUserAnswer(randomAnswers[Math.floor(Math.random() * randomAnswers.length)]);
  };
  
  // 加载问题详情
  const loadQuestionDetail = async (questionId: string, showDialog = true) => {
    try {
      setIsLoadingDetail(true);
      const response = await careerCoachClient.getQuestionDetail(questionId);
      if (response.status === 'success') {
        setQuestionDetail({
          question: response.question,
          options: response.options,
          correct_answer: response.correct_answer
        });
        if (showDialog) {
          setShowQuestionDetail(true);
        }
      } else {
        throw new Error('获取问题详情失败');
      }
    } catch (error) {
      console.error('加载问题详情失败:', error);
      if (showDialog) {
        toast('加载问题详情失败，请重试');
      }
    } finally {
      setIsLoadingDetail(false);
    }
  };

  // 下一个问题
  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      const nextIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(nextIndex);
      setUserAnswer('');
      setEvaluationResult(null);
      setPartialData('');
      resetShowSections();
      setShowKnowledge(false);
      setShowQuestionDetail(false);
      
      // 预加载下一个问题的详情
      if (questions[nextIndex]) {
        loadQuestionDetail(questions[nextIndex].id, false);
      }
    }
  };

  // 提交评估
  const submitForEvaluation = async () => {
    if (!userAnswer) {
      toast('请先完成回答');
      return;
    }

    if (!selectedTopic || !questions.length) {
      toast('数据加载错误，请刷新页面重试');
      return;
    }
    
    setIsEvaluating(true);
    resetShowSections();
    setEvaluationResult(null);
    setPartialData('');
      
    // 获取当前问题
    const currentQuestion = questions[currentQuestionIndex];
    
    // 如果问题详情还没加载，尝试加载
    if (!questionDetail) {
      await loadQuestionDetail(currentQuestion.id, false);
    }
    
    // 准备评估请求
    const request = {
      topic: selectedTopic.title,
      knowledge_points: knowledgeText,
      question: currentQuestion.query,
      options: questionDetail?.options || currentQuestion.subject,
      correct_answer: questionDetail?.correct_answer || '',
      user_answer: userAnswer
    };
      
    // 使用流式API评估
    careerCoachClient.streamEvaluateAnswer(request, {
      onPartial: (dataChunk) => {
        console.log("收到部分数据:", dataChunk); 
        
        setPartialData(prevStream => {
          const newStream = prevStream + dataChunk;
          
          // 1. 更新模块可见性 (提前触发)
          setShowSection(prevShow => {
            const newShow = { ...prevShow };

            // 定义模块触发器 (键名 + 初始字符)
            const triggers = {
              score: /"score"\s*:\s*/,
              strengths: /"strengths"\s*:\s*\[/,
              weaknesses: /"weaknesses"\s*:\s*\[/,
              improvements: /"improvements"\s*:\s*"/,
              keyPoints: /"key_points"\s*:\s*\[/,
              referenceAnswer: /"reference_answer"\s*:\s*"/,
              passed: /"passed"\s*:\s*(t|f|tr|fa|tru|fal)/,
              starRating: /"star_rating"\s*:\s*/,
              summary: /"summary"\s*:\s*"/,
            };

            for (const key of Object.keys(triggers) as Array<keyof typeof triggers>) {
              if (!prevShow[key] && triggers[key].test(newStream)) {
                newShow[key] = true;
                console.log(`模块 ${key} 已触发显示。`);
              }
            }
            
            return newShow;
          });

          // 2. 更新 partialTexts 以实现打字机效果 (使用渐进式正则表达式)
          setPartialTexts(prevTexts => {
            const newTexts = { ...prevTexts };

            // 分数 (渐进式数字)
            const scoreMatch = newStream.match(/"score"\s*:\s*(\d*)/);
            if (scoreMatch) {
              newTexts.score = scoreMatch[1]; // scoreMatch[1] 会随着数字的输入而增长
            }
            
            // 优点
            const strengthsMatch = newStream.match(/"strengths"\s*:\s*\[([\s\S]*?)(\]|$)/); // 匹配到数组结束或流结束
            if (strengthsMatch) {
              const strengthsText = strengthsMatch[1]; // 括号内的原始文本，可能不完整
              const strengths = strengthsText.split(',')
                .map(s => s.trim().replace(/^"|"$/g, ''))
                .filter(s => s);
              newTexts.strengths = strengths;
            }
            
            // 不足
            const weaknessesMatch = newStream.match(/"weaknesses"\s*:\s*\[([\s\S]*?)(\]|$)/);
            if (weaknessesMatch) {
              const weaknessesText = weaknessesMatch[1];
              const weaknesses = weaknessesText.split(',')
                .map(s => s.trim().replace(/^"|"$/g, ''))
                .filter(s => s);
              newTexts.weaknesses = weaknesses;
            }
            
            // 改进建议
            const improvementsMatch = newStream.match(/"improvements"\s*:\s*"([\s\S]*?)("|$)/); // 匹配到字符串结束或流结束
            if (improvementsMatch) {
              newTexts.improvements = improvementsMatch[1]; // improvementsMatch[1] 会随文本输入增长
            }

            // 参考回答
            const referenceAnswerMatch = newStream.match(/"reference_answer"\s*:\s*"([\s\S]*?)("|$)/);
            if (referenceAnswerMatch) {
              newTexts.referenceAnswer = referenceAnswerMatch[1];
            }
            
            // 关键点
            const keyPointsMatch = newStream.match(/"key_points"\s*:\s*\[([\s\S]*?)(\]|$)/);
            if (keyPointsMatch) {
              const keyPointsText = keyPointsMatch[1];
              const keyPoints = keyPointsText.split(',')
                .map(s => s.trim().replace(/^"|"$/g, ''))
                .filter(s => s);
              newTexts.keyPoints = keyPoints;
            }
            
            // 星级评定
            const starRatingMatch = newStream.match(/"star_rating"\s*:\s*(\d+)/);
            if (starRatingMatch) {
              newTexts.starRating = parseInt(starRatingMatch[1]);
            }

            // 教练建议
            const summaryMatch = newStream.match(/"summary"\s*:\s*"([\s\S]*?)("|$)/);
            if (summaryMatch) {
              newTexts.summary = summaryMatch[1];
            }

            // 通过状态 (布尔值)
            const passedMatch = newStream.match(/"passed"\s*:\s*(true|false)/); // 这个匹配完整的 true/false
            if (passedMatch) {
              newTexts.passed = passedMatch[1] === 'true';
            }
            return newTexts;
          });
          
          // 在接收到部分数据且正在评估时，滚动以显示最新内容
          if (isEvaluating) { 
            setTimeout(() => {
              if (resultContainerRef.current) {
                console.log("尝试滚动到结果容器 (onPartial 统一滚动)");
                resultContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
              } else {
                console.log("结果容器引用不存在 (onPartial 统一滚动)");
              }
            }, 100); // 100ms 延迟确保 DOM 更新
          }
          
          return newStream; // 返回累积的数据流
        });
      },
      onFull: (data) => {
        setEvaluationResult(data);
      },
      onComplete: (data) => {
        setEvaluationResult(data);
        setIsEvaluating(false);
        
        // 确保所有部分都显示出来
        setShowSection({
          score: true,
          strengths: true,
          weaknesses: true,
          improvements: true,
          referenceAnswer: true,
          keyPoints: true,
          passed: true,
          starRating: true,
          summary: true,
        });
        
        // 滚动到结果底部
        setTimeout(() => {
          if (resultContainerRef.current) {
            resultContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
          }
        }, 200);
      },
      onError: (error) => {
        console.error('评估错误:', error);
        toast('评估过程中出现错误，请重试');
        setIsEvaluating(false);
      },
      onClose: () => {
        setIsEvaluating(false);
      }
    });
  };

  // 更新可见部分的函数
  const updateVisibleSections = (data: any) => {
    if (!data) {
      console.log("updateVisibleSections: 没有数据");
      return;
    }
    
    let shouldScroll = false;
    
    setShowSection(prev => {
      const newState = { ...prev };
      
      // 检查各个部分是否存在，如果存在且之前未显示，则显示
      if (data.score !== undefined && !prev.score) {
        console.log("显示分数部分");
        newState.score = true;
        shouldScroll = true;
      }
      
      if (data.analysis?.strengths && !prev.strengths) {
        console.log("显示优点部分");
        newState.strengths = true;
        shouldScroll = true;
      }
      
      if (data.analysis?.weaknesses && !prev.weaknesses) {
        console.log("显示不足部分");
        newState.weaknesses = true;
        shouldScroll = true;
      }
      
      if (data.improvements && !prev.improvements) {
        console.log("显示改进建议部分");
        newState.improvements = true;
        shouldScroll = true;
      }
      
      if (data.reference_answer &&!prev.referenceAnswer) {
        console.log("显示参考回答部分");
        newState.referenceAnswer = true;
        shouldScroll = true;
      }

      if (data.key_points && !prev.keyPoints) {
        console.log("显示关键点部分");
        newState.keyPoints = true;
        shouldScroll = true;
      }
      
      return newState;
    });
    
    console.log("shouldScroll:", shouldScroll, "userScrolled:", userScrolled);
    
    // 如果有新部分显示，强制滚动到结果容器
    if (shouldScroll) {
      console.log("尝试滚动到结果容器");
      // 使用更直接的滚动方法
      if (resultContainerRef.current) {
        // 尝试多种滚动方法
        resultContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
        
        // 备用滚动方法
        setTimeout(() => {
          window.scrollTo({
            top: resultContainerRef.current!.offsetTop + resultContainerRef.current!.offsetHeight,
            behavior: 'smooth'
          });
        }, 150);
      } else {
        console.log("结果容器引用不存在");
      }
    }
  };

  // 清理JSON字符串，修复常见格式问题
  const cleanupJsonString = (jsonStr: string): string => {
    try {
      // 1. 尝试找到最后一个有效的JSON结构
      // 查找最后一个完整的JSON对象
      const lastCompleteObjectMatch = jsonStr.match(/{(?:[^{}]|{[^{}]*})*}/g);
      if (lastCompleteObjectMatch && lastCompleteObjectMatch.length > 0) {
        return lastCompleteObjectMatch[lastCompleteObjectMatch.length - 1];
      }
      
      // 2. 如果没有找到完整的JSON对象，尝试基本的修复
      // 移除可能导致解析错误的字符
      let cleaned = jsonStr;
      
      // 处理未配对的引号
      const quoteCount = (cleaned.match(/"/g) || []).length;
      if (quoteCount % 2 !== 0) {
        // 有未闭合的引号，尝试找到最后一个完整的属性
        const lastValidPropEnd = cleaned.lastIndexOf('",');
        if (lastValidPropEnd !== -1) {
          cleaned = cleaned.substring(0, lastValidPropEnd + 2) + '}';
        } else {
          // 无法修复，返回原始字符串
          return jsonStr;
        }
      }
      
      // 处理括号不匹配
      const openBraces = (cleaned.match(/{/g) || []).length;
      const closeBraces = (cleaned.match(/}/g) || []).length;
      
      if (openBraces > closeBraces) {
        // 添加缺少的闭合括号
        cleaned += '}'.repeat(openBraces - closeBraces);
      } else if (closeBraces > openBraces) {
        // 移除多余的闭合括号
        let excess = closeBraces - openBraces;
        while (excess > 0 && cleaned.endsWith('}')) {
          cleaned = cleaned.substring(0, cleaned.length - 1);
          excess--;
        }
        // 确保JSON以}结束
        if (!cleaned.endsWith('}')) {
          cleaned += '}';
        }
      }
      
      // 处理属性名没有引号的情况
      cleaned = cleaned.replace(/([{,]\s*)([a-zA-Z0-9_]+)(\s*:)/g, '$1"$2"$3');
      
      // 处理属性值没有引号的情况（除了数字、true、false、null）
      cleaned = cleaned.replace(/:(\s*)([^"{}\[\],\s][^{}\[\],]*[^"{}\[\],\s])(\s*[,}])/g, (match, before, value, after) => {
        if (/^(true|false|null|\d+|\d+\.\d+)$/.test(value.trim())) {
          return `:${before}${value}${after}`;
        }
        return `:${before}"${value.trim()}"${after}`;
      });
      
      return cleaned;
    } catch (e) {
      console.error("清理JSON字符串时出错:", e);
      return jsonStr;
    }
  };

  // 生成参考答案
  const generateReferenceAnswer = async () => {
    if (!selectedTopic || !questions.length) {
      toast('数据加载错误，请刷新页面重试');
      return;
    }
    
    setIsGeneratingAnswer(true);
    setReferenceAnswer('');
    setPartialReferenceAnswer('');
      
    const currentQuestion = questions[currentQuestionIndex];
    
    if (!questionDetail) {
      await loadQuestionDetail(currentQuestion.id, false);
    }
    
    const request = {
      topic: selectedTopic.title,
      knowledge_points: knowledgeText,
      question: currentQuestion.query,
      options: questionDetail?.options || currentQuestion.subject,
      correct_answer: questionDetail?.correct_answer || ''
    };
      
    // 使用流式API生成
    careerCoachClient.streamGenerateAnswer(request, {
      onPartial: (dataChunk) => {
        setPartialReferenceAnswer(prev => prev + dataChunk);
      },
      onComplete: (data) => {
        setReferenceAnswer(data as any);
      },
      onError: (error) => {
        console.error('生成参考答案失败:', error);
        toast('生成参考答案失败，请重试');
        setIsGeneratingAnswer(false);
      },
      onClose: () => {
        setIsGeneratingAnswer(false);
      }
    });
  };

  // 重新开始
  const restart = () => {
    setCurrentQuestionIndex(0);
    setUserAnswer('');
    setEvaluationResult(null);
    setPartialData('');
    resetShowSections();
    setShowKnowledge(false);
  };

  // 切换知识点显示
  const toggleKnowledge = () => {
    setShowKnowledge(!showKnowledge);
  };

  // 切换问题详情显示
  const toggleQuestionDetail = () => {
    if (!showQuestionDetail && !questionDetail) {
      // 如果问题详情还没加载，先加载
      if (questions.length > 0) {
        loadQuestionDetail(questions[currentQuestionIndex].id);
      }
    } else {
      // 直接切换显示状态
      setShowQuestionDetail(!showQuestionDetail);
    }
  };

  // 尝试解析部分JSON数据
  const tryParsePartialJson = () => {
    try {
      // 查找JSON开始的位置
      const startPos = partialData.indexOf('{');
      if (startPos === -1) return null;
      
      // 提取JSON部分并尝试清理
      const jsonStr = partialData.substring(startPos);
      const cleanedJson = cleanupJsonString(jsonStr);
      
      // 尝试解析
      try {
        return JSON.parse(cleanedJson);
      } catch (e) {
        console.log("JSON解析错误:", e);
        return null;
      }
    } catch (e) {
      console.log("尝试解析JSON时出错:", e);
      return null;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-center items-center h-16">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mr-2"></div>
          <span className="text-gray-600 text-sm">加载中...</span>
        </div>
      </div>
    );
  }

  if (!selectedTopic || !questions.length) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">暂无可用的测试主题</h2>
          <p>请稍后再试或联系管理员</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-center mb-8">职场技能陪练</h1>
      
      <div>
        {/* 主题选择 */}
        <div className="mb-6">
          <label htmlFor="topic-select" className="block text-sm font-medium mb-2">选择训练主题</label>
          <select
            id="topic-select"
            value={selectedTopic?.id || ''}
            onChange={(e) => handleTopicChange(e.target.value)}
            className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isEvaluating}
          >
            {topics.map(topic => (
              <option key={topic.id} value={topic.id}>{topic.title} - {topic.sub_title}</option>
            ))}
          </select>
        </div>
        
        <div className="flex justify-between items-center mb-6">
          <div className="w-24"></div> {/* 占位，保持标题居中 */}
          <h2 className="text-xl font-semibold">{selectedTopic.title}</h2>
          <button 
            onClick={restart}
            className="text-blue-500 hover:text-blue-700 flex items-center"
            disabled={isEvaluating}
          >
            <RefreshCw className="w-4 h-4 mr-1" /> 重新开始
          </button>
        </div>
        
        <div className="mb-8">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">问题 {currentQuestionIndex + 1}/{questions.length}</h3>
              <div className="flex space-x-2">
                <button
                  onClick={toggleKnowledge}
                  className="flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800"
                  disabled={isEvaluating}
                >
                  <BookOpen className="w-4 h-4 mr-1" />
                  {showKnowledge ? '隐藏知识点' : '查看知识点'}
                </button>
                <button
                  onClick={toggleQuestionDetail}
                  className="flex items-center px-3 py-1 text-sm bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-md hover:bg-green-200 dark:hover:bg-green-800"
                  disabled={isEvaluating || isLoadingDetail}
                >
                  {isLoadingDetail ? (
                    <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                  ) : (
                    <BookOpen className="w-4 h-4 mr-1" />
                  )}
                  {showQuestionDetail ? '隐藏样例' : '查看样例'}
                </button>
              </div>
            </div>
            <p className="mb-4">{questions[currentQuestionIndex].query}</p>
            {/* 知识点显示区域 */}
            {showKnowledge && knowledgeText && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2">相关知识点</h4>
                <p className="text-sm whitespace-pre-line">{knowledgeText}</p>
              </div>
            )}
            {/* 问题详情显示区域 */}
            {showQuestionDetail && questionDetail && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2">回答样例</h4>
                <div className="text-sm whitespace-pre-line">
                  <p className="mb-2"><strong>选项：</strong><br/>{questionDetail.options}</p>
                  <p><strong>解析：</strong><br/>{questionDetail.correct_answer}</p>
                </div>
              </div>
            )}
          </div>
          
          {/* 添加生成参考答案按钮 */}
          <button
            onClick={generateReferenceAnswer}
            disabled={isGeneratingAnswer}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isGeneratingAnswer ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                <BookOpen className="w-4 h-4" />
                获得灵感
              </>
            )}
          </button>

          {/* 显示参考答案 */}
          {(partialReferenceAnswer || referenceAnswer) && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-2">灵感：</h3>
              <div className="prose max-w-none">
                {referenceAnswer || partialReferenceAnswer}
              </div>
            </div>
          )}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">你的回答</h3>
              <div className="flex space-x-2">
                <button
                  onClick={generateRandomAnswer}
                  disabled={isEvaluating}
                  className="flex items-center text-white bg-purple-500 hover:bg-purple-600 px-3 py-1 rounded disabled:opacity-50"
                >
                  <Shuffle className="w-4 h-4 mr-1" /> 随机回答
                </button>
              </div>
            </div>
            
            <textarea
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              placeholder="在这里输入你的回答，或使用随机回答功能..."
              className="w-full h-20 p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isEvaluating}
            />
            
            <div className="flex justify-end mt-2">
              <button
                onClick={submitForEvaluation}
                disabled={!userAnswer || isEvaluating}
                className="flex items-center text-white bg-green-500 hover:bg-green-600 px-4 py-2 rounded disabled:opacity-50"
              >
                {isEvaluating ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> 评估中...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" /> 提交评估
                  </>
                )}
              </button>
            </div>
          </div>
          
          {/* 评估结果区域 - 使用流式数据动态显示 */}
          {(partialData || evaluationResult) && (
            <div 
              className="bg-white dark:bg-gray-900 border rounded-lg p-6 shadow-sm"
              ref={resultContainerRef}
            >
              <h3 className="text-xl font-semibold mb-4">评估结果</h3>
              
              {/* 分数和通过状态 */}
              {showSection.score && (
                <div className="mb-6 flex items-center">
                  <div className="text-4xl font-bold mr-4">
                    {evaluationResult?.score || partialTexts.score}
                  </div>
                  <div>
                    {(evaluationResult?.passed || partialTexts.passed) ? (
                      <div className="flex items-center text-green-500">
                        <CheckCircle className="w-5 h-5 mr-1" /> 通过
                      </div>
                    ) : (
                      <div className="flex items-center text-red-500">
                        <XCircle className="w-5 h-5 mr-1" /> 未通过
                      </div>
                    )}
                    <div className="text-sm text-gray-500">
                      {(evaluationResult?.passed || partialTexts.passed) ? '恭喜你通过了这次评估！' : '再接再厉，继续努力！'}
                    </div>
                  </div>
                </div>
              )}
              
              {/* 星级评定 */}
              {showSection.starRating && (evaluationResult?.star_rating || partialTexts.starRating) && (
                <div className="mb-4">
                  <h4 className="font-medium text-yellow-600 mb-2">星级评定</h4>
                  <div className="text-2xl font-bold text-yellow-500">
                    {evaluationResult?.star_rating || partialTexts.starRating}
                  </div>
                </div>
              )}
              
              {/* 教练建议 */}
              {showSection.summary && (evaluationResult?.summary || partialTexts.summary) && (
                <div className="mb-4">
                  <h4 className="font-medium text-yellow-600 mb-2">教练建议</h4>
                  <p className="text-gray-700 dark:text-gray-300">{evaluationResult?.summary || partialTexts.summary}</p>
                </div>
              )}
              
              {/* 优点分析 */}
              {showSection.strengths && (evaluationResult?.analysis?.strengths || partialTexts.strengths.length > 0) && (
                <div className="mb-4">
                  <h4 className="font-medium text-green-600 mb-2">优点分析</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {(evaluationResult?.analysis?.strengths || partialTexts.strengths).map((strength, index) => (
                      <li key={index} className="text-gray-700 dark:text-gray-300">{strength}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {/* 不足分析 */}
              {showSection.weaknesses && (evaluationResult?.analysis?.weaknesses || partialTexts.weaknesses.length > 0) && (
                <div className="mb-4">
                  <h4 className="font-medium text-red-600 mb-2">不足分析</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {(evaluationResult?.analysis?.weaknesses || partialTexts.weaknesses).map((weakness, index) => (
                      <li key={index} className="text-gray-700 dark:text-gray-300">{weakness}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {/* 改进建议 */}
              {showSection.improvements && (evaluationResult?.improvements || partialTexts.improvements) && (
                <div className="mb-4">
                  <h4 className="font-medium text-purple-600 mb-2">改进建议</h4>
                  <p className="text-gray-700 dark:text-gray-300">{evaluationResult?.improvements || partialTexts.improvements}</p>
                </div>
              )}
              
              {/* 参考答案 */}
              {showSection.referenceAnswer && (evaluationResult?.referenceAnswer || partialTexts.referenceAnswer) && (
                <div className="mb-4">
                  <h4 className="font-medium text-yellow-600 mb-2">参考答案</h4>
                  <p className="text-gray-700 dark:text-gray-300">{evaluationResult?.referenceAnswer || partialTexts.referenceAnswer}</p>
                </div>
              )}
              
              {/* 关键点 */}
              {showSection.keyPoints && (evaluationResult?.key_points || partialTexts.keyPoints.length > 0) && (
                <div className="mb-4">
                  <h4 className="font-medium text-teal-600 mb-2">需要掌握的关键点</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {(evaluationResult?.key_points || partialTexts.keyPoints).map((point, index) => (
                      <li key={index} className="text-gray-700 dark:text-gray-300">{point}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
          {/* 下一题按钮 */}
          {currentQuestionIndex < questions.length - 1 && (
            <div className="flex justify-end mt-4">
              <button
                onClick={nextQuestion}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              >
                下一题
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CareerCoachPage;
