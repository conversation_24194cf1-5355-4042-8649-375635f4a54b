import type { Metadata } from 'next';
import { Montserrat } from 'next/font/google';
import './globals.css';
import { cn } from '@/lib/utils';
import Sidebar from '@/components/Sidebar';
import { ResponsiveToaster } from '@/components/responsive-toaster';
import ThemeProvider from '@/components/theme/Provider';
import Script from 'next/script';
import type { Viewport } from 'next';
import { ExpandedProvider } from '@/components/context/ExpandedContext';
import { SelectedChatProvider } from '@/components/context/SelectedChatContext';
import { Suspense } from 'react';

const montserrat = Montserrat({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  fallback: ['Arial', 'sans-serif'],
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: '灵通',
  description: '灵通是一个连接互联网的 AI 智能助手。',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <Suspense>
      <html className="h-full bg-white dark:bg-black" lang="zh" suppressHydrationWarning>
        <head>
          {/* --- 资源预加载 --- */}
          {/* 示例：预加载字体文件 (注意: Next.js 的 next/font 通常会自动优化字体加载) */}
          {/* <link
            rel="preload"
            href="/path/to/your/font.woff2" // 替换为实际的字体文件路径
            as="font"
            type="font/woff2"
            crossOrigin="anonymous"
          /> */}

          {/* 示例：预加载关键 CSS 文件 */}
          {/* <link
            rel="preload"
            href="/path/to/critical.css" // 替换为实际的 CSS 文件路径
            as="style"
          /> */}

          {/* 示例：预加载关键脚本 */}
          {/* <link
            rel="preload"
            href="/path/to/critical.js" // 替换为实际的 JS 文件路径
            as="script"
          /> */}

          {/* 示例：预加载图片 */}
          <link
            rel="preload"
            href="https://static.cyjiaomu.com/ai/battery.svg" // 替换为实际的图片文件路径
            as="image"
          />
          {/* --- 结束资源预加载 --- */}
          <Script
            id="clarity-script"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                try {
                  (function (c, l, a, r, i, t, y) {
                    c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
                    t = l.createElement(r); t.async = 1; t.src = "https://www.clarity.ms/tag/" + i;
                    y = l.getElementsByTagName(r)[0]; y.parentNode.insertBefore(t, y);
                  })(window, document, "clarity", "script", "s5evdi62ia");
                } catch (error) {
                  console.log("Clarity script error:", error)
                }
              `
            }}
          />
        </head>
        <Script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js" />
        <Script src="https://jic.talkingdata.com/app/h5/v1?appid=83A41F6335B84CA4B9239A54638954EC&vn=v1.0&vc=h5" />
        <body className={cn('h-full bg-white dark:bg-black', montserrat.className)} suppressHydrationWarning>
          <ThemeProvider>
            <ExpandedProvider>
              <SelectedChatProvider>
                <Sidebar>{children}</Sidebar>
                <ResponsiveToaster />
              </SelectedChatProvider>
            </ExpandedProvider>
          </ThemeProvider>
        </body>
      </html>
    </Suspense>
  );
}
