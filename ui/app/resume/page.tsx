'use client';

import BackNavigationWrapper from "@/components/common/BackNavigationWrapper";
import Resume from "../../components/resume/index";
import { FileClock, FileText } from "lucide-react";
import { useRouter } from "next/navigation";
import { withQuery } from "@/lib/utils";


const Page = () => {

  const router = useRouter();
  const goHistoryResumeChats = () => {
    router.push(withQuery('/library', { source: 'resumeAnalyzer' }));
  }

  return (
    <div className="flex flex-col h-full">
      <div className="px-4 sticky top-0 z-10 bg-white dark:bg-dark-primary">
        <div className="container mx-auto py-1">
          <BackNavigationWrapper title="简历分析" className="border-none">
            <div className="flex-1 flex items-center justify-end" title="我的简历">
              <div className="flex items-center cursor-pointer text-base justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 flex items-center gap-2 text-blue-600" onClick={goHistoryResumeChats}>
                 <FileText className="w-5 h-5" />我的简历
              </div>
            </div>
          </BackNavigationWrapper>
        </div>
      </div>
      <div className="flex-1 overflow-auto">
        <Resume />
      </div>
    </div>
  );
};

export default Page;
