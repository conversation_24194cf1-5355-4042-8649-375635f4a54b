'use client';

import React, { useState, useEffect } from 'react';
import ResumeProcessing from '@/components/resume/ResumeProcessing';
import BackNavigationWrapper from '@/components/common/BackNavigationWrapper';
import Battery from '@/components/chat/Battery';
import { FileClock } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { withQuery } from '@/lib/utils';

const Page = ({ params }: any) => {
  const [chatId, setChatId] = useState<string | null>(null);
  
  const router = useRouter();

  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setChatId(resolvedParams.chatId);
      console.log("🚀 ~ Page ~ chatId:", resolvedParams.chatId);
    };

    unwrapParams();
  }, [params]);

  if (!chatId) {
    return null;
  }

  const goHistoryResumeChats = () => {
    router.push(withQuery('/library', { source: 'resumeAnalyzer' }));
  };

  return (
    <div className="flex flex-col h-full px-4">
      <div className="sticky top-0 z-10 bg-white dark:bg-dark-primary border-b border-gray-200 dark:border-gray-800">
        <div className="container mx-auto lg:px-4 py-1">
          <BackNavigationWrapper title="简历分析">
            <div className="flex-1 flex items-center justify-end" title="我的简历">
              <Battery className="mr-[6.5px]" />
              <div className="flex items-center cursor-pointer text-base" onClick={goHistoryResumeChats}>
                <FileClock size={17} />我的简历
              </div>
            </div>
          </BackNavigationWrapper>
        </div>
      </div>
      <div className="flex-1 overflow-auto">
        <ResumeProcessing chatId={chatId} />
      </div>
    </div>
  );
};

export default Page;
