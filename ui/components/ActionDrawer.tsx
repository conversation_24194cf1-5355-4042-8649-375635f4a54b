import { Link, LogOut, Image } from 'lucide-react';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { createPortal } from 'react-dom';
import { toast } from 'sonner';
import { useDeviceContext } from './context/DeviceContext';
import { saveAs } from 'file-saver';
import { clkLog } from '@/lib/utils';
import html2canvas from 'html2canvas';

export interface ActionDrawerRef {
    open: () => void;
}

const ActionDrawer = forwardRef<ActionDrawerRef, { chatId?: string, showExport?: boolean }>((props, ref) => {
    const { chatId } = props;
    const [visible, setVisible] = useState(false);
    const url = `${window.location.origin}/c/${chatId}?share=true`;
    const { channelId }: any = useDeviceContext();
    const channelExport = !['gy-android', 'gy-ios', 'mp', 'mp-kl'].includes(channelId)
    const showExport = 'showExport' in props ? props.showExport && channelExport : channelExport;

    const close = () => {
        setVisible(false);
    };

    useImperativeHandle(ref, () => ({
        open: () => {
            setVisible(true);
        },
    }));

    useEffect(() => {
        if (visible) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }

        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [visible]);

    const handleCopyLink = async () => {
        try {
            close();
            const title = document.title || '分享对话';
            clkLog('按钮操作', { opt: '制到剪贴板', title, chatId });
            await navigator.clipboard.writeText(url);
            toast.success('链接已复制到剪贴板，快去分享给小伙伴吧！');
        } catch (error) {
            toast.error('复制链接失败');
        }
    };

    const handleSaveImage = async () => {
        try {
            close();
            const title = document.title || '分享对话';
            clkLog('按钮操作', { opt: '保存图片', title, chatId });

            const container = document.getElementById('messages_container');
            if (!container) throw new Error('找不到消息容器');

            // 克隆容器避免修改原始DOM
            const clone = container.cloneNode(true) as HTMLElement;
            // 设置克隆体为不可见，避免水印显示在网页上
            clone.style.position = 'fixed';
            clone.style.left = '-9999px';
            clone.style.top = '0';
            document.body.appendChild(clone);

            // 移除不需要的元素
            Array.from(clone.querySelectorAll('.expand-button, .copy_rewrite, .related_container, .thinking_status, .suggestions_container, .message_input, .message_sources')).forEach(el => el?.remove?.());

            // 移除行数限制样式并设置容器宽度
            clone.style.width = '780px';
            clone.style.padding = '20px';
            clone.style.backgroundColor = 'white';

            clone.style.maxWidth = 'unset';
            Array.from(clone.querySelectorAll('.line-clamp-3')).forEach((el: any) => {
                el.classList.remove('line-clamp-3');
                const parent = el.parentElement;
                if (parent) {
                    parent.style.height = `${el.scrollHeight + 34}px`;
                }
            });

            // 移除所有隐藏元素
            Array.from(clone.querySelectorAll('*')).forEach(el => {
                const style = window.getComputedStyle(el);
                if (style.display === 'none' || style.visibility === 'hidden') {
                    el.remove();
                }
            });

            // 添加标题元素
            const imageElement = document.createElement('img');
            imageElement.src = 'https://static.cyjiaomu.com/ai/qr-jmso.png';
            imageElement.style.height = '240px';
            imageElement.style.width = '211.2px';
            imageElement.style.margin = 'auto';

            // 等待二维码图片加载完成
            await new Promise((resolve, reject) => {
                imageElement.onload = resolve;
                imageElement.onerror = reject;
            });

            clone.appendChild(imageElement);

            // 使用html2canvas生成图片
            const canvas = await html2canvas(clone, {
                scale: 2,
                useCORS: true,
                backgroundColor: '#ffffff',
                logging: false
            });

            // 转换为Blob
            canvas.toBlob((blob) => {
                if (blob) {
                    saveAs(blob, `chat-${title}-${chatId}.png`);
                    toast.success('图片保存成功');
                } else {
                    throw new Error('图片生成失败');
                }
            }, 'image/png');

        } catch (error) {
            console.error('保存图片失败:', error);
            toast.error('保存图片失败');
        } finally {
            const clone = document.body.lastChild;
            if (clone) document?.body?.removeChild?.(clone);
        }
    };

    const handleExport = async () => {
        const container = document.getElementById('messages_container');
        if (!container) throw new Error('找不到消息容器');

        // 克隆容器避免修改原始DOM
        const clone = container.cloneNode(true) as HTMLElement;
        clone.style.position = 'absolute';
        clone.style.left = '-9999px';
        document.body.appendChild(clone);

        // 移除不需要的元素
        Array.from(clone.querySelectorAll('.expand-button, .copy_rewrite, .related_container, .thinking_status, .suggestions_container, .message_input, .message_sources')).forEach(el => el?.remove?.());

        // 移除行数限制样式并设置容器宽度
        clone.style.width = '780px';
        clone.style.padding = '20px';

        // 添加标题元素
        const imageElement = document.createElement('img');
        imageElement.src = 'https://static.cyjiaomu.com/ai/qr-jmso.png';
        imageElement.style.height = '240px';
        imageElement.style.width = '211.2px';
        imageElement.style.margin = 'auto';
        clone.appendChild(imageElement);

        clone.style.maxWidth = 'unset';
        Array.from(clone.querySelectorAll('.line-clamp-3')).forEach((el: any) => {
            el.classList.remove('line-clamp-3');
            const parent = el.parentElement;
            if (parent) {
                parent.style.height = `${el.scrollHeight + 34}px`;
            }
        });

        // 移除所有隐藏元素
        Array.from(clone.querySelectorAll('*')).forEach(el => {
            const style = window.getComputedStyle(el);
            if (style.display === 'none' || style.visibility === 'hidden') {
                el.remove();
            }
        });
        try {
            close();
            const title = document.title || '分享对话';
            clkLog('按钮操作', { opt: '导出对话', title, chatId });

            // 调用API路由
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/export-docx`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    htmlContent: clone.outerHTML,
                    title,
                    chatId
                })
            });

            if (!response.ok) {
                throw new Error('导出失败');
            }

            const blob = await response.blob();
            saveAs(blob, `chat-${title}-${chatId}.docx`);
            toast.success('导出成功');
        } catch (error) {
            console.error('导出失败:', error);
            toast.error('导出失败');
        } finally {
            document.body.removeChild(clone);
        }
    };

    if (!visible) return null;

    return createPortal(
        <div className="relative">
            <div className="fixed inset-0 bg-black/30 z-[100]" onClick={close} />
            <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[100]  max-w-[90vw] w-[90vw] md:max-w-md md:w-full mx-auto">
                <div className="bg-white dark:bg-dark-secondary shadow-xl dark:border dark:border-white/10 dark:shadow-black/30 rounded-lg flex flex-col space-y-0 p-1">
                    <button
                        onClick={handleCopyLink}
                        className="w-full flex items-center justify-between space-x-2 text-sm text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-200 rounded-none py-4 px-6"
                    >
                        <span className="whitespace-nowrap">复制链接</span>
                        <Link size={18} className="text-[#4D6BFE]" />
                    </button>
                    {showExport && (
                        <>
                            <div className="border-b border-light-100 dark:border-dark-200 mx-4" />
                            <button
                                onClick={handleSaveImage}
                                className="w-full flex items-center justify-between space-x-2 text-sm text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-200 rounded-none py-4 px-6"
                            >
                                <span className="whitespace-nowrap">保存图片</span>
                                <Image size={18} className="text-[#4D6BFE]" />
                            </button>
                            <div className="border-b border-light-100 dark:border-dark-200 mx-4" />
                            <button
                                onClick={handleExport}
                                className="w-full flex items-center justify-between space-x-2 text-sm text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-200 rounded-none py-4 px-6"
                            >
                                <span className="whitespace-nowrap">导出记录</span>
                                <LogOut size={18} className="text-[#4D6BFE]" />
                            </button>
                        </>
                    )}
                </div>
                <div className="flex justify-center p-3 md:p-4">
                    <button
                        onClick={close}
                        className="bg-white dark:bg-dark-secondary shadow-xl rounded-full p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                        <svg
                            className="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                            />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        , document.body);
});

ActionDrawer.displayName = 'ActionDrawer';

export default ActionDrawer;