'use client';

import { Fragment, useEffect, useRef, useState } from 'react';
import MessageInput from './MessageInput';
import { File, Message } from './chat/ChatTools';
import MessageBox from './MessageBox';
import MessageBoxLoading from './MessageBoxLoading';
import { cn, getUserText } from '@/lib/utils';
import { useChatContext } from './context/ChatContext';
import useScrollDetection from '@/hooks/useScrollDetection';
import { useDeviceContext } from './context/DeviceContext';

const Chat = ({
  loading,
  messages,
  sendMessage,
  messageAppeared,
  rewrite,
}: {
  messages: Message[];
  sendMessage: (message: string) => void;
  loading: boolean;
  messageAppeared: boolean;
  rewrite: (messageId: string) => void;
  fileIds: string[];
  setFileIds: (fileIds: string[]) => void;
  files: File[];
  setFiles: (files: File[]) => void;
}) => {
  const [dividerWidth, setDividerWidth] = useState(0);
  const dividerRef = useRef<HTMLDivElement | null>(null);
  const messageEnd = useRef<HTMLDivElement | null>(null);
  const { focusMode } = useChatContext() as any;
  const { channelId } = useDeviceContext() as any;

  // 自动滚动控制状态
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const lastMessageCountRef = useRef(0);
  const lastMessageContentRef = useRef('');

  // 聊天容器引用，用于滚动检测
  const chatContainerRef = useRef<HTMLDivElement | null>(null);

  // 直接实现滚动检测，绕过可能有问题的 useScrollDetection hook
  const [userScrolled, setUserScrolled] = useState(false);
  const lastScrollYRef = useRef(0);

  const resetScrollState = useCallback(() => {
    console.log('🔄 重置滚动状态');
    setUserScrolled(false);
  }, []);

  // 直接监听 window 滚动
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleWindowScroll = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        const currentScrollY = window.scrollY;
        const scrollDirection = currentScrollY < lastScrollYRef.current ? 'up' : 'down';

        console.log('🔄 Window 滚动检测:', {
          direction: scrollDirection,
          current: currentScrollY,
          last: lastScrollYRef.current
        });

        // 任何滚动都认为是用户手动滚动
        if (!userScrolled) {
          console.log('🔄 设置 userScrolled = true');
          setUserScrolled(true);
        }

        // 如果向下滚动且接近底部，重置状态
        if (scrollDirection === 'down') {
          const isNearBottom = document.documentElement.scrollHeight - currentScrollY - window.innerHeight < 100;
          if (isNearBottom && userScrolled) {
            console.log('🔄 接近底部，重置 userScrolled = false');
            setUserScrolled(false);
          }
        }

        lastScrollYRef.current = currentScrollY;
      }, 50);
    };

    console.log('✅ 添加 window 滚动监听器');
    window.addEventListener('scroll', handleWindowScroll, { passive: true });

    return () => {
      console.log('🗑️ 移除 window 滚动监听器');
      clearTimeout(timeoutId);
      window.removeEventListener('scroll', handleWindowScroll);
    };
  }, [userScrolled]);

  // 监控 userScrolled 状态变化
  useEffect(() => {
    console.log('👀 userScrolled 状态变化:', userScrolled);
  }, [userScrolled]);

  // 直接测试多个可能的滚动容器
  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    const mainLayout = document.getElementById('mainLayout');

    const testHandlers: Array<{ element: Element | Window, handler: () => void, name: string }> = [];

    if (chatContainer) {
      const handler = () => console.log('🎯 直接检测到聊天容器滚动:', chatContainer.scrollTop);
      chatContainer.addEventListener('scroll', handler, { passive: true });
      testHandlers.push({ element: chatContainer, handler, name: '聊天容器' });
      console.log('✅ 已为聊天容器添加测试滚动监听器');
    }

    if (mainLayout) {
      const handler = () => console.log('🎯 直接检测到 mainLayout 滚动:', mainLayout.scrollTop);
      mainLayout.addEventListener('scroll', handler, { passive: true });
      testHandlers.push({ element: mainLayout, handler, name: 'mainLayout' });
      console.log('✅ 已为 mainLayout 添加测试滚动监听器');
    }

    // 也测试 window 滚动
    const windowHandler = () => console.log('🎯 直接检测到 window 滚动:', window.scrollY);
    window.addEventListener('scroll', windowHandler, { passive: true });
    testHandlers.push({ element: window, handler: windowHandler, name: 'window' });
    console.log('✅ 已为 window 添加测试滚动监听器');

    return () => {
      testHandlers.forEach(({ element, handler }) => {
        element.removeEventListener('scroll', handler as EventListener);
      });
    };
  }, []);

  const showRewrite = (i: number) => !(focusMode == 'resumeAnalyzer' && i < 2);

  useEffect(() => {
    const updateDividerWidth = () => {
      if (dividerRef.current) {
        setDividerWidth(dividerRef.current.scrollWidth);
      }
    };

    updateDividerWidth();

    window.addEventListener('resize', updateDividerWidth);

    return () => {
      window.removeEventListener('resize', updateDividerWidth);
    };
  });

  // 检测新消息或消息内容变化，重置自动滚动状态
  useEffect(() => {
    const currentMessageCount = messages.length;
    const lastMessage = messages[messages.length - 1];
    const currentLastMessageContent = lastMessage ? JSON.stringify(lastMessage.content) : '';

    // 检测是否有新消息或最后一条消息内容发生变化
    const hasNewMessage = currentMessageCount > lastMessageCountRef.current;
    const hasContentChange = currentLastMessageContent !== lastMessageContentRef.current;

    if (hasNewMessage || hasContentChange) {
      // 有新消息或内容变化时，重置滚动状态并启用自动滚动
      setAutoScrollEnabled(true);
      resetScrollState();

      // 更新引用值
      lastMessageCountRef.current = currentMessageCount;
      lastMessageContentRef.current = currentLastMessageContent;
    }
  }, [messages, resetScrollState]);

  // 处理自动滚动逻辑
  useEffect(() => {
    // 如果用户手动滚动了，禁用自动滚动
    if (userScrolled && autoScrollEnabled) {
      console.log('🔄 用户手动滚动，禁用自动滚动');
      setAutoScrollEnabled(false);
    }
  }, [userScrolled, autoScrollEnabled]);

  // 执行自动滚动
  useEffect(() => {
    if (autoScrollEnabled && !userScrolled) {
      console.log('📜 执行自动滚动');
      // 在执行自动滚动前，临时重置滚动检测状态
      resetScrollState();

      // 延迟一小段时间再执行滚动，避免立即被检测为用户滚动
      setTimeout(() => {
        messageEnd.current?.scrollIntoView({ behavior: 'smooth' });
      }, 50);
    }
  }, [messages, autoScrollEnabled, userScrolled, resetScrollState]);

  // 设置页面标题
  useEffect(() => {
    if (messages.length === 1) {
      document.title = `${getUserText(messages[0].content)} - 灵通`;
    }
  }, [messages]);

  return (
    <div ref={chatContainerRef} className={cn('chat-container flex flex-col flex-1 overflow-y-auto items-center')}>
      <div id="messages_container" className={cn('flex flex-col space-y-1 pb-40 pt-4 w-full max-w-[764px] lg:pt-[70px]')}>
        <div className={cn('flex flex-col space-y-1 w-full')}>
          {messages.map((msg, i) => {
            const isLast = i === messages.length - 1;

            // 思考内容变化时滚动到底部（仅在自动滚动启用时）
            const handleReasoningContentChange = () => {
              if (autoScrollEnabled && !userScrolled) {
                // 重置滚动检测状态，然后延迟执行滚动
                resetScrollState();
                setTimeout(() => {
                  messageEnd.current?.scrollIntoView({ behavior: 'smooth' });
                }, 50);
              }
            };

            return (
              <Fragment key={msg.messageId}>
                <MessageBox
                  key={i}
                  message={msg}
                  messageIndex={i}
                  history={messages}
                  loading={loading}
                  dividerRef={isLast ? dividerRef : undefined}
                  isLast={isLast}
                  rewrite={rewrite}
                  sendMessage={sendMessage}
                  showRewrite={showRewrite(i)}
                  onReasoningContentChange={handleReasoningContentChange}
                />
                {!isLast && msg.role === 'assistant' && (
                  <div className="h-px w-full bg-light-secondary dark:bg-dark-secondary !mb-4" />
                )}
              </Fragment>
            );
          })}
        </div>
        {loading && !messageAppeared && <MessageBoxLoading />}
        <div ref={messageEnd} className="h-0" />
        {dividerWidth > 0 && (
          <div
            className="message_input bottom-1 lg:bottom-10 fixed z-[1]"
            style={{ width: dividerWidth }}
          >
            <MessageInput
              loading={loading}
              sendMessage={sendMessage}
              focusMode={focusMode}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Chat;
