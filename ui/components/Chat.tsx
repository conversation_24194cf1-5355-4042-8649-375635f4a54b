'use client';

import { Fragment, useEffect, useRef, useState } from 'react';
import MessageInput from './MessageInput';
import { File, Message } from './chat/ChatTools';
import MessageBox from './MessageBox';
import MessageBoxLoading from './MessageBoxLoading';
import { cn, getUserText } from '@/lib/utils';
import { useChatContext } from './context/ChatContext';
import useScrollDetection from '@/hooks/useScrollDetection';
import { useDeviceContext } from './context/DeviceContext';

const Chat = ({
  loading,
  messages,
  sendMessage,
  messageAppeared,
  rewrite,
}: {
  messages: Message[];
  sendMessage: (message: string) => void;
  loading: boolean;
  messageAppeared: boolean;
  rewrite: (messageId: string) => void;
  fileIds: string[];
  setFileIds: (fileIds: string[]) => void;
  files: File[];
  setFiles: (files: File[]) => void;
}) => {
  const [dividerWidth, setDividerWidth] = useState(0);
  const dividerRef = useRef<HTMLDivElement | null>(null);
  const messageEnd = useRef<HTMLDivElement | null>(null);
  const { focusMode } = useChatContext() as any;
  const { channelId } = useDeviceContext() as any;

  // 是否已完成首次自动滚动
  const hasAutoScrolled = useRef(false);
  // 记录上一次消息数量
  const prevMessagesLength = useRef(0);
  // 记录上一次最后一条消息的 id 和内容
  const prevLastMsgRef = useRef<{ id?: string; content?: string }>({});

  // 使用自定义 Hook 替代原来的滚动监听逻辑
  const { userScrolled } = useScrollDetection(['mp', 'mp-kl', 'gy-ios', 'gy-android'].includes(channelId) ? 'window' : '#mainLayout');

  const showRewrite = (i: number) => !(focusMode == 'resumeAnalyzer' && i < 2);

  useEffect(() => {
    const updateDividerWidth = () => {
      if (dividerRef.current) {
        setDividerWidth(dividerRef.current.scrollWidth);
      }
    };

    updateDividerWidth();

    window.addEventListener('resize', updateDividerWidth);

    return () => {
      window.removeEventListener('resize', updateDividerWidth);
    };
  });

  useEffect(() => {
    let shouldScroll = false;

    // 首次加载历史消息后自动滚动
    if (!hasAutoScrolled.current && messages.length > 0 && !userScrolled) {
      shouldScroll = true;
      hasAutoScrolled.current = true;
    } else if (hasAutoScrolled.current && messages.length > 0 && !userScrolled) {
      // 新消息或重写消息输出时，始终滚动（但获取建议后不滚动）
      const lastMsg = messages[messages.length - 1];
      const prevLastMsg = prevLastMsgRef.current;
      const lastMsgId = lastMsg?.messageId;
      const lastMsgContent = lastMsg?.content;

      // 判断是否为建议类消息
      const isSuggestionMsg =
        lastMsg?.role === 'assistant' &&
        Array.isArray((lastMsg as any).suggestions) &&
        (lastMsg as any).suggestions.length > 0 &&
        messageAppeared;

      // 只要最后一条消息的 id 或内容发生变化，且不是建议类消息，就滚动
      if (
        !isSuggestionMsg &&
        (lastMsgId !== prevLastMsg.id || lastMsgContent !== prevLastMsg.content)
      ) {
        shouldScroll = true;
      }
    }

    if (messages.length > 0) {
      const lastMsg = messages[messages.length - 1];
      prevLastMsgRef.current = {
        id: lastMsg?.messageId,
        content: lastMsg?.content,
      };
    }

    if (shouldScroll) {
      messageEnd.current?.scrollIntoView({ behavior: 'smooth' });
    }

    prevMessagesLength.current = messages.length;

    if (messages.length === 1) {
      document.title = `${getUserText(messages[0].content)} - 灵通`;
    }
  }, [messages, userScrolled, messageAppeared]);

  return (
    <div className={cn('flex flex-col flex-1 overflow-y-auto items-center')}>
      <div id="messages_container" className={cn('flex flex-col space-y-1 pb-40 pt-4 w-full max-w-[764px] lg:pt-[70px]')}>
        <div className={cn('flex flex-col space-y-1 w-full')}>
          {messages.map((msg, i) => {
            const isLast = i === messages.length - 1;

            // 思考内容变化时滚动到底部
            const handleReasoningContentChange = () => {
              messageEnd.current?.scrollIntoView({ behavior: 'smooth' });
            };

            return (
              <Fragment key={msg.messageId}>
                <MessageBox
                  key={i}
                  message={msg}
                  messageIndex={i}
                  history={messages}
                  loading={loading}
                  dividerRef={isLast ? dividerRef : undefined}
                  isLast={isLast}
                  rewrite={rewrite}
                  sendMessage={sendMessage}
                  showRewrite={showRewrite(i)}
                  onReasoningContentChange={handleReasoningContentChange}
                />
                {!isLast && msg.role === 'assistant' && (
                  <div className="h-px w-full bg-light-secondary dark:bg-dark-secondary !mb-4" />
                )}
              </Fragment>
            );
          })}
        </div>
        {loading && !messageAppeared && <MessageBoxLoading />}
        <div ref={messageEnd} className="h-0" />
        {dividerWidth > 0 && (
          <div
            className="message_input bottom-1 lg:bottom-10 fixed z-[1]"
            style={{ width: dividerWidth }}
          >
            <MessageInput
              loading={loading}
              sendMessage={sendMessage}
              focusMode={focusMode}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Chat;
