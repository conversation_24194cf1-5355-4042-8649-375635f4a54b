'use client';

import { <PERSON>over, PopoverButton, PopoverPanel, Transition } from '@headlessui/react';
import { Fragment, useRef } from 'react';
import { Share2 } from 'lucide-react';
import DeleteChat from './DeleteChat';
import ActionDrawer, { ActionDrawerRef } from './ActionDrawer';

interface ChatActionsProps {
    selectedChatId: any;
    chatId: string;
    showExport?: boolean;
}

const ChatActions = ({ chatId, showExport, selectedChatId }: ChatActionsProps) => {

    const actionDrawerRef = useRef<ActionDrawerRef>(null);

    const handleShare = (close: Function) => {
        close();
        actionDrawerRef.current?.open()
    };

    return (
        <>
            <Popover className="relative">
                {({ open, close }) => (
                    <>
                        <PopoverButton
                            className={`${open ? 'bg-black/5 dark:bg-white/5' : ''} p-1 rounded-lg ${selectedChatId === chatId ? 'opacity-100' : 'opacity-0 group-hover:opacity-100 focus:opacity-100'} transition-opacity`}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-black/70 dark:text-white/70 group-hover:text-black dark:group-hover:text-white transition-colors"
                            >
                                <circle cx="12" cy="12" r="1" />
                                <circle cx="19" cy="12" r="1" />
                                <circle cx="5" cy="12" r="1" />
                            </svg>
                        </PopoverButton>

                        <Transition
                            as={Fragment}
                            enter="transition ease-out duration-200"
                            enterFrom="opacity-0 translate-y-1"
                            enterTo="opacity-100 translate-y-0"
                            leave="transition ease-in duration-150"
                            leaveFrom="opacity-100 translate-y-0"
                            leaveTo="opacity-0 translate-y-1"
                        >
                            <PopoverPanel className="absolute z-10 right-0 mt-2 w-36 origin-top-right rounded-lg bg-white dark:bg-dark-secondary shadow-lg ring-1 ring-black/5 dark:ring-white/5 focus:outline-none">
                                <div className="py-1">
                                    <button
                                        onClick={() => handleShare(close)}
                                        className="group flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                                    >
                                        <Share2 className="mr-3 h-4 w-4" />
                                        分享
                                    </button>
                                    <div className="group flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700" >
                                        <DeleteChat className="w-full" chatId={chatId} chats={[]} setChats={() => close()} buttonText="删除" />
                                    </div>
                                </div>
                            </PopoverPanel>
                        </Transition>
                    </>
                )}
            </Popover>
            <ActionDrawer ref={actionDrawerRef} chatId={chatId} showExport={showExport} />
        </>
    );
};

export default ChatActions;