import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { File as FileType } from './chat/ChatTools';
import { useChatContext } from './context/ChatContext';

interface Question {
  title?: string;
  submitText?: string;
  fileType?: string;
  input?: string;
  placeholder?: string;
}

export interface ChatFilePopupRef {
  open: (item: any) => void;
}

const ChatFilePopup = forwardRef<ChatFilePopupRef, {
  fileIds: string[],
  setFileIds: (fileIds: string[]) => void,
  files: FileType[],
  setFiles: (files: FileType[]) => void
}>(
  ({ fileIds, setFileIds, files, setFiles }, ref) => {
    const { sendMessage } = useChatContext();

    const [visible, setVisible] = useState(false);
    const [question, setQuestion] = useState<Question>({});
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [loading, setLoading] = useState(false);

    const close = () => {
      setVisible(false);
      setSelectedFile(null);
    };

    useImperativeHandle(ref, () => ({
      open: (item: any) => {
        setQuestion(item);
        setVisible(true);
      },
    }));

    const handleSubmit = async () => {
      if (selectedFile) {
        try {
          setLoading(true);
          const fileExtension: any = selectedFile.name.split('.').pop()?.toLowerCase();

          if (['pdf', 'txt', 'docx'].includes(fileExtension)) {
            const formData = new FormData();
            formData.append('files', selectedFile);
            formData.append('embedding_model', process.env.CUSTOM_EMBEDDING_MODEL || 'text-embedding-3-large');
            formData.append('embedding_model_provider', 'jiaomu-ai-server');
            formData.append('embedding', 'false');

            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/uploads`, {
              method: 'POST',
              body: formData,
            });

            if (!response.ok) {
              throw new Error('文件上传失败');
            }

            const resData = await response.json();
            const filesIds = [...fileIds, ...resData.files.map((file: any) => file.fileId)];
            setFiles([...files, ...resData.files]);
            setFileIds(filesIds);
            sendMessage(selectedFile.name + question.input, resData.messageId, filesIds);
          } else {
            alert('不支持的文件格式');
            return;
          }
        } catch (error) {
          console.error('处理文件失败:', error);
          alert('处理文件失败，请重试');
          return;
        } finally {
          setLoading(false);
        }
      } else {
        alert('请选择要上传的文件');
        return;
      }
      close();
    };

    useEffect(() => {
      if (visible) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
      return () => {
        document.body.style.overflow = '';
      };
    }, [visible]);

    if (!visible) return null;

    return (
      <div className="relative">
        <div className="fixed inset-0 bg-black/30 z-40" onClick={close} />
        <div className="fixed left-0 right-0 bottom-0 max-h-[80vh] bg-white dark:bg-dark-secondary z-50 shadow-xl overflow-hidden rounded-xl flex flex-col">
          <div className="flex justify-between p-4 border-b dark:border-dark-200 bg-white dark:bg-dark-secondary sticky top-0 z-10">
            <span>{question.title}</span>
            <button
              onClick={close}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 pb-[calc(72px+env(safe-area-inset-bottom))]">
              <div className="mb-4">
                <div className="text-gray-500 dark:text-gray-400 text-sm mb-3"> {question.placeholder} </div>
                <label htmlFor="file-upload" className={`mt-2 flex justify-center rounded-lg border-2 border-dashed ${loading ? 'border-gray-200 dark:border-gray-700 cursor-not-allowed' : 'border-gray-300 dark:border-gray-600 cursor-pointer hover:border-gray-400 dark:hover:border-gray-500'} transition-colors bg-gray-50 dark:bg-dark-100 hover:bg-gray-100 dark:hover:bg-dark-200 px-8 py-12`} onClick={(e) => loading && e.preventDefault()}>
                  <div className="text-center">
                    <div className="mt-4 flex text-sm leading-6 text-gray-600 dark:text-gray-400">
                      <span className={`relative rounded-md bg-white dark:bg-dark-100 font-semibold ${loading ? 'text-gray-400 dark:text-gray-500' : 'text-blue-600 hover:text-blue-500'} px-3 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:ring-gray-400 dark:hover:ring-gray-500 transition-all m-auto`}>
                        上传文件
                      </span>
                      <input
                        id="file-upload"
                        name="file-upload"
                        type="file"
                        className="sr-only"
                        accept={question.fileType || '.pdf,.docx,.txt'}
                        disabled={loading}
                        onChange={(e) => {
                          if (loading) return;
                          const file = e.target.files?.[0];
                          if (file) {
                            if (file.size > 2 * 1024 * 1024) {
                              alert('文件大小不能超过 2MB');
                              return;
                            }
                            if (question.fileType) {
                              const allowedTypes = question.fileType.split(',');
                              const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
                              if (!allowedTypes.includes(fileExtension)) {
                                alert('请上传正确的文件格式');
                                return;
                              }
                            }
                            setSelectedFile(file);
                          }
                        }}
                      />
                    </div>
                    {selectedFile && (
                      <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        已选择: {selectedFile.name}
                      </p>
                    )}
                  </div>
                </label>
              </div>
            </div>
            <div className="fixed bottom-0 left-0 right-0 p-4 pb-[calc(16px+env(safe-area-inset-bottom))] bg-white dark:bg-dark-secondary border-t dark:border-dark-200">
              <button
                className="w-full bg-gradient-to-r from-[#4D6BFE]/90 to-[#4D6BFE]/50 text-white hover:bg-opacity-85 transition duration-100 rounded-md py-2 relative"
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="opacity-0">{question.submitText || '提交'}</span>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                  </>
                ) : (
                  question.submitText || '提交'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

ChatFilePopup.displayName = 'ChatFilePopup';

export default ChatFilePopup;
