import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

interface Field {
  label: string;
  type?: string;
  placeholder?: string;
  options?: string[];
  required?: boolean;
}

interface Question {
  fields?: Field[];
  type?: string;
  title?: string;
  label?: string;
  icon: string;
  input?: string;
  prompt?: string;
  submitText?: string;
}

export interface ChatPopupRef {
  open: (item: any) => void;
}

const ChatPopup = forwardRef<ChatPopupRef, { sendMessage?: (message: string) => void }>(
  ({ sendMessage = () => { } }, ref) => {
    const [visible, setVisible] = useState(false);
    const [question, setQuestion] = useState<Question>({ icon: '', title: '' });
    const [formData, setFormData] = useState<Record<string, string>>({});

    const close = () => {
      setVisible(false);
      setFormData({});
    };

    useImperativeHandle(ref, () => ({
      open: (item: any) => {
        setQuestion(item);
        setVisible(true);
        // 初始化表单数据，为 radio 类型设置默认值
        const initialFormData: Record<string, string> = {};
        item.fields?.forEach((field: Field) => {
          if (field.type === 'radio' && field.options && field.options.length > 0) {
            initialFormData[field.label] = field.options[0];
          }
        });
        setFormData(initialFormData);
      },
    }));

    const handleSubmit = () => {
      if (question.fields) {
        // 校验必填字段
        const requiredFields = question.fields.filter(field => field.required);
        const emptyRequiredFields = requiredFields.filter(
          field => !formData[field.label] || formData[field.label].trim() === ''
        );

        if (emptyRequiredFields.length > 0) {
          alert(`请填写必填字段: ${emptyRequiredFields.map(field => field.label).join(', ')}`);
          return;
        }

        const message = question.fields
          .filter(field => formData[field.label] && formData[field.label].trim() !== '')
          .map(field => `${field.label}: ${formData[field.label]}`)
          .join('\n');
        if (message) {
          sendMessage(`${message}${question.prompt || ''}`);
        }
      }
      close();
    };

    useEffect(() => {
      if (visible) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
      return () => {
        document.body.style.overflow = '';
      };
    }, [visible]);

    if (!visible) return null;

    return (
      <div className="relative">
        <div className="fixed inset-0 bg-black/30 z-50" onClick={close} />
        <div className="fixed left-0 right-0 bottom-0 max-h-[80vh] bg-white dark:bg-dark-secondary z-50 shadow-xl overflow-hidden rounded-xl flex flex-col">
          <div className="flex justify-between p-4 border-b dark:border-dark-200 bg-white dark:bg-dark-secondary sticky top-0 z-10">
            <div className="flex items-center gap-0.5">
              <span dangerouslySetInnerHTML={{ __html: question.icon }} />
              <span>{question.title}</span>
            </div>
            <button
              onClick={close}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 pb-[calc(72px+env(safe-area-inset-bottom))]">
              {question.fields?.map((field: Field, index: number) => (
                <div key={index} className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {field.label}{field.required && <span className="text-red-500 ml-0.5">*</span>}
                  </label>
                  {field.type === 'textarea' ? (
                    <textarea
                      placeholder={field.placeholder || `请输入${field.label}`}
                      value={formData[field.label] || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        [field.label]: e.target.value
                      }))}
                      className="w-full px-3 py-2 border rounded-md dark:bg-dark-100 dark:border-dark-200 dark:text-white min-h-[100px] resize-none"
                    />
                  ) : field.type === 'select' ? (
                    <select
                      value={formData[field.label] || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        [field.label]: e.target.value
                      }))}
                      className="w-full px-3 py-2 border rounded-md dark:bg-dark-100 dark:border-dark-200 dark:text-white"
                    >
                      <option value="">请选择</option>
                      {field.options?.map((option: string) => (
                        <option key={option} value={option}>
                          {typeof option === 'string' ? option : String(option)}
                        </option>
                      ))}
                    </select>
                  ) : field.type === 'radio' ? (
                    <div className="flex flex-row flex-wrap gap-3">
                      {field.options?.map((option: string) => (
                        <label key={option} className="relative">
                          <input
                            type="radio"
                            value={option}
                            checked={formData[field.label] === option}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              [field.label]: e.target.value
                            }))}
                            className="absolute opacity-0 w-0 h-0"
                          />
                          <div className={`px-4 py-2.5 rounded-lg cursor-pointer transition-all duration-200 text-sm font-medium select-none ${formData[field.label] === option ? 'bg-gradient-to-r from-[#4D6BFE]/90 to-[#4D6BFE]/50 text-white' : 'bg-gray-50 dark:bg-dark-100 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-200'}`}>
                            {typeof option === 'string' ? option : String(option)}
                          </div>
                        </label>
                      ))}
                    </div>
                  ) : (
                    <input
                      type={field.type || 'text'}
                      placeholder={field.placeholder || `请输入${field.label}`}
                      value={formData[field.label] || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        [field.label]: e.target.value
                      }))}
                      className="w-full px-3 py-2 border rounded-md dark:bg-dark-100 dark:border-dark-200 dark:text-white"
                    />
                  )}
                </div>
              ))}
            </div>
            <div className="fixed bottom-0 left-0 right-0 p-4 pb-[calc(16px+env(safe-area-inset-bottom))] bg-white dark:bg-dark-secondary border-t dark:border-dark-200">
              <button
                className="w-full bg-gradient-to-r from-[#4D6BFE]/90 to-[#4D6BFE]/50 text-white hover:bg-opacity-85 transition duration-100 rounded-md py-2"
                onClick={handleSubmit}
              >
                {question.submitText || '提交'}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

ChatPopup.displayName = 'ChatPopup';

export default ChatPopup;
