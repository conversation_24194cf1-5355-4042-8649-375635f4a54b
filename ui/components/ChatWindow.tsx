'use client';

import { useEffect, useRef, useState } from 'react';
import { Document } from '@langchain/core/documents';
import Chat from './Chat';
import EmptyChat from './EmptyChat';
import { toast } from 'sonner';
import { useSearchParams } from 'next/navigation';
import { getSuggestions } from '@/lib/actions';
import { Settings } from 'lucide-react';
import SettingsDialog from './SettingsDialog';
import NextError from 'next/error';
import { ChatContext } from './context/ChatContext';
import { clkLog, getLocalItem } from '@/lib/utils';
import { FZ_JsBridge } from '@/lib/js-bridge';
import { useDeviceContext } from './context/DeviceContext';
import { useSelectedChat } from './context/SelectedChatContext';
import { MessageUtils } from '@/lib/messageUtils';
import { Message, File, recentMessages, loadMessages, Task } from "./chat/ChatTools";
import { useSocket } from '@/hooks/useSocket';

const ChatWindow = ({ id, asyncMessages }: any) => {
  const searchParams = useSearchParams();
  const initialMessage = searchParams?.get('q');
  let userId = getLocalItem('__user_detail__', true)?.data?.detail?.user_id;
  const [chatId, setChatId] = useState<string>(id || '');
  const [newChatCreated, setNewChatCreated] = useState(false);

  const [hasError, setHasError] = useState(false);
  const [isReady, setIsReady] = useState(false);

  const [isWSReady, setIsWSReady] = useState(false);
  const ws = useSocket(
    process.env.NEXT_PUBLIC_WS_URL!,
    setIsWSReady,
    setHasError,
  );

  const isApp = useRef(FZ_JsBridge.isApp);

  const { loadingMessage: loading, setLoadingMessage: setLoading } = useSelectedChat();

  const [messageAppeared, setMessageAppeared] = useState(false);

  const [chatHistory, setChatHistory] = useState<[string, string][]>([]);
  const [messages, setMessages] = useState<Message[]>([]);

  const [files, setFiles] = useState<File[]>([]);
  const [fileIds, setFileIds] = useState<string[]>([]);

  const [focusMode, setFocusMode] = useState('managementMaster');

  const handleSetFocusMode = (mode: string) => {
    setFocusMode(mode);
    localStorage.setItem('focusMode', mode);
  };
  const [optimizationMode, setOptimizationMode] = useState('speed');

  const [isMessagesLoaded, setIsMessagesLoaded] = useState(false);

  const [notFound, setNotFound] = useState(false);

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  const { query, channelId }: any = useDeviceContext();
  const { chatBattery }: any = useSelectedChat();
  const [task, setTask] = useState<Task | null>(null);

  const reset = () => {
    setLoading(false);
    setChatId("");
    setNewChatCreated(false);
    setHasError(false);
    setIsReady(false);
    setIsWSReady(false);
    setMessageAppeared(false);
    setChatHistory([]);
    setMessages([]);
    // 确保 messagesRef 也被重置
    if (messagesRef.current.length > 0) {
      messagesRef.current = [];
    }
    setFiles([]);
    setFileIds([]);
    // 使用默认值而不是空字符串
    const defaultFocusMode = channelId === 'jm' ? 'managementMasterJM' : 'managementMaster';
    setFocusMode(defaultFocusMode);
    setOptimizationMode('speed');
    setIsMessagesLoaded(false);
    setNotFound(false);
    setIsSettingsOpen(false);
    setTask(null);
    if (ws?.readyState === 1) {
      // 移除所有事件监听器
      ws.onmessage = null;
      ws.onerror = null;
      ws.onclose = null;
      ws.close();
      console.debug(new Date(), 'ws:cleanup');
    }
  }

  useEffect(() => {
    // 检查URL参数中是否包含简历分析相关参数
    const source = searchParams?.get('source');
    const taskId = searchParams?.get('taskId');

    const defaultFocusMode = channelId === 'jm' ? 'managementMasterJM' : 'managementMaster';
    const localFocusMode = localStorage.getItem('focusMode') || defaultFocusMode;
    let newFocusMode = [defaultFocusMode, 'webSearch'].includes(localFocusMode) ? localFocusMode : defaultFocusMode;
    // 如果是从简历分析页面跳转过来的，设置focusMode为resumeAnalyzer
    if (source === 'resumeAnalyzer' && taskId) {
      newFocusMode = 'resumeAnalyzer';
    }
    handleSetFocusMode(newFocusMode);
    if (
      chatId &&
      !newChatCreated &&
      !isMessagesLoaded &&
      messages.length === 0
    ) {
      loadMessages(
        chatId,
        setMessages,
        setIsMessagesLoaded,
        setChatHistory,
        setFocusMode,
        setNotFound,
        setFiles,
        setFileIds,
        query?.get('share'),
        setTask
      );
    } else if (!chatId) {
      setNewChatCreated(true);
      setIsMessagesLoaded(true);
      setChatId(MessageUtils.generateMessageId(20));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    return () => {
      reset();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const messagesRef = useRef<Message[]>([]);

  useEffect(() => {
    console.log("🚀 ~ useEffect ~ messages:", messages)
    messagesRef.current = messages;
    asyncMessages?.(messages);
  }, [messages]);

  useEffect(() => {
    if (id) {
      setChatId(id || '');
      chatBattery.getBattery(); // 更新灵豆信息
    }
  }, [id]);

  useEffect(() => {
    if (isMessagesLoaded && isWSReady) {
      setIsReady(true);
      console.debug(new Date(), 'app:ready');
    } else {
      setIsReady(false);
    }
  }, [isMessagesLoaded, isWSReady]);

  useEffect(() => {
    if (task && isReady && ws?.readyState === 1) {
      // 发送一条空消息到服务端
      const wsMessage = {
        type: 'resumeAnalyzer',
        message: {
          chatId: chatId!,
        },
        focusMode: 'resumeAnalyzer',
        optimizationMode: optimizationMode,
        source: channelId || 'web',
        taskId: task.id
      };

      ws.send(JSON.stringify(wsMessage));

      // 更新 URL 参数
      const newSearchParams = new URLSearchParams(window.location.search);
      newSearchParams.set('source', 'resumeAnalyzer');
      newSearchParams.set('taskId', task.id.toString());
      window.history.replaceState({}, '', `${window.location.pathname}?${newSearchParams.toString()}`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [task, isReady, ws?.readyState]);


  const sendMessage = async (
    message: string,
    messageId?: string,
    fIds?: string[],
    hotQuestionId?: number,
    options?: {
      type?: string;
      focusMode?: string;
      history?: [string, string][];
      taskId?: number;
    }
  ) => {
    if (loading) return;

    if (!(await chatBattery.checkEnoughBattery(focusMode))) {
      return;
    }

    // 检查WebSocket连接是否就绪
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      toast.error('连接断开，无法发送消息');
      return;
    }

    // 检查组件是否完全初始化
    if (!isReady) {
      toast.error('系统正在准备中，请稍后再试');
      return;
    }

    // 检查chatId是否已设置
    if (!chatId) {
      toast.error('聊天ID未初始化，请刷新页面重试');
      return;
    }

    setLoading(true);
    setMessageAppeared(false);

    let sources: Document[] | undefined = undefined;
    let recievedMessage = '';
    let added = false;

    messageId = messageId ?? MessageUtils.generateMessageId(7);

    const deviceId = localStorage.getItem('deviceId');

    // 构建消息对象
    const msg = {
      messageId: messageId,
      chatId: chatId!,
      content: String(message),
      userId,
      deviceId,
      role: 'user',
      createdAt: new Date(),
    };

    clkLog('发送消息', { ...msg, focusMode, channelId });

    // 发送消息到服务器
    const wsMessage = {
      type: options?.type || 'message',
      message: msg,
      files: fIds || fileIds,
      focusMode: options?.focusMode || focusMode,
      optimizationMode: optimizationMode,
      history: options?.history || [...chatHistory, ['human', message]],
      source: channelId || 'web',
      hotQuestionId,
      taskId: options?.taskId
    };

    ws.send(JSON.stringify(wsMessage));

    // 更新本地消息列表
    setMessages((prevMessages) => [
      ...prevMessages,
      msg as Message,
    ]);

    const messageHandler = async (e: MessageEvent) => {
      const data = JSON.parse(e.data);

      if (data.type === 'error') {
        toast.error(data.data);
        setLoading(false);
        return;
      }

      if (data.type === 'sources') {
        sources = data?.data || [];
        if (!added) {
          setMessages((prevMessages: any) => [
            ...prevMessages,
            {
              content: '',
              messageId: data.messageId,
              chatId: chatId!,
              role: 'assistant',
              sources: sources,
              createdAt: new Date(),
            },
          ]);
          added = true;
        }
        setMessageAppeared(true);
      }

      // 添加对 reasoning 类型消息的处理
      if (data.type === 'reasoning') {
        if (!added) {
          setMessages((prevMessages: any) => [
            ...prevMessages,
            {
              content: '',
              messageId: data.messageId,
              chatId: chatId!,
              role: 'assistant',
              sources: sources,
              reasoning_content: data.data,  // 保存推理内容
              createdAt: new Date(),
            },
          ]);
          added = true;
        } else {
          setMessages((prev) =>
            prev.map((message) => {
              if (message.messageId === data.messageId) {
                return {
                  ...message,
                  reasoning_content: (message.reasoning_content || '') + data.data
                };
              }
              return message;
            }),
          );
        }
      }

      if (data.type === 'message') {
        if (!added) {
          setMessages((prevMessages: any) => [
            ...prevMessages,
            {
              content: data.data,
              messageId: data.messageId,
              chatId: chatId!,
              role: 'assistant',
              sources: sources,
              reasoning_content: '',  // 初始化 reasoning_content
              createdAt: new Date(),
            },
          ]);
          added = true;
        }

        setMessages((prev) =>
          prev.map((message) => {
            if (message.messageId === data.messageId) {
              return {
                ...message,
                content: message.content + data.data,
                reasoning_content: message.reasoning_content || ''  // 确保保留现有的推理内容
              };
            }
            return message;
          }),
        );

        recievedMessage += data.data;
        setMessageAppeared(true);
      }

      if (data.type === 'messageEnd') {
        chatBattery.getBattery(); // 更新灵豆信息
        setChatHistory((prevHistory) => [
          ...prevHistory,
          ['human', message],
          ['assistant', recievedMessage],
        ]);

        ws?.removeEventListener('message', messageHandler);
        setLoading(false);

        const lastMsg = messagesRef.current[messagesRef.current.length - 1];


        if (
          lastMsg.role === 'assistant' &&
          !lastMsg.suggestions
        ) {
          const { suggestions = [], relatedContent = [] } = await getSuggestions(recentMessages(messagesRef.current), task?.agentType);
          setMessages((prev) =>
            prev.map((msg) => {
              if (msg.messageId === lastMsg.messageId) {
                return { ...msg, suggestions, relatedContent };
              }
              return msg;
            }),
          );
        }
      }
    };

    ws?.addEventListener('message', messageHandler);
  };

  const rewrite = (messageId: string) => {
    const index = messages.findIndex((msg) => msg.messageId === messageId);

    if (index === -1) return;

    const message = messages[index - 1];

    setMessages((prev) => {
      return [...prev.slice(0, messages.length > 2 ? index - 1 : 0)];
    });
    setChatHistory((prev) => {
      return [...prev.slice(0, messages.length > 2 ? index - 1 : 0)];
    });

    sendMessage(message.content, message.messageId);
  };

  useEffect(() => {
    if (isReady && initialMessage && ws?.readyState === 1) {
      sendMessage(initialMessage);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ws?.readyState, isReady, initialMessage, isWSReady]);

  if (hasError) {
    return (
      <div className="relative">
        <div className="absolute w-full flex flex-row items-center justify-end mr-5 mt-5">
          <Settings
            className="cursor-pointer lg:hidden"
            onClick={() => setIsSettingsOpen(true)}
          />
        </div>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <p className="dark:text-white/70 text-black/70 text-sm">
            连接服务器失败。请稍后再试。
          </p>
        </div>
        <SettingsDialog isOpen={isSettingsOpen} setIsOpen={setIsSettingsOpen} />
      </div>
    );
  }

  return isReady ? (
    notFound ? (
      <NextError statusCode={404} />
    ) : (
      <ChatContext.Provider value={{ channelId, chatBattery, focusMode, chatId: chatId || id, sendMessage, isApp: isApp.current, isMobile: FZ_JsBridge.isApp || FZ_JsBridge.isMobile || ['mp', 'mp-kl', 'gy-andriod', 'gy-ios'].includes(channelId) }}>
        {messages.length > 0 ? (
          <>
            <Chat
              loading={loading}
              messages={messages}
              sendMessage={sendMessage}
              messageAppeared={messageAppeared}
              rewrite={rewrite}
              fileIds={fileIds}
              setFileIds={setFileIds}
              files={files}
              setFiles={setFiles}
            />
          </>
        ) : (
          <EmptyChat
            sendMessage={sendMessage}
            focusMode={focusMode}
            setFocusMode={handleSetFocusMode}
            optimizationMode={optimizationMode}
            setOptimizationMode={setOptimizationMode}
            fileIds={fileIds}
            setFileIds={setFileIds}
            files={files}
            setFiles={setFiles}
          />
        )}
      </ChatContext.Provider>
    )
  ) : (
    <div className="fixed inset-0 flex items-center justify-center bg-white/50 dark:bg-black/50 z-50">
      <svg
        aria-hidden="true"
        className="w-8 h-8 text-light-200 fill-light-secondary dark:text-[#202020] animate-spin dark:fill-[#ffffff3b]"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M100 50.5908C100.003 78.2051 78.1951 100.003 50.5908 100C22.9765 99.9972 0.997224 78.018 1 50.4037C1.00281 22.7993 22.8108 0.997224 50.4251 1C78.0395 1.00281 100.018 22.8108 100 50.4251ZM9.08164 50.594C9.06312 73.3997 27.7909 92.1272 50.5966 92.1457C73.4023 92.1642 92.1298 73.4365 92.1483 50.6308C92.1669 27.8251 73.4392 9.0973 50.6335 9.07878C27.8278 9.06026 9.10003 27.787 9.08164 50.594Z"
          fill="currentColor"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4037 97.8624 35.9116 96.9801 33.5533C95.1945 28.8227 92.871 24.3692 90.0681 20.348C85.6237 14.1775 79.4473 9.36872 72.0454 6.45794C64.6435 3.54717 56.3134 2.65431 48.3133 3.89319C45.869 4.27179 44.3768 6.77534 45.014 9.20079C45.6512 11.6262 48.1343 13.0956 50.5786 12.717C56.5073 11.8281 62.5542 12.5399 68.0406 14.7911C73.527 17.0422 78.2187 20.7487 81.5841 25.4923C83.7976 28.5886 85.4467 32.059 86.4416 35.7474C87.1273 38.1189 89.5423 39.6781 91.9676 39.0409Z"
          fill="currentFill"
        />
      </svg>
    </div>
  );
};

export default ChatWindow;
