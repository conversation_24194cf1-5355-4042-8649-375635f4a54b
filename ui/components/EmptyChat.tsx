import './styles/marquee.css';
import { File } from './chat/ChatTools';
import { clkLog, withQuery } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { useDeviceContext } from './context/DeviceContext';
import { useSelectedChat } from './context/SelectedChatContext';
import { useRouter } from 'next/navigation';
import PortalHeader from '../components/ai/PortalHeader';
import HotTopics from '../components/ai/HotTopics';
import MainInputCard from '../components/ai/MainInputCard';
import ResumeAnalysisCard from '../components/ai/ResumeAnalysisCard';
import CustomTabBar from './common/CustomTabBar/index';
import { jmWx } from '@/lib/wx';

const EmptyChat = ({
  sendMessage,
  focusMode,
  setFocusMode,
}: {
  sendMessage: (message: string, messageId?: string, fileIds?: Array<any>, questionId?: any) => void;
  focusMode: string;
  setFocusMode: (mode: string) => void;
  optimizationMode: string;
  setOptimizationMode: (mode: string) => void;
  fileIds: string[];
  setFileIds: (fileIds: string[]) => void;
  files: File[];
  setFiles: (files: File[]) => void;
}) => {

  const { deviceId, userInfo, channelId }: any = useDeviceContext();
  const showResumeAnalysis = ['mp-kl', 'guanwang', 'xuexi', ''].includes(channelId) || !channelId

  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return '上午好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };
  const [selectedQuestions, setSelectedQuestions] = useState<any>({ list: [] });
  const { fetchChats, setCurrentPage } = useSelectedChat();
  const userDetail = userInfo?.data?.detail;
  const [hotQuestions, setHotQuestions] = useState<Array<{ id: number, title: string, chatId: string | null }>>([]);
  const router = useRouter();

  // 获取热门问题
  useEffect(() => {
    const fetchHotQuestions = async () => {
      try {
        const headers: any = {
          'Content-Type': 'application/json'
        };
        if (deviceId) {
          headers['X-Device-ID'] = deviceId;
        }
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/hot-questions`, {
          method: 'GET',
          headers
        });
        const data = await response.json();
        setHotQuestions(data.questions);
      } catch (error) {
        console.error('Failed to fetch hot questions:', error);
      }
    };

    fetchHotQuestions();
  }, [deviceId]);

  const doSetFocusMode = (focusMode: string) => {
    setFocusMode(focusMode);
    setSelectedQuestions({ list: [] })
  }

  const gotoHotQuestion = async (question: any) => {
    clkLog('热门问题', { "问题内容": question.title, chatId: question.chatId });
    if (question.chatId) {
      router.push(withQuery(`/c/${question.chatId}`));
    } else {
      await sendMessage(question.title, undefined, undefined, question.id);
      setTimeout(() => {
        setCurrentPage(1);
        fetchChats();
      }, 2000);
    }
  }

  const goHistory = () => {
    clkLog('历史记录');
    router.push(withQuery('/library'));
  };

  const onTabClick = (page: any) => {
    clkLog('切换标签', page);
    jmWx.switchTab({ url: page.pagePath });
  }

  useEffect(() => {
    if (selectedQuestions.list.length > 0) {
      document.body.style.overflow = 'hidden';
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        if (!target.closest('.suggestions-dropdown')) {
          setSelectedQuestions({ list: [] });
        }
      };
      document.addEventListener('click', handleClickOutside);
      return () => {
        document.removeEventListener('click', handleClickOutside);
        document.body.style.overflow = '';
      };
    } else {
      document.body.style.overflow = '';
    }
  }, [selectedQuestions.list.length]);

  return (<>
    <div className="min-h-screen w-full bg-gradient-to-b from-white to-slate-50 flex flex-col pb-[calc(env(safe-area-inset-bottom)* (4 / 15)vw + 15vw)]">
      <PortalHeader greeting={getGreeting()} goHistory={goHistory} useName={userDetail?.name} />
      <div className="flex-1 flex flex-col justify-center items-center w-full max-w-2xl mx-auto px-4 space-y-6 pb-12">
        <HotTopics questions={hotQuestions} onTopicClick={gotoHotQuestion} />
        <div className="w-full max-w-xl">
          <MainInputCard onSendMessage={sendMessage} isLoading={isLoading}
            focusMode={focusMode}
            setFocusMode={doSetFocusMode}
          />
        </div>
        {showResumeAnalysis && <ResumeAnalysisCard />}
        <footer className="text-xs text-gray-400 text-center">
          内容由AI生成，仅供参考
        </footer>
      </div>
    </div>
    {channelId === 'mp-kl' && <CustomTabBar currentPage="/pages/ai/index" onTabClick={onTabClick} />}
  </>);
};

export default EmptyChat;
