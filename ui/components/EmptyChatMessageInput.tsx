import { useEffect, useRef, useState } from 'react';
import ConsumeButton from '@/components/common/ConsumeButton';
import Focus from './MessageInputActions/Focus';
import { questions } from './config';
import { FZ_JsBridge } from '../lib/js-bridge';
import { File } from './chat/ChatTools';
import { useDeviceContext } from './context/DeviceContext';
import RichTextEditor from './RichTextEditor';
import { clkLog } from '@/lib/utils';
import { focusModes } from '../components/MessageInputActions/Focus';
import { useSelectedChat } from './context/SelectedChatContext';
import { useChatContext } from './context/ChatContext';

const EmptyChatMessageInput = ({
  sendMessage,
  focusMode,
  setFocusMode,
  defaultInput = '',
  selectedButton,
  setSelectedQuestions
}: {
  sendMessage: (message: string) => void;
  focusMode: string;
  setFocusMode: (mode: string) => void;
  optimizationMode: string;
  setOptimizationMode: (mode: string) => void;
  fileIds: string[];
  setFileIds: (fileIds: string[]) => void;
  files: File[];
  setFiles: (files: File[]) => void;
  defaultInput?: string;
  selectedButton?: any;
  setSelectedQuestions: (questions: any) => void;
}) => {
  const { deviceId, channelId } = useDeviceContext();
  const [message, setMessage] = useState(defaultInput);

  const inputRef: any = useRef<any>(null);

  const { fetchChats, setCurrentPage } = useSelectedChat();
  const { chatBattery: { battery: { taskPoints = {} } = {} } = {} } = useChatContext() as any;
  const placeholder = focusModes.find((it: any) => it.key === focusMode)?.placeholder || '输入您的问题...';

  const handleSendMessage = (e: any) => {
    e.preventDefault();
    // 解析文档内容解析实际文本
    const messageText = (inputRef.current?.getContent() || '').trim();
    if (!messageText) {
      return
    };
    const list = questions.reduce((acc: any, cur: any) => {
      if (cur.key === message) {
        acc = cur;
      }
      acc.push(...cur.list)
      return acc;
    }, []);
    const { prompt = '' } = list.find((it: any) => it.label === selectedButton) || {};
    sendMessage(messageText + prompt);
    setMessage('');
    clkLog("发送提问", { "问题内容": messageText + prompt, channelId });

    setTimeout(() => {
      setCurrentPage(1);
      fetchChats();
    }, 2000);
  };

  useEffect(() => {
    setMessage(defaultInput);
  }, [defaultInput]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const activeElement = document.activeElement;

      const isInputFocused =
        activeElement?.tagName === 'INPUT' ||
        activeElement?.tagName === 'TEXTAREA' ||
        activeElement?.hasAttribute('contenteditable');

      if (e.key === '/' && !isInputFocused && !FZ_JsBridge.isApp) {
        e.preventDefault();
        inputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    if (deviceId && !FZ_JsBridge.isApp) {
      inputRef.current?.focus();
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [deviceId]);

  const [isComposing, setIsComposing] = useState(false);

  return (
    <form
      onSubmit={handleSendMessage}
      onKeyDown={(e) => {
        if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
          handleSendMessage(e);
        }
      }}
      onCompositionStart={() => setIsComposing(true)}
      onCompositionEnd={() => setIsComposing(false)}
      className="w-full px-[10px]"
    >
      <div className="flex flex-col bg-white dark:bg-dark-secondary p-2 rounded-lg w-full border-1 border-[#4D6BFE] shadow-[0_0_10px_rgba(77,107,254,0.25)]">
        <RichTextEditor
          ref={inputRef}
          value={message}
          onFocus={() => setSelectedQuestions({ list: [] })}
          placeholder={placeholder}
          onChange={(value) => setMessage(value)}
        />
        <div className="flex flex-row items-center justify-between mt-4">
          <div className="flex flex-row items-center space-x-2 lg:space-x-4">
            <Focus focusMode={focusMode} setFocusMode={setFocusMode} />
          </div>
          <div className="flex flex-row items-center space-x-1 sm:space-x-4">
            <ConsumeButton disabled={message.trim().length === 0} title="发送" consumePoints={taskPoints[focusMode]} />
          </div>
        </div>
      </div>
    </form>
  );
};

export default EmptyChatMessageInput;
