"use client";

import { useRouter, useSearchParams } from "next/navigation";
import ChatWindow from "./ChatWindow";
import { useState } from "react";
import BackNavigationWrapper from "./common/BackNavigationWrapper";
import Navbar from "./Navbar";
import { withQuery } from "@/lib/utils";

const HomePage = () => {
    const usp = useSearchParams() as any;
    const refresh = usp.get('refresh');
    const [messages, setMessages] = useState([] as any[]);
    console.log("🚀 ~ HomePage ~ messages:", messages)

    const title = messages?.[0]?.content || '详情';
    const chatId = messages?.[0]?.chatId || '';
    const router = useRouter();

    const creatNewChat = () => {
        router.push(withQuery('/', { refresh: `${Math.random()}` }, ['source', 'taskId']));
    };

    return (<div className="flex flex-col h-full">
        {chatId && <div className="sticky top-0 z-10 bg-white dark:bg-dark-primary px-4 lg:mx-0 border-b border-gray-200 dark:border-gray-800">
            <div className="container mx-auto lg:px-4 py-1">
                <BackNavigationWrapper title={title} onBack={creatNewChat}>
                    <Navbar messages={messages} chatId={chatId} />
                </BackNavigationWrapper>
            </div>
        </div>}
        <div className="flex-1 overflow-auto">
            <ChatWindow key={refresh} asyncMessages={setMessages} />
        </div>
    </div>)

};

export default HomePage;
