import Marquee from 'react-fast-marquee';
import { cn } from '@/lib/utils';

interface HotQuestion {
    id: number;
    title: string;
    chatId: string | null;
}

interface HotQuestionsSwiperProps {
    questions: HotQuestion[];
    onQuestionClick: (question: HotQuestion) => void;
}

const HotQuestionsSwiper = ({ questions, onQuestionClick }: HotQuestionsSwiperProps) => {
    if (!questions.length) return null;

    const renderQuestion = (question: HotQuestion) => (
        <button
            onClick={() => onQuestionClick(question)}
            className={cn(
                "h-[33px] bg-white dark:bg-dark-secondary text-gray-500 relative width-fit text-left text-[12px] rounded-[8px] border border-gray-100 dark:border-gray-800 hover:border-[#4D6BFE]/50 dark:hover:border-[#4D6BFE]/50 hover:bg-[#4D6BFE]/3 dark:hover:bg-[#4D6BFE]/3 hover:shadow-lg transition-all duration-200 truncate overflow-hidden whitespace-nowrap flex items-center gap-1",
                question.chatId ? "pl-5 text-[#9C9C9C] pl-[7px] pr-[5.5px]" : "text-[#333333] pl-[11.5px] pr-[9px]"
            )}
        >
            {question.chatId && (
                <span className="inline-flex items-center gap-[4px]">
                    <img className="w-[14px] h-[9.5px]" src="https://static.cyjiaomu.com/ai/read.png" alt="已问答" />
                    <span className="text-[#3B5A96]">已读</span>
                </span>
            )}
            {question.title}
        </button>
    );

    const halfLength = Math.ceil(questions.length / 2);
    const firstRow = questions.slice(0, halfLength);
    const secondRow = questions.slice(halfLength);

    return (
        <div className="w-full overflow-hidden mt-[38px]">
            <div className="flex flex-col gap-2">
                <Marquee
                    gradient={false}
                    speed={20}
                    pauseOnHover={true}
                    className="overflow-y-hidden !overflow-x-auto hide-scrollbar"
                >
                    <div className="flex gap-1 px-1">
                        {firstRow.map((question, index) => (
                            <div key={`${question.id}-${index}`} className="mx-1">
                                {renderQuestion(question)}
                            </div>
                        ))}
                    </div>
                </Marquee>
                <Marquee
                    gradient={false}
                    speed={20}
                    pauseOnHover={true}
                    direction="right"
                    className="overflow-y-hidden !overflow-x-auto hide-scrollbar"
                >
                    <div className="flex gap-1 px-1">
                        {secondRow.map((question, index) => (
                            <div key={`${question.id}-${index}`} className="mx-1">
                                {renderQuestion(question)}
                            </div>
                        ))}
                    </div>
                </Marquee>
            </div>
        </div>
    );
};

export default HotQuestionsSwiper;