'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FZ_JsBridge } from '../lib/js-bridge';
import { DeviceContext } from './context/DeviceContext';
import { cn } from '@/lib/utils';
import { jmWx } from '@/lib/wx';
import { get } from '@/lib/request';
import * as CryptoJS from 'crypto-js';

interface ChildProps {
  deviceId?: string;
  isExpanded?: boolean;
  userInfo?: boolean;
}

const Layout = ({ children, isExpanded }: { children: React.ReactNode; isExpanded?: boolean }) => {
  const router = useRouter();
  const [deviceId, setDeviceId] = useState('');
  const query: any = useSearchParams();
  const channelId: any = query.get('td_channelid');
  const [userInfo, setUserInfo] = useState<any>(null);

  const getDeviceId = async (token: any) => {
    let userId = ''
    try {
      const userDetailStr = localStorage.getItem('__user_detail__') || '{}'
      const userDetail = JSON.parse(userDetailStr) || {}
      if (userDetail?.data?.detail?.user_id?.startsWith?.('trial_use_')) { // 试用用户不更新deviceId
        userId = userDetail?.data?.detail?.user_id
      } else {
        const timestamp = userDetail.timestamp || ''
        const now = new Date()
        const lastUpdate = timestamp ? new Date(timestamp) : new Date(0)
        const isExpired = lastUpdate.getFullYear() !== now.getFullYear() ||
          lastUpdate.getMonth() !== now.getMonth() ||
          lastUpdate.getDate() !== now.getDate()
        userId = !isExpired ? userDetail?.data?.detail?.user_id || '' : ''
      }
    } catch (error) {
      console.warn("Failed to parse user detail:", error)
    }
    if (!userId || userId?.startsWith?.('trial_use_')) {
      let res: any = await get({ url: `https://user-api.cyjiaomu.com/api/v2/user/detail`, headers: { 'Content-Type': 'application/json', token } });
      const { data: { detail: { user_id = '' } = {} } = {} } = res || {};
      if (!user_id) {
        if (!userId) {
          const rId = Math.floor(Math.random() * 10000);
          userId = `trial_use_${rId}`;
          res = { "code": 0, "data": { "detail": { "isVip": false, "user_id": userId, "name": "灵通君", "avatar": "https://static.cyjiaomu.com/ai/icons/ai-plus.png", "resume": "", "introduce": "", "rule": null, "is_self": true, "is_followed": false, "fans_count": 0, "keywords": "" } } };
          localStorage.setItem('__user_detail__', JSON.stringify({ ...res, timestamp: new Date().toISOString() }));
        }
      } else {
        const member: any = await get({ url: `${process.env.NEXT_PUBLIC_API_URL}/billing/checkUserMember?userId=${user_id}` });
        res.data.detail.isVip = !!(member?.data?.info?.valid);
        res.timestamp = new Date().toISOString();
        userId = user_id;
        localStorage.setItem('__user_detail__', JSON.stringify(res));
      }
    }
    setUserInfo(JSON.parse(localStorage.getItem('__user_detail__') || '{}'));
    // 使用SHA-256对user_id进行加密
    const encryptedUserId = CryptoJS.SHA256(CryptoJS.enc.Utf8.parse(userId)).toString(CryptoJS.enc.Hex);
    return encryptedUserId || userId || '';
  };

  const getToken = (): any => {
    try {
      if (FZ_JsBridge.isApp) {
        return FZ_JsBridge.getToken();
      }

      // 从 cookie 中获取 token
      const cookieToken = document.cookie
        .split('; ')
        .find(row => row.startsWith('c_token='))
        ?.split('=')[1];
      if (cookieToken) return cookieToken;

      // 从 URL 参数中获取 token
      const urlToken = query.get('token');
      if (urlToken) {
        return urlToken
      }
    } catch (error) {

    }
    return undefined;
  };

  const goLogin = async () => {

    if (FZ_JsBridge.isApp) {
      return FZ_JsBridge.login();
    }
    if (['mp', 'mp-kl'].includes(channelId)) {
      // 动态加载微信JS SDK
      return jmWx.navigateTo({ url: `/pages/login/index` });
    }
    const currentUrl = encodeURIComponent(window.location.href);
    const loginUrl = `https://sso.cyjiaomu.com/login?r=https://xuexi.cyjiaomu.com/api/login/ticket?r=${currentUrl}`;
    return window.location.replace(loginUrl);
  }

  const loadWxSDK = async () => {
    try {
      // 确保wx对象可用后再执行后续逻辑
      const token = getToken();
      const id: any = await getDeviceId(token);
      localStorage.setItem('deviceId', id);
      setDeviceId(id);
      if (token && token !== 'logout') {
        localStorage.setItem('chatToken', token);
        return;
      }
    } catch (error) {
      console.error('Failed to load WeChat JS SDK:', error);
    }
  };

  useEffect(() => {
    loadWxSDK();
  }, [router]);

  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement<ChildProps>(child)) {
      return React.cloneElement(child, { deviceId, isExpanded });
    }
    return child;
  });

  return (
    <DeviceContext.Provider value={{ deviceId, channelId, userInfo, query, isMobile: FZ_JsBridge.isMobile }}>
      <main className={cn("layout-main h-full lg:m-auto lg:w-full")}>
        <div className="max-w-full lg:mx-auto h-full">{childrenWithProps}</div>
        {!deviceId && <div className="mask fixed inset-0 bg-black opacity-0 cursor-pointer z-50" onClick={goLogin} />}
      </main>
    </DeviceContext.Provider>
  );
};

export default Layout;
