import React from 'react';
import ReactDOM from 'react-dom';

interface LoadingProps {
    loading: boolean;
    messages?: string;
}

const Loading = ({ loading, messages }: LoadingProps) => {
    if (!loading) return null;

    return ReactDOM.createPortal(
        <div className="fixed inset-0 bg-black/1 backdrop-blur-sm z-[3] flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            {messages && (
                <div className="bg-white/90 dark:bg-gray-600 rounded-md px-4 py-2 max-w-2xl mx-4 max-h-[60vh] overflow-y-auto">
                    <p className="text-gray-800 dark:text-gray-100 whitespace-pre-wrap">{messages}</p>
                </div>
            )}
        </div>,
        document.body
    );
};

export default Loading;