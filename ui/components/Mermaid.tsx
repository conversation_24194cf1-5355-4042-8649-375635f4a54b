import React, { useEffect, useState } from "react";
import mermaid, { MermaidConfig } from "mermaid";

// 初始化mermaid配置
const config: Partial<MermaidConfig> = {
    startOnLoad: true,
    theme: typeof window === 'undefined' ? "default" : (window?.matchMedia?.('(prefers-color-scheme: dark)')?.matches ? 'dark' : 'default'),
    securityLevel: "loose",
    themeVariables: {
        fontFamily: 'system-ui, sans-serif'
    },
    flowchart: {
        curve: 'basis',
        padding: 20
    },
    journey: {
        taskFontSize: '14px',
        taskMargin: 20,
        taskHeight: 40
    } as any
};

mermaid.initialize({
    ...config,
    parseError: (err: any) => {
        console.warn('Mermaid parse error:', err);
        return true; // 允许继续处理错误
    }
} as MermaidConfig);

export default function Mermaid(props: any = {}) {
    const { chart } = props;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [initialScale, setInitialScale] = useState(1);
    const [key, setKey] = useState(0);

    useEffect(() => {
        setKey(k => k + 1); // 强制组件重新渲染
    }, [chart]);

    useEffect(() => {
        if (isModalOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
        return () => {
            document.body.style.overflow = '';
        };
    }, [isModalOpen]);
    const [isZoomed, setIsZoomed] = useState(false);
    const [lastTap, setLastTap] = useState(0);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [touchStart, setTouchStart] = useState({ x: 0, y: 0 });
    const [renderError, setRenderError] = useState(false);
    const [rotation, setRotation] = useState(0);
    const [scale, setScale] = useState(1);
    const [isFullscreen, setIsFullscreen] = useState(false);

    useEffect(() => {
        const renderChart = async () => {
            try {
                await mermaid.contentLoaded();
                const container = document.querySelector('.mermaid-modal-content');
                const chart = container?.querySelector('.mermaid');
                if (container && chart) {
                    const containerWidth = container.clientWidth;
                    const chartWidth = chart.scrollWidth;
                    const scale = Math.min(containerWidth / chartWidth, 2);
                    setInitialScale(scale);
                    setScale(scale);
                }
            } catch (error) {
                console.error('Mermaid rendering error:', error);
                setRenderError(true);
            }
        };

        if (isModalOpen) {
            setTimeout(renderChart, 100);
        } else {
            setInitialScale(1);
            setScale(1);
        }

        try {
            const isDarkMode = window?.matchMedia?.('(prefers-color-scheme: dark)')?.matches;
            const config: Partial<MermaidConfig> = {
                startOnLoad: true,
                theme: typeof window === 'undefined' ? "default" : (window?.matchMedia?.('(prefers-color-scheme: dark)')?.matches ? 'dark' : 'default'),
                securityLevel: "loose",
                themeVariables: {
                    fontFamily: 'system-ui, sans-serif'
                },
                flowchart: {
                    curve: 'basis',
                    padding: 20
                }
            };

            mermaid.initialize({
                ...config,
                startOnLoad: true,
                theme: isDarkMode ? 'dark' : 'default',
                securityLevel: "loose",
                themeVariables: {
                    fontFamily: 'system-ui, sans-serif'
                },
                flowchart: {
                    curve: 'basis',
                    padding: 20
                },
                parseError: (err: any) => {
                    console.warn('Mermaid parse error:', err);
                    setRenderError(true);
                    return true; // 允许继续处理错误
                }
            } as MermaidConfig);
            mermaid.contentLoaded()
        } catch (error) {
            console.error('Mermaid rendering error:', error);
        }
    }, [chart, isModalOpen]);

    if (!chart) return null;

    const handleClick = () => {
        setIsModalOpen(true);
    };

    const handleTouchStart = (e: React.TouchEvent) => {
        if (e.touches.length === 2) {
            e.preventDefault();
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const distance = Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
            );
            const angle = Math.atan2(
                touch2.clientY - touch1.clientY,
                touch2.clientX - touch1.clientX
            );
            setTouchStart({
                x: distance,
                y: angle
            });
            setIsDragging(false);
        } else if (e.touches.length === 1) {
            setIsDragging(true);
            const touch = e.touches[0];
            setTouchStart({ x: touch.clientX - position.x, y: touch.clientY - position.y });
        }
    };

    const handleTouchMove = (e: React.TouchEvent) => {
        if (e.touches.length === 2) {
            e.preventDefault();
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const distance = Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
            );
            const angle = Math.atan2(
                touch2.clientY - touch1.clientY,
                touch2.clientX - touch1.clientX
            );
            const scaleDiff = distance / touchStart.x;
            const rotationDiff = angle - touchStart.y;
            setScale(s => Math.min(Math.max(s * scaleDiff, 0.5), 10));
            setRotation(r => (r + (rotationDiff * 180 / Math.PI)) % 360);
            setTouchStart({
                x: distance,
                y: angle
            });
        } else if (isDragging && e.touches.length === 1) {
            e.preventDefault();
            const touch = e.touches[0];
            setPosition({
                x: touch.clientX - touchStart.x,
                y: touch.clientY - touchStart.y
            });
        }
    };

    const handleTouchEnd = () => {
        setIsDragging(false);
    };

    const handleMouseDown = (e: React.MouseEvent) => {
        setIsDragging(true);
        setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    };

    const handleMouseMove = (e: React.MouseEvent) => {
        if (isDragging) {
            setPosition({
                x: e.clientX - dragStart.x,
                y: e.clientY - dragStart.y
            });
        }
    };

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    const handleDoubleClick = () => {
        setIsZoomed(!isZoomed);
        setPosition({ x: 0, y: 0 });
    };

    const handleFullscreen = async () => {
        if (!document.fullscreenElement) {
            await document.documentElement.requestFullscreen();
            setIsFullscreen(true);
        } else {
            await document.exitFullscreen();
            setIsFullscreen(false);
        }
    };

    const handleReset = () => {
        setScale(1);
        setRotation(0);
        setPosition({ x: 0, y: 0 });
    };

    return (
        <>
            <div
                className="mermaid cursor-pointer"
                style={{ textAlign: 'center', overflow: 'auto', maxWidth: '100%' }}
                onClick={handleClick}
            >
                {renderError ? (
                    <pre className="whitespace-pre-wrap text-left p-4 bg-gray-100 dark:bg-gray-800 rounded">
                        {chart}
                    </pre>
                ) : chart}
            </div>
            {isModalOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
                    onClick={() => setIsModalOpen(false)}
                >
                    <div
                        className={`relative bg-white dark:bg-dark-secondary rounded-lg p-6 overflow-hidden transition-all duration-300 ${isZoomed ? 'w-screen h-screen' : 'w-[90vw] h-[90vh]'}`}
                        ref={node => {
                            if (node) {
                                node.classList.add('mermaid-modal-content');
                            }
                        }}
                        onClick={e => e.stopPropagation()}
                        onTouchStart={handleTouchStart}
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={handleMouseUp}
                        onMouseLeave={handleMouseUp}
                        onTouchMove={handleTouchMove}
                        onTouchEnd={handleTouchEnd}
                    >
                        <div className="absolute z-[1] top-2 right-2 flex gap-2">
                            <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onClick={() => setScale(s => Math.min(s + 0.5, 10))}
                                title="放大"
                            >
                                <svg className="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </button>
                            <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onClick={() => setScale(s => Math.max(s - 0.5, 0.5))}
                                title="缩小"
                            >
                                <svg className="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
                                </svg>
                            </button>
                            <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onClick={() => setRotation(r => (r + 90) % 360)}
                                title="旋转"
                            >
                                <svg className="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </button>
                            <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onClick={handleFullscreen}
                                title={isFullscreen ? "退出全屏" : "全屏"}
                            >
                                <svg className="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    {isFullscreen ? (
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5-5m0 0l5-5m-5 5h16m0-5l5 5m0 0l-5 5m5-5H4" />
                                    ) : (
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-9v4m0-4h-4m4 4l-5 5M4 16v4m0-4H8m-4 4l5-5m11 5v-4m0 4h-4m4-4l-5 5" />
                                    )}
                                </svg>
                            </button>
                            <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onClick={handleReset}
                                title="还原"
                            >
                                <svg className="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </button>
                            <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onClick={() => setIsModalOpen(false)}
                                title="关闭"
                            >
                                <svg className="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div
                            className="mermaid w-full h-full flex items-center justify-center"
                            key={`modal-${chart}`}
                            style={{
                                transform: `scale(${scale}) rotate(${rotation}deg) translate(${position.x / scale}px, ${position.y / scale}px)`,
                                transformOrigin: 'center center',
                                transition: isDragging ? 'none' : 'transform 0.3s'
                            }}
                        >
                            {chart}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}