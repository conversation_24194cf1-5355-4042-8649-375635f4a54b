import { Check, CopyIcon } from 'lucide-react';
import { Message } from '../chat/ChatTools';
import { useState } from 'react';

const Copy = ({
  message,
  initialMessage,
}: {
  message: Message;
  initialMessage: string;
}) => {
  const [copied, setCopied] = useState(false);

  return (
    <button
      onClick={() => {
        let contentToCopy = initialMessage;
        if (message?.sources?.length) {
          contentToCopy = `${initialMessage}\n\nCitations:\n${message.sources?.map((source: any, i: any) => `[${i + 1}] ${source.metadata.url}`).join(`\n`)}`;
        }
        navigator.clipboard.writeText(contentToCopy);
        setCopied(true);
        setTimeout(() => setCopied(false), 1500);
      }}
      title="复制至剪贴板"
      className="text-black/70 dark:text-white/70 rounded-xl transition duration-200 hover:text-black dark:hover:text-white relative group"
    >
      {copied ? (
        <div className="flex items-center">
          <Check size={18} />
          <span className="absolute -top-1 left-full ml-2 bg-black/70 dark:bg-white/70 text-white dark:text-black px-2 py-1 rounded text-xs whitespace-nowrap z-10">
            内容已复制至剪贴板
          </span>
        </div>
      ) : (
        <div className="text-black/70 dark:text-white/70 rounded-xl transition duration-200 flex flex-row items-center">
          <CopyIcon size={18} />
          <p className="block text-xs font-medium">复制</p>
        </div>
      )}
    </button>
  );
};

export default Copy;
