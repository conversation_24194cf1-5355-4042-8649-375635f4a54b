import React, { useState } from 'react';
import { X, TriangleAlert } from 'lucide-react';
import { post } from '@/lib/request';
import { clkLog } from '@/lib/utils';

interface FeedbackData {
  reason: string;
  details: string;
}

// onSubmit 现在是可选的
interface FeedbackComponentProps {
  message: any; // 新增：用于关联举报内容的消息ID
  triggerButtonText?: string;
  triggerButtonClassName?: string;
}

const REPORT_OPTIONS = [
  '有害/不安全',
  '色情低俗',
  '谩骂攻击',
  '违法犯罪',
  '隐私相关',
  '侵犯我的著作权、名誉权',
  '其他',
];

const FeedbackReportComponent: React.FC<FeedbackComponentProps> = ({
  triggerButtonText = '举报',
  triggerButtonClassName = 'text-black/70 dark:text-white/70 rounded-xl transition duration-200 flex flex-row items-center space-x-1',
  message // 接收 messageId
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [details, setDetails] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);

  const handleOpen = () => {
    setIsOpen(true);
    setSubmitSuccess(null); // 重置提交状态
  };
  const handleClose = () => setIsOpen(false);

  // 默认的提交处理逻辑
  const onSubmit = async (data: FeedbackData) => {
    setIsSubmitting(true);
    setSubmitSuccess(null);
    console.log('Submitting feedback:', { ...data, message }); // 包含 messageId

    try {
      const params = {
        ...data,
        message,
      };
      await post({
        url: 'https://user-api.foundingaz.com/api/user-feedback',
        body: { content: JSON.stringify(params) },
      })
      clkLog("内容举报", params)
    } catch (error) {
      console.log("🚀 ~ onSubmit ~ error:", error)
    }
    try {
      console.log('Feedback submitted successfully to internal handler!');
      setSubmitSuccess(true);
      // 重置状态并关闭
      setSelectedReason('');
      setDetails('');
      setTimeout(() => {
        handleClose();
      }, 1500); // 成功提示后延迟关闭
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      setSubmitSuccess(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = () => {
    if (selectedReason || details.trim()) {
      const feedbackData = { reason: selectedReason, details };
      onSubmit(feedbackData);
    }
  };

  const isSubmitButtonDisabled = (!selectedReason && !details.trim()) || isSubmitting;

  return (
    <>
      <button
        onClick={handleOpen}
        className={triggerButtonClassName}
        title="举报此消息"
      >
        <TriangleAlert size={18} />
        <p className="block text-xs font-medium">{triggerButtonText}</p>
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm flex justify-center items-center z-50 p-4">
          <div className="bg-white w-full max-w-md rounded-lg shadow-xl transform transition-all sm:max-w-lg">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">举报内容</h2>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600"
                disabled={isSubmitting}
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
              {submitSuccess === true && (
                <div className="mb-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded-md text-sm">
                  举报已提交，感谢您的反馈！此窗口将自动关闭。
                </div>
              )}
              {submitSuccess === false && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded-md text-sm">
                  提交失败，请稍后重试。
                </div>
              )}
              {!submitSuccess && (
                <>
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">请选择举报原因：</p>
                    <div className="flex flex-wrap gap-2">
                      {REPORT_OPTIONS.map((option) => (
                        <label
                          key={option}
                          className={`flex items-center p-3 rounded-md border cursor-pointer transition-colors text-sm 
                                      ${selectedReason === option ? 'bg-blue-50 border-blue-500 ring-1 ring-blue-500' : 'border-gray-300 hover:bg-gray-50'}`}
                        >
                          <input
                            type="radio"
                            name="reportReason"
                            value={option}
                            checked={selectedReason === option}
                            onChange={() => setSelectedReason(option)}
                            disabled={isSubmitting}
                            className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out mr-2"
                          />
                          <span className={`${selectedReason === option ? 'text-blue-700 font-medium' : 'text-gray-700'}`}>{option}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="reportDetails" className="block text-sm font-medium text-gray-700 mb-1">
                      详细举报内容（可选）
                    </label>
                    <textarea
                      id="reportDetails"
                      rows={3}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
                      placeholder="我们想知道你举报的原因，你可以描述你遇到的问题。"
                      value={details}
                      onChange={(e) => setDetails(e.target.value)}
                      disabled={isSubmitting}
                    />
                  </div>
                </>
              )}
            </div>

            {/* Footer */}
            {!submitSuccess && (
              <div className="p-4 border-t bg-gray-50 rounded-b-lg">
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitButtonDisabled}
                  className={`w-full px-4 py-2 text-white font-semibold rounded-md transition-colors text-sm 
                              ${isSubmitButtonDisabled ? 'bg-gray-200 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'}`}
                >
                  {isSubmitting ? '提交中...' : '提交'}
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default FeedbackReportComponent;