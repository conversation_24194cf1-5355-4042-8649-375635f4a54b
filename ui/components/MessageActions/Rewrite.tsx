import { RotateCw } from 'lucide-react';
import { useDeviceContext } from '../context/DeviceContext';
import { useChatContext } from '../context/ChatContext';

const Rewrite = ({
  rewrite,
  messageId,
}: {
  rewrite: (messageId: string) => void;
  messageId: string;
}) => {
  const { query } = useDeviceContext() as any;
  if (query?.share === "true") {
    return null;
  }
  return (
    <button
      onClick={() => rewrite(messageId)}
      title="重新回答"
      className="text-black/70 dark:text-white/70 rounded-xl hover:bg-light-secondary dark:hover:bg-dark-secondary transition duration-200 hover:text-black dark:hover:text-white flex flex-row items-center space-x-1"
    >
      <RotateCw size={18} />
      <p className="block text-xs font-medium">重新回答</p>
    </button>
  );
};

export default Rewrite;
