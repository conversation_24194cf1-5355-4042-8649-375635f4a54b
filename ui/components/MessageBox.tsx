'use client';

/* eslint-disable @next/next/no-img-element */
import React, { MutableRefObject, useEffect, useState } from 'react';
import { Message, recentMessages } from './chat/ChatTools';
import { clkLog } from '@/lib/utils';
import RelatedContentSwiper from './RelatedContentSwiper';
import { FZ_JsBridge } from '../lib/js-bridge';
import Copy from './MessageActions/Copy';
import Rewrite from './MessageActions/Rewrite';
import MessageSources from './MessageSources';
import SearchImages from './SearchImages';
import SearchVideos from './SearchVideos';
import { useSpeech } from 'react-text-to-speech';
import { useDeviceContext } from './context/DeviceContext';
import { useChatContext } from './context/ChatContext';
import { toast } from 'sonner';
import './doMermaid';
import ProseContent from './ProseContent';
import UserMessageBox from './chat/UserMessageBox';
import { jmWx } from '@/lib/wx';
import Suggestions from './chat/Suggestions';
import AnalysisPayload from './chat/AnalysisPayload';
import Feedback from './MessageActions/Feedback';
import { motion, AnimatePresence } from 'framer-motion'; // 引入 motion 和 AnimatePresence
import { ChevronUp, ChevronDown, CheckCircle, Layers } from 'lucide-react'; // 引入 ChevronUp, ChevronDown, CheckCircle, Brain
import { Button } from './ui/button'; // 引入 Button 组件
import ThinkingSection from './chat/ThinkingSection';

const MessageBox = ({
  message,
  history,
  loading,
  dividerRef,
  messageIndex,
  isLast,
  rewrite,
  sendMessage,
  showRewrite,
  onReasoningContentChange,
}: {
  message: Message;
  messageIndex: number;
  history: Message[];
  loading: boolean;
  showRewrite?: boolean;
  dividerRef?: MutableRefObject<HTMLDivElement | null>;
  isLast: boolean;
  rewrite: (messageId: string) => void;
  sendMessage: (message: string) => void;
  onReasoningContentChange?: () => void;
}) => {
  const [parsedMessage, setParsedMessage] = useState(message.content);
  const [speechMessage, setSpeechMessage] = useState(message.content);
  const [isSourcesExpanded, setIsSourcesExpanded] = useState(false);
  const { channelId }: any = useDeviceContext();
  const { isApp } = useChatContext();

  const gotoDifferentPlatform = async (detail: any, type: number) => {
    try {
      if (isApp) {
        // 管用APP
        if (detail.type == 1) {
          return FZ_JsBridge.goToGoods(detail.id, 0, false)
        }
        return FZ_JsBridge.goAutoJump('/home/<USER>', 'Frement_Media_VC', { "id": +detail.id, "course_id": `${detail.id}`, "type": 1 })
      }
      const res: any = await fetch(`https://user-api.cyjiaomu.com/api/ect/number?id=${detail.id}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }).then(res => res.json());

      const { data: { id = '' } = {} } = res || {};

      const routerPath = `https://xuexi.cyjiaomu.com/${type == 1 ? 'course' : 'article'}/${id}`;
      if (["mp-kl", "mp"].includes(channelId)) {
        const mpUrl = `pages/webview/index?url=${encodeURIComponent(routerPath)}`
        // 开练小程序
        if (channelId == "mp-kl") {
          return jmWx.navigateTo({ url: `/pages/middle/index?appId=wx6de5cf35fa883c68&path=${encodeURIComponent(mpUrl)}` });
        }
        // 管用小程序
        if (channelId == "mp") {
          return jmWx.navigateTo({ url: `/${mpUrl}` });
        }
      }
      return window.open(routerPath);
    } catch (error) {
      toast.error("跳转失败，请稍后再试")
    }
  }

  const gotoDetail = (detail: any) => {
    clkLog('管用详情', { "详情内容": detail.title })

    // 跳转到商品详情页
    if (detail?.type == 1) {
      gotoDifferentPlatform(detail, 1)
      return
    }
    // 跳转到图文详情页
    gotoDifferentPlatform(detail, 2)
  }

  useEffect(() => {
    const regex = /(\d+)/g;
    const thinkRegex = /<think>([\s\S]*?)<\/think>/;
    const thinkMatch = message.content.match(thinkRegex);

    let finalContent = message.content;
    let thinkingSectionJsx: JSX.Element | null = null;

    // 判断是否为历史消息
    const isHistory = !isLast;
    // 判断是否为思考中（reasoning_content 存在且 loading 为 true）
    const isThinking = !!message.reasoning_content && loading && isLast;

    if (message.reasoning_content) {
      thinkingSectionJsx = (
        <ThinkingSection
          content={finalContent}
          reasoningContent={message.reasoning_content}
          isHistory={isHistory}
          isThinking={isThinking}
          onReasoningContentChange={onReasoningContentChange}
        />
      );
      finalContent = finalContent.replace(message.reasoning_content, '').trim();
    } else if (thinkMatch) {
      const contentWithoutThinkTag = message.content.replace(thinkRegex, '').trim();
      const thinkContent = thinkMatch[1];
      thinkingSectionJsx = (
        <ThinkingSection
          content={contentWithoutThinkTag}
          reasoningContent={thinkContent}
          isHistory={isHistory}
          isThinking={isThinking}
          onReasoningContentChange={onReasoningContentChange}
        />
      );
      finalContent = contentWithoutThinkTag;
    }

    if (
      message.role === 'assistant' &&
      message?.sources &&
      message.sources.length > 0
    ) {
      setParsedMessage(
        finalContent.replace(
          regex,
          (_, number) =>
            `<a href="${(message.sources?.[parseInt(number) - 1] as any)?.metadata?.url}" target="_blank" className="bg-light-secondary dark:bg-dark-secondary px-1 rounded ml-1 no-underline text-xs text-black/70 dark:text-white/70 relative">${number}</a>`,
        ),
      );
    } else {
      setParsedMessage(finalContent);
    }

    setSpeechMessage(finalContent.replace(regex, ''));

    // 将 thinkingSectionJsx 存储在 message 对象中，以便在渲染时使用
    (message as any).thinkingSectionJsx = thinkingSectionJsx;

  }, [message.content, message.sources, message.role, message.reasoning_content]);

  const { speechStatus, start, stop } = useSpeech({ text: speechMessage });

  return (
    <div>
      {message.role === 'user' && <UserMessageBox message={message} />}

      {message.role === 'assistant' && (
        <div className="flex flex-col space-y-9 lg:space-y-0 lg:flex-row lg:justify-between lg:space-x-9">
          <div
            ref={dividerRef}
            className="flex flex-col space-y-2 w-full"
          >
            <div className='px-4'>
              {message.sources && message.sources.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-3"
                >
                  <Button
                    variant="ghost"
                    onClick={() => setIsSourcesExpanded(!isSourcesExpanded)}
                    className="w-full flex items-center justify-between p-0 h-auto text-left hover:bg-transparent"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-medium text-green-700 text-sm">
                        引用{message.sources.length}篇资料作为参考
                      </span>
                    </div>
                    {isSourcesExpanded ? (
                      <ChevronUp className="w-4 h-4 text-green-600" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-green-600" />
                    )}
                  </Button>

                  <AnimatePresence>
                    {isSourcesExpanded && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="space-y-3 mt-2">
                          <MessageSources sources={message.sources as any} />
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              )}
            </div>
            {(message as any).thinkingSectionJsx && (
              <div className="m-4">
                {(message as any).thinkingSectionJsx}
              </div>
            )}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }} className="flex flex-col px-4">
              <ProseContent content={parsedMessage} />
              {loading && isLast ? null : (
                <div className="copy_rewrite flex flex-row items-center lg:justify-between gap-2 w-fit text-black dark:text-white px-4 mt-4">
                  <div className="flex flex-row items-center space-x-1">
                    <Copy initialMessage={message.content} message={message} />
                  </div>
                  <div className="flex flex-row items-center space-x-1">
                    <Feedback message={message} triggerButtonText="举报" />
                  </div>
                  {showRewrite &&
                    <div className="flex flex-row items-center space-x-1">
                      <Rewrite rewrite={rewrite} messageId={message.messageId} />
                    </div>}
                </div>
              )}
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }} className="flex flex-col">
              <AnalysisPayload analysisPayload={message.analysisPayload} />
              {isLast &&
                message.relatedContent &&
                message.relatedContent.length > 0 &&
                message.role === 'assistant' &&
                !loading && (
                  <div className="space-y-4 mt-4">
                    <div className="px-4 flex items-center gap-2 mb-3">
                      <div className="p-1.5 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500">
                        <Layers className="w-4 h-4 text-white" />
                      </div>
                      <h3 className="text-base font-semibold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                        相关推荐
                      </h3>
                    </div>
                    <RelatedContentSwiper
                      relatedContent={message.relatedContent}
                      onItemClick={gotoDetail}
                    />
                  </div>
                )}
              {isLast &&
                message.suggestions && message.suggestions.length > 0 && (
                  <div className="space-y-4 mt-4">
                    <Suggestions
                      suggestions={message.suggestions}
                      sendMessage={sendMessage}
                    />
                  </div>
                )}
            </motion.div>
          </div>
          <div className="hidden lg:sticky lg:top-20 flex flex-col items-center space-y-3 w-full lg:w-3/12 z-30 h-full pb-4">
            <SearchImages
              query={messageIndex > 0 && history.length > messageIndex - 1 ? history[messageIndex - 1].content : ''}
              chatHistory={recentMessages(history)}
            />
            <SearchVideos
              chatHistory={recentMessages(history)}
              query={messageIndex > 0 && history.length > messageIndex - 1 ? history[messageIndex - 1].content : ''}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MessageBox;
