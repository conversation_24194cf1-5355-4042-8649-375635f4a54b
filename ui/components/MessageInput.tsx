import { Send, Loader2, Gem } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useChatContext } from './context/ChatContext';
const batteryImage = 'https://static.cyjiaomu.com/ai/bean.png';

export default function MessageInput({
  sendMessage,
  loading,
  focusMode,
}: {
  sendMessage: (message: string) => void;
  loading: boolean;
  focusMode: string;
}) {
  const [message, setMessage] = useState('');
  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const { chatBattery: { battery = {} } = {} } = useChatContext() as any;
  const costBeans = battery.taskPoints.webSearch || "限免分析";
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const activeElement = document.activeElement;

      const isInputFocused =
        activeElement?.tagName === 'INPUT' ||
        activeElement?.tagName === 'TEXTAREA' ||
        activeElement?.hasAttribute('contenteditable');

      if (e.key === '/' && !isInputFocused) {
        e.preventDefault();
        inputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (message.trim() && !loading) {
      sendMessage(message.trim());
      setMessage('');
    }
  };

  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="bg-white/90 backdrop-blur-sm border-t border-gray-100 p-4 fixed bottom-0 left-0 right-0 z-20">
      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        onSubmit={handleSubmit}
        className="max-w-3xl mx-auto"
      >
        <div className="relative flex items-end gap-3 bg-white rounded-2xl border border-gray-200 shadow-sm p-3">
          <div className="flex-1 relative">
            <Textarea
              ref={inputRef}
              value={message}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={loading ? "AI正在思考中,请稍候..." : "输入您的问题..."}
              className="min-h-[70px] max-h-32 resize-none border-0 focus:ring-0 bg-transparent text-gray-800 placeholder:text-gray-400 pr-36 py-0" // 增加上下padding和右侧padding
              disabled={loading}
            />
            {/* 绝对定位按钮，右下角 */}
            <div
              style={{
                position: 'absolute',
                right: 0,
                bottom: 0,
                zIndex: 10,
                padding: '0.25rem 0.125rem', // 适当内边距让按钮不贴边
              }}
            >
              <Button
                type="submit"
                disabled={!message.trim() || loading}
                className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-xl px-4 py-2 shadow-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <>
                    <img className="w-4 h-4 fill-white" src={batteryImage} />
                    <span className="font-semibold">{costBeans}</span>
                    <div className="w-px h-4 bg-white/30"></div>
                    <Send className="w-4 h-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        <p className="text-xs text-gray-400 text-center mt-2">
          {loading ? "AI正在为您生成回答..." : "内容由AI生成，仅供参考"}
        </p>
      </motion.form>
    </div>
  );
}