import { Globe, FilePlus2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDeviceContext } from '../context/DeviceContext';
import { useChatContext } from '../context/ChatContext';

export const focusModes = [
  {
    key: 'managementMasterJM',
    title: '酵母管理大师',
    placeholder: `酵母管理大师已升级使用DeepSeek-R1满血版。\n 您可以通过管理大师与酵母的专业管理内容进行交互式对话。\n 管理大师会根据酵母的经验和方法论为您提供专业解答。\n 该功能利用DeepSeek-R1大模型，确保回答的准确性。`,
    icon: <img src="https://static.cyjiaomu.com/ai/managementMaster.svg" alt="酵母管理大师" className="w-[17px] h-[17px]" />,
    selectedIcon: <img src="https://static.cyjiaomu.com/ai/managementMaster-active.svg" alt="酵母管理大师" className="w-[17px] h-[17px]" />,
  },
  {
    key: 'managementMaster',
    title: '管理大师',
    placeholder: `智能管理大师搭载全功能 AI 系统，支持多轮交流，基于创业酵母丰富的管理经验，为你提供更专业、更靠谱的解决方案。`,
    icon: <img src="https://static.cyjiaomu.com/ai/managementMaster.svg" alt="管理大师" className="w-[17px] h-[17px]" />,
    selectedIcon: <img src="https://static.cyjiaomu.com/ai/managementMaster-active.svg" alt="管理大师" className="w-[17px] h-[17px]" />,
  },
  {
    key: 'webSearch',
    title: '全网搜索',
    placeholder: '使用互联网的内容回答您...',
    icon: <Globe size={17} />,
    selectedIcon: <Globe size={17} />,
  },
  {
    key: 'resumeAnalyzer',
    title: '简历分析',
    placeholder: '使用AI的简历分析能力回答您...',
    icon: <FilePlus2 size={17} />,
    selectedIcon: <FilePlus2 size={17} />,
    hidden: true,
  }
];

const Focus = ({
  focusMode,
  setFocusMode,
}: {
  focusMode: string;
  setFocusMode: (mode: string) => void;
}) => {
  const { channelId } = useDeviceContext();
  let finnalMode: any = focusModes;
  const filterKey = channelId === 'jm' ? 'managementMaster' : 'managementMasterJM';
  const { chatBattery: { battery: { availableTasks = [] } = {} } = {} } = useChatContext() as any;
  finnalMode = finnalMode.filter((item: any) => (item?.key !== filterKey && !item?.hidden && availableTasks.includes(item.key)));
  return (
    <div className="flex flex-row items-center space-x-2 w-full max-w-[15rem] md:max-w-md lg:max-w-lg mt-[6.5px] bg-white dark:bg-dark-secondary">
      {finnalMode.map((mode: any) => (
        <button
          key={mode.key}
          onClick={() => setFocusMode(mode.key)}
          type="button"
          title={mode.placeholder}
          className={cn(
            'flex flex-row items-center space-x-1 px-2 h-[27px] rounded-[13.5px] transition duration-200',
            focusMode === mode.key
              ? 'bg-[#F1F5FF] dark:bg-dark-secondary text-[#0E5FFF]'
              : 'bg-[#F1F1F1] dark:bg-dark-secondary text-[#666666] dark:text-[#999999]',
          )}
        >
          {focusMode === mode.key ? mode.selectedIcon : mode.icon}
          <span className="font-medium inline text-[12px]">
            {mode.title}
          </span>
        </button>
      ))}
    </div>
  );
};

export default Focus;

// 定义 mode 的类型
interface Mode {
  key: string;
  title: string;
  placeholder?: string;
  icon?: React.ReactNode;
  selectedIcon?: React.ReactNode;
  description?: React.ReactNode;
  hidden?: boolean;
}

// 定义 map 的类型
const focusMap: Record<string, Mode> = {}; // 使用 Record 明确键值对类型

(focusModes as Mode[]).forEach((mode) => {
  if ('key' in mode && typeof mode.key === 'string') { // 类型守卫
    focusMap[mode.key] = mode; // 安全赋值
  }
});

export { focusMap };
