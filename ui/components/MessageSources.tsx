/* eslint-disable @next/next/no-img-element */
import { Document } from '@langchain/core/documents';
import { File, ArrowUpRight } from 'lucide-react';
import SafeImage from './SafeImage';
import { useDeviceContext } from './context/DeviceContext';
import { clkLog, cn } from '@/lib/utils';
import { FZ_JsBridge } from '@/lib/js-bridge';

const sourceKnowledgeBase = [
  '管理学经典文献',
  '企业管理实务手册',
  '人力资源管理指南',
  '团队建设最佳实践',
  '组织行为学研究',
  '现代管理理论精要',
]

const MessageSources = ({ sources }: { sources: Document[] }) => {
  console.log("🚀 ~ MessageSources ~ sources:", sources)
  const { channelId } = useDeviceContext() as any;
  const showLink = !['mp', 'mp-kl'].includes(channelId)

  const openSource = (url: string, title: string, source: string) => {
    clkLog('查看来源', { title, url, channelId });
    if (source === 'knowledge_base') { // 知识库来源不显示链接
      return
    }
    // if (channelId == 'mp') {
    //   // 判断url的域名是否在白名单，如果不在，则不打开链接，提示用户在管用APP内打开
    //   const whiteList = ['cyjiaomu', 'foundingaz'];
    //   const domain = new URL(url).hostname;
    //   const legitimateDomain = !!whiteList.find(it => domain.indexOf(it) > -1);
    //   if (legitimateDomain) {
    //     return (window as any).wx.miniProgram.navigateTo({ url: `/pages/webviewnoshare/index?url=${encodeURIComponent(url)}` })
    //   }
    //   return (window as any).wx.miniProgram.navigateTo({ url: `/pages/management-data/index` })
    // }
    if (showLink) {
      if (FZ_JsBridge.isApp) {
        return FZ_JsBridge.goAutoJump('/common/WebDetailActivity', 'FouningazWkWebVC', { url })
      }
      return window.open(url, '_blank');
    }
  }

  return (
    <div className="flex flex-col space-y-3">
      {sources.map((source, i) => (
        <a
          className={cn("flex items-center gap-3 p-2 bg-white/60 rounded-lg hover:bg-white/80 transition-colors cursor-pointer", (showLink && source.metadata.source != 'knowledge_base') && "cursor-pointer hover:underline")} key={i}
          onClick={() => openSource(source.metadata.url, source.metadata.title, source.metadata.source)}
          target="_blank"
          title={source.metadata.title}
        >
          <span className="text-sm text-gray-500 font-medium min-w-[20px]">
            {i + 1}.
          </span>
          {(source.metadata.url === 'File' || source.metadata.source === 'knowledge_base') ? (
            <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0">
              <File size={16} className="text-white" />
            </div>
          ) : (
            <SafeImage
              src={`https://s2.googleusercontent.com/s2/favicons?domain_url=${source.metadata.url}`}
              width={32}
              height={32}
              alt="favicon"
              className={cn("rounded-full h-8 w-8 flex-shrink-0", ['mp', 'mp-kl'].includes(channelId) ? "hidden" : "")}
            />
          )}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-gray-800 truncate">
              {source.metadata.title}
            </h4>
            <p className="text-xs text-gray-500 truncate">
              {sourceKnowledgeBase[i%6]}
            </p>
          </div>
        </a>
      ))}
    </div>
  );
};

export default MessageSources;
