import { Share2 } from 'lucide-react';
import { Message } from './chat/ChatTools';
import { useEffect, useRef, useState } from 'react';
import { formatTimeDifference, getUserText } from '@/lib/utils';
import DeleteChat from './DeleteChat';
import ActionDrawer, { ActionDrawerRef } from './ActionDrawer';
import { useSelectedChat } from './context/SelectedChatContext';
import Battery from './chat/Battery';
import { useSearchParams } from 'next/navigation';

const Navbar = ({ messages, chatId }: { messages: Message[]; chatId: string }) => {
  const query: any = useSearchParams();
  const isShare = useRef(query.get('share') === 'true');
  const [title, setTitle] = useState<string>('');
  const [timeAgo, setTimeAgo] = useState<string>('');
  const actionDrawerRef = useRef<ActionDrawerRef>(null);
  const { fetchChats, setCurrentPage } = useSelectedChat();

  const handleDelete = () => {
    setCurrentPage(1);
    fetchChats();
  }

  const handleShare = () => {
    actionDrawerRef.current?.open()
  }

  useEffect(() => {
    if (messages.length > 0) {
      const newTitle = getUserText(messages[0].content);
      setTitle(newTitle);
      const newTimeAgo = formatTimeDifference(
        new Date(),
        messages[0].createdAt,
      );
      setTimeAgo(newTimeAgo);
    }
  }, [messages]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (messages.length > 0) {
        const newTimeAgo = formatTimeDifference(
          new Date(),
          messages[0].createdAt,
        );
        setTimeAgo(newTimeAgo);
      }
    }, 1000);

    return () => clearInterval(intervalId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-row items-center space-x-4">
      {/* <Battery /> */}
      {chatId && <Share2
        size={17}
        className="active:scale-95 transition duration-100 cursor-pointer"
        onClick={handleShare}
      />}
      {!isShare.current && <DeleteChat chatId={chatId} chats={[]} setChats={handleDelete} />}
      <ActionDrawer ref={actionDrawerRef} chatId={chatId} showExport />
    </div>
  );
};

export default Navbar;
