import React from 'react';
import ReactDOM from 'react-dom';

interface ProgressBarProps {
    stage: number;
    stages?: string[];
    root?: HTMLElement;
}

const ProgressBar = ({ stage, stages = ['开始', '文件上传', '简历分析', '完成'], root }: ProgressBarProps) => {
    const tips = [<>灵通AI正进行分析和优化 <br /> 预计需要3-10分钟，请耐心等待</>];
    const currentStage = stage;

    const progress = (<div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-[3] flex flex-col items-center justify-center">
        <div className="w-full max-w-md px-4 mb-8">
            <div className="flex justify-center mb-6">
                <span className="text-gray-400 text-sm flex items-center text-center">
                    <svg className="animate-spin mr-2 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
                    {tips[0]}
                </span>
            </div>
            <div className="flex justify-between mb-2">
                {stages.map((step, index) => (
                    <div key={index} className="flex flex-col items-center w-1/4">
                        <span className={`text-xs mt-1 transition-all duration-300 ${index === currentStage ? 'text-blue-600 font-bold' : 'text-gray-400'}`}>{step}</span>
                    </div>
                ))}
            </div>
            <div className="relative w-full bg-gray-200 rounded-full h-[6px] mt-[8px] mb-[8px] overflow-visible">
                <div
                    className="bg-[#A1C0FF] h-[6px] rounded-full transition-all duration-500"
                    style={{ width: `${((currentStage + 1) / stages.length) * 100}%` }}
                ></div>
                <div className="absolute top-0 left-0 w-full h-[6px] flex">
                    {stages.map((step, idx) => (
                        <div key={idx} className="flex-1 relative flex items-center justify-center">
                            {idx < stages.length - 1 && (
                                <div className="absolute right-0 top-0 h-full border-r border-gray-300"></div>
                            )}
                            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                                {(idx < currentStage || currentStage === stages.length - 1) ? (
                                    <div className="w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#A1C0FF] border-2 border-[#A1C0FF]">
                                        <img
                                            src="https://static.cyjiaomu.com/ai/completed.png"
                                            alt="已完成"
                                            className="w-[9px] h-[6.5px] object-contain"
                                        />
                                    </div>
                                ) : (
                                    <div className={`w-[16px] h-[16px] rounded-full border-2 flex items-center justify-center transition-all duration-300 ${idx === currentStage ? 'bg-white border-[#2563EB] animate-pulse shadow-lg shadow-blue-200' : 'bg-gray-200 border-gray-300'}`}></div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    </div>)

    if (root) {
        return ReactDOM.createPortal(progress, root);
    }

    return progress;
};

export default ProgressBar;