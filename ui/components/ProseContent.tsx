'use client';

import { marked } from 'marked';
import { MathJ<PERSON>, MathJaxContext } from "better-react-mathjax";
import { cn } from '@/lib/utils';

interface ProseContentProps {
  content: string;
}

const ProseContent = ({ content }: ProseContentProps) => {
  // 直接解析内容，不使用状态管理
  const parsedContent = marked.parse(content) as string;


  const logError: any = (error: Error, info: { componentStack: string }) => {
    // Do something with the error, e.g. log to an external API
  };

  // MathJax configuration
  const mathJaxConfig = {
    tex: {
      inlineMath: [['$', '$'], ['\\(', '\\)']],
      displayMath: [['$$', '$$'], ['\\[', '\\]']],
      processEscapes: true,
    },
    startup: {
      typeset: true,
    }
  };

  if (!content) {
    return null
  }

  return (
    <div className="p-4 rounded-2xl shadow-sm w-fit bg-white/90 backdrop-blur-sm border border-gray-100 text-gray-800 w-full">
      <MathJaxContext config={mathJaxConfig}>
        <MathJax dynamic>
          <div className={
            cn(
              'prose prose-h1:mb-4 prose-h1:text-sm prose-h1:font-[400] prose-h2:mb-3 prose-h2:mt-2 prose-h2:text-sm prose-h2:font-[500] prose-h2:leading-[22px] prose-h3:mt-4 prose-h3:mb-2 prose-h3:text-sm prose-h3:font-[500] prose-h3:leading-[21px] prose-h4:mt-3 prose-h4:mb-2 prose-h4:text-sm prose-h4:font-[500] prose-h4:leading-[18px] prose-h5:mt-2 prose-h5:mb-1 prose-h5:text-sm prose-h5:font-[500] prose-h5:leading-[16px] prose-h6:mt-2 prose-h6:mb-1 prose-h6:text-xs prose-h6:font-[500] prose-h6:leading-[14px] dark:prose-invert prose-p:leading-7 prose-p:my-1 prose-pre:p-0 font-[400]',
              'prose-pre:bg-gray-50 dark:prose-pre:bg-gray-900 prose-pre:!my-0 prose-pre:!p-0 prose-pre:!pl-2 prose-pre:rounded-xl prose-pre:shadow-sm',
              'prose-code:!p-0 prose-code:bg-gray-50 dark:prose-code:bg-gray-900 prose-code:rounded-md prose-code:font-mono prose-code:text-sm',
              'prose-pre:text-black/90 dark:prose-pre:text-white/90 prose-code:text-black/90 dark:prose-code:text-white/90',
              '[&_code]:before:content-none [&_code]:after:content-none',
              'prose-hr:my-0 prose-hr:border-gray-200 dark:prose-hr:border-gray-800',
              'prose-ul:my-1 prose-ul:list-disc prose-ol:my-1 prose-li:my-0',
              'prose-blockquote:my-6 prose-blockquote:pl-4 prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-700 prose-blockquote:italic',
              'prose-img:hidden prose-img + br:hidden',
              'prose-table:my-0 prose-table:w-full prose-table:border-collapse prose-table:border prose-table:border-gray-200 dark:prose-table:border-gray-800 overflow-x-auto',
              'prose-th:p-3 prose-th:border prose-th:border-gray-200 dark:prose-th:border-gray-800 prose-th:bg-gray-50 dark:prose-th:bg-gray-900 prose-th:text-left',
              'prose-td:p-3 prose-td:border prose-td:border-gray-200 dark:prose-td:border-gray-800',
              'max-w-none break-words text-black/90 dark:text-white/90 text-sm leading-relaxed',
              'prose-strong:font-medium prose-strong:text-black/90 dark:prose-strong:text-white/90',
            )
          } dangerouslySetInnerHTML={{ __html: parsedContent }} />
        </MathJax>
      </MathJaxContext>
    </div>
  );
};

export default ProseContent;