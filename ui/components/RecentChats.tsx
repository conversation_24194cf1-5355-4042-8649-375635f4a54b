import React, { useEffect, useRef, useCallback } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { RefreshCcw } from 'lucide-react';
import { withQuery, getUserText, clkLog, formatTimeDifference } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import ChatActions from './ChatActions';
import { Chat, useSelectedChat } from '@/components/context/SelectedChatContext';
import { focusMap } from "@/components/MessageInputActions/Focus";

const RecentChats = () => {
    const { loading, chats, selectedChatId, hasMore, refreshChats, loadNextPage, setSelectedChatId } = useSelectedChat();
    const router = useRouter();
    const containerRef = useRef<HTMLDivElement>(null);

    const gotoChatDetail = (chat: Chat) => {

        setSelectedChatId(chat.id);

        clkLog('首页对话', { "问题标题": getUserText(chat.title) });

        if (chat.task?.agentType === 'resumeAnalyzer' && chat.task.status !== 'completed') {
            router.push(withQuery(`/rp/${chat.id}`, { taskId: chat.task.id }));
        } else {
            router.push(withQuery(`/c/${chat.id}`));
        }
    };

    useEffect(() => {
        const checkAndLoadMore = () => {
            if (containerRef.current && hasMore && !loading) {
                requestAnimationFrame(() => {
                    const { scrollHeight, clientHeight } = containerRef?.current as any || { scrollHeight: 0, clientHeight: 0 };
                    console.log(`RecentChats: scrollHeight=${scrollHeight}, clientHeight=${clientHeight}, hasMore=${hasMore}, loading=${loading}`);
                    if (scrollHeight > 0 && clientHeight > 0 && scrollHeight <= clientHeight) {
                        console.log('RecentChats: Content not filling screen, loading next page.');
                        loadNextPage();
                    } else {
                        console.log('RecentChats: Content fills screen or overflows.');
                    }
                });
            }
        };

        checkAndLoadMore();

        const timeoutId = setTimeout(checkAndLoadMore, 300); // 增加延迟以确保渲染完成

        return () => clearTimeout(timeoutId);
    }, [chats, hasMore, loading, loadNextPage]);

    useEffect(() => {
        // 从地址栏获取chatId
        const pathMatch = window.location.pathname.match(/\/(rp|c)\/([^/?]+)/);
        if (pathMatch && pathMatch[2]) {
            setSelectedChatId(pathMatch[2]);
        }
        return () => {
            setSelectedChatId('');
        };
    }, [])

    if (loading && chats.length === 0) {
        return <div className="text-sm text-black/50 dark:text-white/50 px-2">加载中...</div>;
    }

    if (chats.length === 0) {
        return <div className="text-sm text-black/50 dark:text-white/50 px-2">暂无对话记录</div>;
    }

    return (
        <div className="w-full px-2 h-full overflow-y-auto" ref={containerRef} id="recent-chats-scrollable">
            <div className="flex items-center justify-between pb-1 w-full dark:hover:bg-white/5 duration-150 transition rounded-lg sticky top-0 bg-white dark:bg-dark-primary z-10 group">
                <span className="text-sm text-black/50 dark:text-white/50">历史对话</span>
                <RefreshCcw size={17} className="cursor-pointer text-black/50 dark:text-white/50 opacity-0 group-hover:opacity-100 transition-opacity" onClick={refreshChats} />
            </div>
            <InfiniteScroll
                dataLength={chats.length}
                next={loadNextPage}
                hasMore={hasMore}
                scrollThreshold={0.9}
                scrollableTarget="recent-chats-scrollable"
                loader={loading && <div className="flex justify-center py-4 text-gray-500 dark:text-gray-400">加载中...</div>}
                endMessage={!loading && !hasMore && chats.length > 0 && (
                    <div className="flex justify-center py-4 text-gray-500 dark:text-gray-400"></div>
                )}
            >
                {chats.map((chat, index) => (
                    <div
                        key={`${chat.id}-${index}`}
                        title={getUserText(chat.title)}
                        className={`flex items-center mt-[${index === 0 ? '0px' : '2px'}] py-1 px-2 gap-1 cursor-pointer ${selectedChatId === chat.id ? 'bg-black/5 dark:bg-white/5' : 'hover:bg-black/5 dark:hover:bg-white/5'} duration-150 transition rounded-lg group relative`}
                    >
                        <div className="flex items-center gap-2 flex-1 min-w-0" onClick={() => gotoChatDetail(chat)}>
                            <div className={`flex-shrink-0 w-4 h-4 ${index === 0 ? 'text-black dark:text-white' : 'text-black/70 dark:text-white/70 group-hover:text-black dark:group-hover:text-white'} transition-colors`}>
                                {focusMap[chat.focusMode]?.icon}
                            </div>
                            <span className={`text-sm ${index === 0 ? 'text-black dark:text-white' : 'text-black/90 dark:text-white/90 group-hover:text-black dark:group-hover:text-white'} truncate`}>
                                {getUserText(chat.title)} <span className="text-xs text-black/30 dark:text-white/50">{formatTimeDifference(new Date(), chat.createdAt)}前</span>
                            </span>
                        </div>
                        <div className="transition-opacity duration-200">
                            <ChatActions selectedChatId={selectedChatId} chatId={chat.id} showExport={false} />
                        </div>
                    </div>
                ))}
            </InfiniteScroll>
        </div>
    );
};

export default RecentChats;
