import Marquee from 'react-fast-marquee';

interface RelatedContent {
    title: string;
    thumb_url: string;
    highlight?: {
        name: string;
    };
    keywords?: string;
}

interface RelatedContentSwiperProps {
    relatedContent: RelatedContent[];
    onItemClick: (item: RelatedContent) => void;
}

const RelatedContentSwiper = ({ relatedContent, onItemClick }: RelatedContentSwiperProps) => {
    return (
        <Marquee
            gradient={false}
            speed={40}
            loop={0}
            pauseOnHover={true}
            pauseOnClick={true}
            className="overflow-x-auto hide-scrollbar transition-all duration-300 pb-2"
        >
            <div className="flex gap-2 px-2">
                {relatedContent.map((related, i) => (
                    <div 
                        key={i}
                        className="flex-shrink-0 flex items-stretch"
                    >
                        <div
                            onClick={() => onItemClick(related)}
                            className="group w-40 overflow-hidden border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300 cursor-pointer rounded-xl flex flex-col h-full"
                            title={related.title}
                        >
                            <img
                                src={related.thumb_url}
                                alt={related.title}
                                className="w-full h-24 object-cover group-hover:scale-105 transition-transform duration-300 rounded-t-xl flex-shrink-0"
                            />
                            <div className="p-3 flex flex-col flex-grow">
                                <h4 className="font-semibold text-gray-800 text-sm mb-1 group-hover:text-blue-600 transition-colors leading-snug line-clamp-2">
                                    {related.title}
                                </h4>
                                <div
                                    className="text-xs text-gray-500 line-clamp-2 flex-grow"
                                    dangerouslySetInnerHTML={{ __html: (related?.highlight?.name as any) }}
                                />
                                <div className="hidden sm:flex tags gap-1 mt-2">
                                    {related?.keywords?.trim().split(',').map((it: string, index: number, arr: string[]) => {
                                        if (it?.trim() === '') return null;
                                        if (index < 1 || arr.length <= 2) {
                                            return (
                                                <span
                                                    key={index}
                                                    className="tag inline-block px-1 py-0.5 bg-gray-100 dark:bg-dark-100 text-gray-500 dark:text-gray-300 text-xs rounded-full max-w-[6rem] sm:max-w-[8rem] overflow-hidden text-overflow-ellipsis whitespace-nowrap text-ellipsis"
                                                >
                                                    {it}
                                                </span>
                                            );
                                        } else if (index === 1 && arr.length > 2) {
                                            return (
                                                <span
                                                    key={index}
                                                    className="tag inline-block px-2 py-0.5 bg-gray-100 dark:bg-dark-100 text-gray-500 dark:text-gray-300 text-xs rounded-full cursor-pointer"
                                                    title={arr.slice(2).join(', ')}
                                                >
                                                    +{arr.length - 1}
                                                </span>
                                            );
                                        }
                                        return null;
                                    })}
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </Marquee>
    );
};

export default RelatedContentSwiper;