import { forwardRef, useImperativeHandle, useRef, memo, useEffect } from 'react';

interface RichTextEditorProps {
    value: string;
    onChange?: (value: string) => void;
    onFocus: () => void;
    placeholder?: string;
    className?: string;
}

export interface RichTextEditorRef {
    focus: () => void;
    getContent: () => string;
}

const RichTextEditor = memo(forwardRef<RichTextEditorRef, RichTextEditorProps>((
    {
        value,
        onChange,
        onFocus,
        placeholder = '',
        className = '',
    },
    ref
) => {
    const editorRef: any = useRef<HTMLDivElement>(null);
    const lastContentRef = useRef(value);
    const isInternalChangeRef = useRef(false);
    const debounceTimerRef = useRef<NodeJS.Timeout>();

    useEffect(() => {
        if (!isInternalChangeRef.current && editorRef.current && value !== editorRef.current.innerHTML) {
            editorRef.current.innerHTML = value;
            lastContentRef.current = value;
        }
    }, [value]);

    const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
        const newContentHtml = e.currentTarget.innerHTML || '';
        const newContent = (e.currentTarget.innerText || '').trim();
        if (newContent.length <= 0) {
            editorRef.current.innerHTML = '';
            e.currentTarget.innerHTML = '';
            lastContentRef.current = '';
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
            }
            return onChange?.('');
        }
        if (newContent !== lastContentRef.current) {
            lastContentRef.current = newContent;
            // 立即通知父组件当前内容
            onChange?.(newContentHtml);
            // 使用防抖来优化性能
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
            }
            debounceTimerRef.current = setTimeout(() => {
                isInternalChangeRef.current = true;
                // 再次确保内容同步
                onChange?.(newContentHtml);
                isInternalChangeRef.current = false;
            }, 300);
        }
    };

    useImperativeHandle(ref, () => ({
        focus: () => editorRef.current?.focus(),
        getContent: () => editorRef.current?.textContent || ''
    }));

    return (
        <div
            ref={editorRef}
            contentEditable
            onFocus={onFocus}
            className={`break-all bg-transparent placeholder:text-black/10 dark:placeholder:text-white/10 text-sm text-black dark:text-white focus:outline-none w-full max-h-[calc(100vh*0.2)] min-h-[3rem] h-auto whitespace-pre-wrap empty:before:content-[attr(data-placeholder)] empty:before:text-[#B1B1B1] dark:empty:before:text-white/50 before:text-[#4D6BFE] dark:before:text-[#4D6BFE] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent ${className}`}
            data-placeholder={placeholder}
            onInput={handleInput}
        />
    );
}));

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;