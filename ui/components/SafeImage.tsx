'use client';

import { useState } from 'react';

interface SafeImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {}

const SafeImage = ({ src, alt, className, ...props }: SafeImageProps) => {
  const [hasError, setHasError] = useState(false);

  if (hasError) {
    return <></>;
  }

  return (
    <img
      src={src}
      alt={alt}
      className={className}
      onError={() => setHasError(true)}
      {...props}
    />
  );
};

export default SafeImage;