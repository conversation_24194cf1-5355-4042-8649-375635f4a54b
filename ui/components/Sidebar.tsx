'use client';

import { cn, getLocalItem, withQuery } from '@/lib/utils';
import { useSelectedLayoutSegments, usePathname, useRouter } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import Layout from './Layout';
import SettingsDialog from './SettingsDialog';
import SidebarContent from './SidebarContent';
import { useExpanded } from './context/ExpandedContext';
import { useSelectedChat } from './context/SelectedChatContext';

const Sidebar = ({ children }: { children: React.ReactNode }) => {
  const segments = useSelectedLayoutSegments();
  const pathname = usePathname();
  const isHomepage = pathname === '/';
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [userDetail, setUserDetail] = useState<any>(null);
  const { isExpanded } = useExpanded();
  const { setSelectedChatId } = useSelectedChat();

  const router = useRouter();

  useEffect(() => {
    try {
      const userDetailStr = getLocalItem('__user_detail__');
      if (userDetailStr) {
        const userData = JSON.parse(userDetailStr);
        setUserDetail(userData.data?.detail || null);
      }
    } catch (error) {
      console.error('获取用户信息失败', error);
    }
  }, []);



  const navLinks: any = [
    // {
    //   icon: MessageSquare,
    //   href: '/',
    //   active: segments.length === 0 || segments.includes('c'),
    //   label: '主页',
    // },
    // {
    //   icon: Search,
    //   href: '/discover',
    //   active: segments.includes('discover'),
    //   label: 'Discover',
    // },
    // {
    //   icon: History,
    //   href: '/library',
    //   active: segments.includes('library'),
    //   label: '对话记录',
    // },
  ];



  const creatNewChat = () => {
    setSelectedChatId('');
    router.push(withQuery('/', { refresh: `${Math.random()}` }, ['source', 'taskId']));
  };

  const resume = () => {
    setSelectedChatId('');
    router.push(withQuery('/resume', {}));
  };

  const showAvaar = isExpanded && userDetail

  return (
    <div className="flex flex-col w-full stretch">
      <div className="flex min-h-screen w-full">
        <SidebarContent
          isHomepage={isHomepage}
          showAvaar={showAvaar}
          userDetail={userDetail}
          navLinks={navLinks}
          creatNewChat={creatNewChat}
          setIsSettingsOpen={setIsSettingsOpen}
          resume={resume}
        />
        <div id="mainLayout" className={cn("w-full lg:w-unset lg:fixed lg:top-0 lg:bottom-0 lg:right-0 lg:overflow-y-auto flex-1 lg:flex lg:items-center lg:z-[2]", isExpanded ? 'lg:left-[280px] lg:w-[unset]' : '')}>
          <Layout isExpanded={isExpanded}>{children}</Layout>
        </div>
      </div>

      <SettingsDialog
        isOpen={isSettingsOpen}
        setIsOpen={setIsSettingsOpen}
      />
    </div>
  );
};

export default Sidebar;
