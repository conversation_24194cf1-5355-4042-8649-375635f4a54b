'use client';

import { cn } from '@/lib/utils';
import { Plus, Settings, PanelRightClose } from 'lucide-react';
import RecentChats from './RecentChats';
import { useExpanded } from './context/ExpandedContext';
import { focusMap } from './MessageInputActions/Focus';
import Avatar from '@/components/chat/Avatar';
import { useSelectedChat } from './context/SelectedChatContext';

interface SidebarContentProps {
  isHomepage: boolean;
  showAvaar: boolean;
  userDetail: any;
  navLinks: any[];
  creatNewChat: () => void;
  resume: () => void;
  setIsSettingsOpen: (isOpen: boolean) => void;
}

const VerticalIconContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className={`flex flex-col items-center gap-y-3 w-full flex-1 max-h-[calc(100vh-100px)] overflow-hidden`}>{children}</div>
  );
};

const SidebarContent: React.FC<SidebarContentProps> = ({
  isHomepage,
  showAvaar,
  userDetail,
  creatNewChat,
  setIsSettingsOpen,
  resume
}) => {
  const { isExpanded, setIsExpanded } = useExpanded();
  const { chatBattery: { battery: { availableTasks = [] } = {} } = {} }: any = useSelectedChat();
  const showResmue = availableTasks.includes('resumeAnalyzer');
  return (
    <div className={cn("fixed left-0 top-0 border-r border-light-200 dark:border-dark-200 z-[3]", (isHomepage && !isExpanded) ? "lg:border-[0]" : "")} >
      <div
        className={cn(
          "hidden lg:inline-flex gap-4 z-[41] p-2 dark:bg-black items-center",
          isExpanded ? "justify-between w-[280px]" : ""
        )}
      >
        {showAvaar && (<Avatar />)}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-1 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
        >
          <PanelRightClose
            className={cn(
              "w-5 h-5 transition-transform",
              isExpanded ? "rotate-180" : ""
            )}
          />
        </button>
        {!showAvaar && (
          <button
            onClick={creatNewChat}
            className={cn(
              "flex items-center rounded-full bg-clip-padding text-[#4D6BFE] active:scale-95 duration-150 transition relative border-2 px-2 py-1 border-[#4D6BFE] cursor-pointer",
            )}
          >
            <Plus className="cursor-pointer" size={12} />
            <span className="ml-2 text-sm">新对话</span>
          </button>
        )}
      </div>
      {isExpanded && (
        <div
          className={cn(
            "hidden lg:flex flex-col h-[100vh] dark:bg-black transition-all duration-300 w-[280px]"
          )}
        >
          <div className="flex flex-col h-[calc(100vh-48px)]">
            <div className="flex flex-col items-center w-full h-full">
              {userDetail && (
                <>
                  <button
                    onClick={creatNewChat}
                    className={cn(
                      "flex items-center w-full px-2 cursor-pointer hover:bg-black/5 dark:hover:bg-white/5 duration-150 transition w-full py-2 px-2 rounded-lg"
                    )}
                  >
                    <Plus className="cursor-pointer" size={17} />
                    <span className="ml-1 text-base">新对话</span>
                  </button>
                  {showResmue && <div className="flex flex-col items-center gap-y-4 w-full">
                    <button
                      onClick={resume}
                      className="flex items-center w-full hover:bg-black/5 dark:hover:bg-white/5 duration-150 transition py-2 px-2 rounded-lg"
                    >
                      {focusMap['resumeAnalyzer'].selectedIcon}
                      <span className="ml-1 text-base">简历分析</span>
                    </button>
                  </div>}
                  <div className="flex flex-col items-center gap-y-4 w-full">
                    <button
                      onClick={() => setIsSettingsOpen(true)}
                      className="flex items-center w-full hover:bg-black/5 dark:hover:bg-white/5 duration-150 transition py-2 px-2 rounded-lg"
                    >
                      <Settings size={17} />
                      <span className="ml-1 text-base">设置</span>
                    </button>
                  </div>
                  <hr className="w-full border-t border-light-200 dark:border-dark-200 my-2" />
                  <VerticalIconContainer>
                    <RecentChats />
                  </VerticalIconContainer>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SidebarContent;