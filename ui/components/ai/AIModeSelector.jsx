
import React from 'react';
import { motion } from 'framer-motion';
import { Brain, Search, Globe, Sparkles } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const aiModes = [
    {
        id: 'management',
        name: '管理大师',
        icon: Brain,
        description: '专业管理咨询',
        color: 'from-blue-500 to-indigo-600',
        bgColor: 'bg-blue-50',
        textColor: 'text-blue-700'
    },
    {
        id: 'search',
        name: '全网搜索',
        icon: Search,
        description: '实时信息查询',
        color: 'from-purple-500 to-pink-600',
        bgColor: 'bg-purple-50',
        textColor: 'text-purple-700'
    },
    {
        id: 'general',
        name: '通用助手',
        icon: Globe,
        description: '全能AI助手',
        color: 'from-green-500 to-teal-600',
        bgColor: 'bg-green-50',
        textColor: 'text-green-700'
    }
];

export default function AIModeSelector({ selectedMode, onModeChange }) {
    return (
        <div>
            <h3 className="text-lg font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-yellow-500" />
                AI助手模式
            </h3>
            <div className="flex flex-wrap gap-2">
                {aiModes.map((mode) => (
                    <motion.button
                        key={mode.id}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => onModeChange(mode.id)}
                        className={`relative overflow-hidden rounded-xl p-3 border-2 transition-all duration-300 ${
                            selectedMode === mode.id 
                                ? `border-transparent bg-gradient-to-r ${mode.color} text-white shadow-lg` 
                                : `border-gray-200 ${mode.bgColor} hover:border-gray-300`
                        }`}
                    >
                        <div className="flex items-center gap-2">
                            <mode.icon className={`w-5 h-5 ${selectedMode === mode.id ? 'text-white' : mode.textColor}`} />
                            <div className="text-left">
                                <p className={`font-medium text-sm ${selectedMode === mode.id ? 'text-white' : 'text-gray-800'}`}>
                                    {mode.name}
                                </p>
                                {/* The description is now always visible to make each mode a clearer "portal" */}
                                <p className={`text-xs ${selectedMode === mode.id ? 'text-white/80' : 'text-gray-500'}`}>
                                    {mode.description}
                                </p>
                            </div>
                        </div>
                        {selectedMode === mode.id && (
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-1 right-1"
                            >
                                <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs px-1 py-0.5">
                                    已选
                                </Badge>
                            </motion.div>
                        )}
                    </motion.button>
                ))}
            </div>
        </div>
    );
}
