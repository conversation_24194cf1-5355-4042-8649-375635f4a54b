
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Loader2, Mic, Paperclip } from 'lucide-react';

export default function ChatInput({ onSendMessage, isLoading }) {
    const [message, setMessage] = useState('');

    const handleSubmit = (e) => {
        e.preventDefault();
        if (message.trim() && !isLoading) {
            onSendMessage(message.trim());
            setMessage('');
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };

    return (
        <motion.form
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            onSubmit={handleSubmit}
            className="bg-white/80 backdrop-blur-sm border-t border-gray-100 p-4"
        >
            <div className="max-w-4xl mx-auto">
                <div className="relative flex items-end gap-3 bg-white rounded-2xl border border-gray-200 shadow-sm p-3">
                    <div className="flex-1 relative">
                        <Textarea
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder="输入您的问题..."
                            className="min-h-[44px] max-h-32 resize-none border-0 focus:ring-0 bg-transparent"
                            disabled={isLoading}
                        />
                    </div>
                    
                    <div className="flex items-center gap-2">
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <Paperclip className="w-4 h-4" />
                        </Button>
                        
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <Mic className="w-4 h-4" />
                        </Button>
                        
                        <Button
                            type="submit"
                            disabled={!message.trim() || isLoading}
                            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl px-4 py-2 shadow-md"
                        >
                            {isLoading ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                                <Send className="w-4 h-4" />
                            )}
                        </Button>
                    </div>
                </div>
                
                <p className="text-xs text-gray-400 text-center mt-2">
                    内容由AI生成，仅供参考
                </p>
            </div>
        </motion.form>
    );
}
