
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input'; // Import Input component
import { Sparkles, Send } from 'lucide-react'; // Import Sparkles for AI icon, Send for button icon

export default function AIChatPortal() { // Renamed the component to AIChatPortal
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 p-6 text-white shadow-xl"
        >
            {/* Background decorative elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12" />
            
            <div className="relative z-10 flex flex-col h-full"> {/* Added flex-col and h-full for layout */}
                <div className="flex items-center gap-2 mb-3">
                    <Sparkles className="w-5 h-5" /> {/* Changed icon to Sparkles for AI theme */}
                    <span className="font-semibold">AI智能助手</span> {/* Updated title to reflect AI Q&A */}
                    <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                        智能
                    </Badge> {/* Updated badge text */}
                </div>
                
                <p className="text-white/90 text-sm mb-4 leading-relaxed flex-grow"> {/* Added flex-grow to push input to bottom */}
                    与AI进行智能对话，获取即时解答和创意灵感。输入你的问题，开始探索！
                </p>
                
                {/* AI Q&A input and send button */}
                <div className="flex items-center gap-2 mt-auto"> {/* mt-auto to push content to the bottom */}
                    <Input 
                        placeholder="向AI提问..." 
                        className="flex-grow bg-white/20 text-white placeholder:text-white/70 border-white/30 focus-visible:ring-offset-0 focus-visible:ring-transparent" 
                    />
                    <Button 
                        variant="secondary" 
                        size="icon" // Changed size to 'icon' for a square button
                        className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm"
                    >
                        <Send className="w-4 h-4" /> {/* Send icon for the button */}
                    </Button>
                </div>
                {/* The rotating Sparkles element from the original design is removed as it doesn't fit the new interactive layout */}
            </div>
        </motion.div>
    );
}
