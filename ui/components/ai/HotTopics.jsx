import React from 'react';
import { Check } from 'lucide-react';
import <PERSON>quee from 'react-fast-marquee';

const TopicButton = ({ topic, onClick }) => (
    <button
        onClick={() => onClick(topic)}
        className="flex-shrink-0 flex items-center gap-2 pl-3 pr-4 py-1.5 rounded-full border border-gray-100 bg-gray-50/80 hover:bg-white hover:border-gray-200 hover:shadow-sm transition-all duration-200"
    >
        {topic.read && <div className="w-4 h-4 bg-gray-100 text-gray-400 rounded-full flex items-center justify-center"><Check className="w-3 h-3"/></div>}
        <span className={`text-xs ${topic.chatId ? 'text-gray-400' : 'text-gray-600'}`}>
            {topic.title}
        </span>
    </button>
);

export default function HotTopics({ questions, onTopicClick }) {
    const half = Math.ceil(questions.length / 2);
    const firstRow = questions.slice(0, half);
    const secondRow = questions.slice(half);

    return (
        <div className="w-full space-y-1.5 opacity-80">
            <Marquee
                gradient={false}
                speed={20}
                pauseOnHover={true}
                className="overflow-y-hidden !overflow-x-auto [&::-webkit-scrollbar]:hidden"
            >
                <div className="flex gap-2 px-1">
                    {firstRow.map((topic, index) => (
                        <div key={`${topic.id}-${index}`} className="mx-1">
                            <TopicButton topic={topic} onClick={onTopicClick} />
                        </div>
                    ))}
                </div>
            </Marquee>
            <Marquee
                gradient={false}
                speed={20}
                pauseOnHover={true}
                direction="right"
                className="overflow-y-hidden !overflow-x-auto [&::-webkit-scrollbar]:hidden"
            >
                <div className="flex gap-2 px-1">
                    {secondRow.map((topic, index) => (
                        <div key={`${topic.id}-${index}`} className="mx-1">
                            <TopicButton topic={topic} onClick={onTopicClick} />
                        </div>
                    ))}
                </div>
            </Marquee>
        </div>
    );
}