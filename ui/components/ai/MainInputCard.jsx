
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { BrainCircuit, Globe, Send, MessageSquare, Sparkles } from 'lucide-react';
import { focusModes } from '../MessageInputActions/Focus';
import { useSelectedChat } from '../context/SelectedChatContext';
import { clkLog } from '@/lib/utils';
import { useDeviceContext } from '../context/DeviceContext';
import { toast } from 'sonner';
import { useChatContext } from '../context/ChatContext';

const batteryImage = 'https://static.cyjiaomu.com/ai/bean.png';

export default function MainInputCard({ focusMode, setFocusMode, onSendMessage, isLoading }) {
    const [message, setMessage] = useState('');
    const { fetchChats, setCurrentPage } = useSelectedChat();
    const { channelId } = useDeviceContext();
    const [isFocused, setIsFocused] = useState(false);
    const [isSmallScreen, setIsSmallScreen] = useState(false);
    const [highlightInput, setHighlightInput] = useState(false);

    const { chatBattery: { battery = {} } = {} } = useChatContext();
    const costBeans = battery.taskPoints[focusMode] || "限免";

    useEffect(() => {
        const checkScreenSize = () => {
            setIsSmallScreen(window.innerWidth < 375);
        };
        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
        return () => window.removeEventListener('resize', checkScreenSize);
    }, []);

    const placeholderText = focusModes.find((it) => it.key === focusMode)?.placeholder || '输入您的问题...';

    // 光标动画样式
    const cursorKeyframes = `
        @keyframes cursor-blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        .cursor-animation::after {
            content: '|';
            animation: cursor-blink 1s infinite;
            color: #3b82f6;
            font-weight: bold;
            margin-left: 2px;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        .animate-shake {
            animation: shake 0.5s ease-in-out;
        }
    `;

    const handleSendMessage = (e) => {
        e.preventDefault();
        if (isLoading) return; // Move isLoading check here
        const messageText = message.trim();
        if (!messageText) {
            toast('请输入问题后再发送哦~');
            // Force re-animation by toggling the state
            setHighlightInput(false); // Reset first
            setTimeout(() => {
                setHighlightInput(true);
                setTimeout(() => setHighlightInput(false), 1000); // Reset after 0.5s
            }, 50); // Small delay to ensure class removal before re-adding
            return;
        };
        onSendMessage(messageText);
        clkLog("发送提问", { "问题内容": messageText, channelId });

        setTimeout(() => {
            setCurrentPage(1);
            fetchChats();
        }, 2000);
    };

    return (
        <>
            <style>{cursorKeyframes}</style>
            <motion.div
                initial={{ opacity: 0, scale: 0.95, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.5, ease: 'easeOut' }}
                className="w-full bg-gradient-to-br from-blue-50 to-cyan-50 border-2 border-blue-400 rounded-2xl shadow-xl p-5 relative overflow-hidden flex"
            >
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-cyan-400/10 rounded-full -translate-y-16 translate-x-16" />
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-400/10 to-cyan-400/10 rounded-full translate-y-12 -translate-x-12" />

                <div className="relative z-10 w-full">
                    {/* 引导文案 - 更显眼 */}
                    <div className="flex items-center gap-3 mb-4">
                        <div className="flex items-center gap-2">
                            <div className="p-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 shadow-md">
                                <MessageSquare className="w-5 h-5 text-white" />
                            </div>
                            <div>
                                <h3 className="text-lg font-bold text-gray-800 flex items-center gap-2">
                                    向AI提问
                                    <Sparkles className="w-4 h-4 text-blue-500" />
                                </h3>
                                <p className="text-sm text-blue-600 font-medium">说出你的管理困惑，让AI为你答疑解惑</p>
                            </div>
                        </div>
                    </div>
                    <div className="relative">
                        <Textarea
                            value={message}
                            onChange={(e) => {
                                setMessage(e.target.value);
                                setHighlightInput(false); // Reset highlight on change
                            }}
                            onFocus={() => {
                                setIsFocused(true);
                                setHighlightInput(false); // Reset highlight on focus
                            }}
                            onBlur={() => setIsFocused(false)}

                            onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                    handleSendMessage(e);
                                }
                            }}
                            placeholder={placeholderText}
                            className={`bg-white/90 border-2 rounded-xl text-sm p-4 focus-visible:ring-1 focus-visible:ring-blue-400 focus-visible:border-blue-400 resize-none min-h-[90px] placeholder:text-gray-400 placeholder:leading-relaxed transition-all shadow-sm ${highlightInput ? 'border-red-300 ring-1 ring-red-300 animate-shake' : 'border-blue-400'}`}

                        />
                        {/* 光标动画 - 只在没有焦点且没有内容时显示 */}
                        {!isFocused && !message && (
                            <div className="absolute top-4 left-4 pointer-events-none">
                                <span className="text-gray-400 cursor-animation"></span>
                            </div>
                        )}
                    </div>

                    <div className="flex justify-between items-center gap-2 mt-3">
                        <div className={`flex items-center ${isSmallScreen ? 'gap-1' : 'gap-2'}`}>
                            <Button
                                size="sm"
                                onClick={() => setFocusMode('managementMaster')}
                                className={`
                                    rounded-full transition-all duration-300 text-xs h-auto border-none flex-shrink-0
                                    flex items-center justify-center py-1.5
                                    ${focusMode === 'managementMaster'
                                        ? 'bg-blue-500 text-white shadow-md w-auto px-2'
                                        : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                                    }
                                    ${isSmallScreen && focusMode !== 'managementMaster'
                                        ? 'w-8 px-0 overflow-hidden' // Collapsed state: fixed width, no horizontal padding, hide overflow
                                        : 'w-auto px-2' // Expanded state: default horizontal padding
                                    }
                                `}
                            >
                                <BrainCircuit className={`w-3 h-3 ${(!isSmallScreen || focusMode === 'managementMaster') ? 'mr-0.5' : ''}`} />
                                {(!isSmallScreen || focusMode === 'managementMaster') && '管理大师'}
                            </Button>
                            <Button
                                size="sm"
                                onClick={() => setFocusMode('webSearch')}
                                className={`
                                    rounded-full transition-all duration-300 text-xs h-auto border-none flex-shrink-0
                                    flex items-center justify-center py-1.5
                                    ${focusMode === 'webSearch'
                                        ? 'bg-blue-500 text-white shadow-md w-auto px-2'
                                        : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                                    }
                                    ${isSmallScreen && focusMode !== 'webSearch'
                                        ? 'w-8 px-0 overflow-hidden' // Collapsed state: fixed width, no horizontal padding, hide overflow
                                        : 'w-auto px-2' // Expanded state: default horizontal padding
                                    }
                                `}
                            >
                                <Globe className={`w-3 h-3 ${(!isSmallScreen || focusMode === 'webSearch') ? 'mr-0.5' : ''}`} />
                                {(!isSmallScreen || focusMode === 'webSearch') && '全网搜索'}
                            </Button>
                        </div>
                        <Button
                            onClick={handleSendMessage}
                            className={`bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-2 h-auto text-sm shadow-lg transition-all hover:shadow-xl flex-shrink-0 flex items-center gap-1 ${isLoading || !message.trim() ? 'opacity-50 cursor-not-allowed' : 'hover:from-blue-600 hover:to-cyan-600'}`}
                        >
                            <img className="h-3.5" src={batteryImage} />
                            <span className="font-semibold">{costBeans}</span>
                            <div className="w-px h-4 bg-white/30"></div>
                            <Send className="w-3.5 h-3.5" />
                        </Button>
                    </div>
                </div>
            </motion.div>
        </>
    );
}
