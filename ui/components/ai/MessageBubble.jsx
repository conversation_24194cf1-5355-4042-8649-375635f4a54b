import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { format } from 'date-fns';

export default function MessageBubble({ message, isUser }) {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className={`flex gap-4 mb-6 ${isUser ? 'justify-end' : 'justify-start'}`}
        >
            {!isUser && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                    <Bot className="w-5 h-5 text-white" />
                </div>
            )}
            
            <div className={`max-w-[70%] ${isUser ? 'order-first' : ''}`}>
                <motion.div
                    className={`p-4 rounded-2xl shadow-sm ${
                        isUser 
                            ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white ml-auto' 
                            : 'bg-white/80 backdrop-blur-sm border border-gray-100 text-gray-800'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                >
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                        {message.content}
                    </p>
                </motion.div>
                <p className={`text-xs text-gray-400 mt-2 ${isUser ? 'text-right' : 'text-left'}`}>
                    {format(new Date(message.timestamp), 'HH:mm')}
                </p>
            </div>

            {isUser && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center shadow-lg">
                    <User className="w-5 h-5 text-white" />
                </div>
            )}
        </motion.div>
    );
}