import React from 'react';
import { Clock, User, Zap, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useChatContext } from '../context/ChatContext';
import { toast } from 'sonner';
import { jmWx } from '@/lib/wx';
import { getLocalItem, win } from '@/lib/utils';
import { useDeviceContext } from '../context/DeviceContext';

const batteryImage = 'https://static.cyjiaomu.com/ai/bean.png';

export default function PortalHeader({ greeting, useName, goHistory }) {
    const { chatBattery: { battery = {} } = {} } = useChatContext();
    const { channelId, query } = useDeviceContext();
    console.log("🚀 ~ battery:", battery)
    const payMethod = query.get('payMethod')
    const points = +(battery.remainingPoints || 0);
    const alarmPoints = 100;
    const showAlarm = points < alarmPoints;
    let showBattery = battery?.available || false;
    const isIos = /(iPhone|iPod|iPad|Macintosh|MacIntel|MacPPC|Mac68K)/i.test(navigator.userAgent);
    if (isIos && payMethod === 'wxh5') {
        showBattery = false;
        console.log("🚀 ~ PortalHeader ~ showBattery:", showBattery)
    }
    const { isVip, avatar = 'https://static.cyjiaomu.com/mp-mskl/head.png', name } = getLocalItem('__user_detail__', true)?.data?.detail || {};

    const handleUpgradeBean = async () => {
        console.log("🚀 ~ handleUpgradeClick ~ showBattery:", showBattery)
        if(!showBattery) return;
        try { toast.dismiss(t.id); } catch (error) { }
        if (['mp-kl'].includes(channelId)) {
            jmWx.navigateTo({ url: `/pages/training/order/member-center/memberCenter?tabIndex=1` });
            return;
        }
        if (['gy-ios', 'gy-android', 'mp'].includes(channelId)) {
            toast('建设中，敬请期待...')
            return
        }
        // 通过h5方案打开微信小程序
        win.open(`weixin://dl/business/?action=openMiniProgram&username=gh_535178aa25fe&appid=wx03ab06399e1b6616&path=pages/training/order/member-center/memberCenter&query=tabIndex%3D1`)
    };

    const handleUpgradeClick = async () => {
        console.log("🚀 ~ handleUpgradeClick ~ showBattery:", showBattery)
        if(!showBattery) return;
        try { toast.dismiss(t.id); } catch (error) { }
        if (['mp-kl'].includes(channelId)) {
            jmWx.navigateTo({ url: `/pages/training/order/member-center/memberCenter` });
            return;
        }
        if (['gy-ios', 'gy-android', 'mp'].includes(channelId)) {
            toast('建设中，敬请期待...')
            return
        }
        // 通过h5方案打开微信小程序
        win.open(`weixin://dl/business/?action=openMiniProgram&username=gh_535178aa25fe&appid=wx03ab06399e1b6616&path=pages/training/order/member-center/memberCenter`)
    };

    return (
        <div className="flex justify-between items-center w-full max-w-2xl mx-auto px-4 pt-6">
            <div className="flex items-center gap-4">
                <div className="relative" onClick={handleUpgradeClick}>
                    <div className="w-11 h-11 rounded-full bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center shadow-md">
                        <img
                            src={avatar || 'https://static.cyjiaomu.com/mp-mskl/head.png'}
                            alt="avatar"
                            className="w-full h-full text-white object-contain rounded-full"
                        />
                    </div>
                    {/* VIP标识 */}
                    {isVip && (
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 flex items-center justify-center shadow-sm">
                            <Crown className="w-3 h-3 text-white" />
                        </div>
                    )}
                </div>
                <div>
                    <h1 className="text-xl font-semibold text-blue-700 tracking-wide mb-1.5" onClick={handleUpgradeClick}>
                        {greeting}，{useName}
                    </h1>
                    {showBattery && (
                        <div className="flex items-center gap-2">
                            {/* 灵豆数量 */}
                            <div className="flex items-center gap-1 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-full px-2.5 py-1" onClick={handleUpgradeBean}>
                                <img className="w-3.5 h-3.5 text-amber-500 fill-amber-500" src={batteryImage} />
                                <span className={`font-semibold text-amber-700 text-xs ${showAlarm ? 'text-red-500' : ''}`}>{points}</span>
                            </div>
                            {/* 升级按钮 */}
                            <Button
                                onClick={handleUpgradeClick}
                                variant="outline"
                                size="sm"
                                className="h-6 px-2 text-xs bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200 text-amber-700 hover:bg-gradient-to-r hover:from-amber-100 hover:to-orange-100 hover:border-amber-300"
                            >
                                <Crown className="w-3 h-3 mr-1 text-amber-500" />
                                升级
                            </Button>
                        </div>
                    )}
                </div>
            </div>
            <button className="p-2.5 rounded-full hover:bg-gray-100/80 transition-colors" onClick={goHistory}>
                <Clock className="w-5 h-5 text-gray-500" />
            </button>
        </div>
    );
}