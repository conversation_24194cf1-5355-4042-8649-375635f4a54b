
import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, MessageCircle, Users, TrendingUp, Send } from 'lucide-react';

const suggestions = [
    {
        id: 1,
        text: "如何代表团队要资源？",
        icon: Users,
        completed: false
    },
    {
        id: 2,
        text: "HR不懂业务，如何做业务",
        icon: TrendingUp,
        completed: true
    },
    {
        id: 3,
        text: "为什么留不住新员工？",
        icon: MessageCircle,
        completed: true
    },
    {
        id: 4,
        text: "一个好的绩效管理流程",
        icon: CheckCircle,
        completed: true
    }
];

export default function QuickSuggestions({ onSuggestionClick, onQuerySubmit }) {
    const [query, setQuery] = useState('');
    const inputRef = useRef(null);

    const handleQuerySubmit = () => {
        if (query.trim()) {
            onQuerySubmit(query.trim());
            setQuery(''); // Clear input after submission
            inputRef.current?.focus(); // Keep focus on input
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) { // Submit on Enter, but allow Shift+Enter for new line in textarea
            e.preventDefault(); // Prevent default new line
            handleQuerySubmit();
        }
    };

    const handleSuggestionClick = (text) => {
        // This directly triggers the action for the suggestion,
        // similar to the original component's behavior.
        onSuggestionClick(text);
        setQuery(''); // Clear the main query input if a suggestion is clicked
    };

    return (
        <div className="h-full flex flex-col">
            {/* Main AI Q&A Input Area */}
            <div className="bg-white/80 backdrop-blur-md rounded-xl p-6 mb-4 shadow-lg border border-gray-100 flex-shrink-0">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                    <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                        <MessageCircle className="w-6 h-6 text-blue-500" />
                    </motion.div>
                    AI 助手
                </h3>
                <div className="flex gap-3">
                    <textarea
                        ref={inputRef}
                        className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none text-gray-800 text-sm"
                        placeholder="向AI提问，例如：如何有效激励团队？"
                        rows={3}
                        value={query}
                        onChange={(e) => setQuery(e.target.value)}
                        onKeyPress={handleKeyPress}
                    />
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleQuerySubmit}
                        disabled={!query.trim()}
                        className="flex items-center justify-center p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <Send className="w-5 h-5" />
                    </motion.button>
                </div>
            </div>

            {/* Quick Suggestions Section */}
            <div className="flex-1 overflow-y-auto">
                <h3 className="text-lg font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <MessageCircle className="w-5 h-5 text-blue-500" />
                    快速开始
                </h3>
                <div className="grid grid-cols-2 gap-2">
                    {suggestions.map((suggestion, index) => (
                        <motion.button
                            key={suggestion.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleSuggestionClick(suggestion.text)}
                            className="flex items-center gap-2 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-100 hover:bg-white/80 hover:shadow-md transition-all duration-300 text-left"
                        >
                            <div className={`p-2 rounded-lg ${suggestion.completed ? 'bg-green-100' : 'bg-blue-100'}`}>
                                <suggestion.icon className={`w-4 h-4 ${suggestion.completed ? 'text-green-600' : 'text-blue-600'}`} />
                            </div>
                            <div className="flex-1">
                                <p className="text-xs font-medium text-gray-800">{suggestion.text}</p>
                                {suggestion.completed && (
                                    <div className="flex items-center gap-1 mt-1">
                                        <CheckCircle className="w-3 h-3 text-green-500" />
                                        <span className="text-xs text-green-600">已读</span>
                                    </div>
                                )}
                            </div>
                        </motion.button>
                    ))}
                </div>
            </div>
        </div>
    );
}
