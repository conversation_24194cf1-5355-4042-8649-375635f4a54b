import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';
import { withQuery } from '@/lib/utils';

export default function ResumeAnalysisCard() {
    const router = useRouter();
    const gotoResumeAnalysis = () => {
        router.push(withQuery('/resume', {}));
    };
    return (
        <div onClick={gotoResumeAnalysis} className="w-full max-w-xl block">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5, ease: 'easeOut' }}
                className="w-full p-5 rounded-2xl bg-gradient-to-tr from-indigo-400 to-cyan-400 relative overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
            >
                <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full" />
                <div className="relative z-10 flex justify-between items-center">
                    <div>
                        <h3 className="text-lg font-bold text-white flex items-center gap-2">
                            简历分析 <Badge className="rounded-full bg-red-500 text-white border-none shadow text-xs">new</Badge>
                        </h3>
                        <p className="text-white/80 text-sm mt-1">结合岗位需求 优化简历内容</p>
                    </div>
                    <Button className="bg-white/20 hover:bg-white/30 text-white rounded-full shadow-md backdrop-blur-sm border border-white/30 text-sm px-4 py-2">
                        去试试
                    </Button>
                </div>
            </motion.div>
        </div>
    );
}