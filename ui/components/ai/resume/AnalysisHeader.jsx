import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { createPageUrl } from '@/utils';
import { ArrowLeft, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function AnalysisHeader() {
    return (
        <header className="w-full max-w-4xl mx-auto px-4 py-4 flex justify-between items-center">
            <Link to={createPageUrl('AIChat')}>
                <Button variant="ghost" className="flex items-center gap-2 text-gray-600">
                    <ArrowLeft className="w-5 h-5" />
                    <span className="hidden sm:inline">简历分析</span>
                </Button>
            </Link>
            <Link to="#">
                <Button variant="ghost" className="flex items-center gap-2 text-blue-600">
                    <FileText className="w-5 h-5" />
                    我的简历
                </Button>
            </Link>
        </header>
    );
}