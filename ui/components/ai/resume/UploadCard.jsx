
import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Upload, PenSquare, Image as ImageIcon, File as FileIcon, X, History } from 'lucide-react';

export default function UploadCard({ title, onFileChange, onTextChange, placeholder }) {
    const [mode, setMode] = useState('file');
    const [file, setFile] = useState(null);
    const [text, setText] = useState('');
    const [isDragging, setIsDragging] = useState(false);
    const fileInputRef = useRef(null);

    const handleFileSelect = (selectedFile) => {
        if (selectedFile) {
            setFile(selectedFile);
            onFileChange(selectedFile);
        }
    };

    const handleTextUpdate = (e) => {
        setText(e.target.value);
        onTextChange(e.target.value);
    };
    
    const handleDragEnter = (e) => { e.preventDefault(); e.stopPropagation(); setIsDragging(true); };
    const handleDragLeave = (e) => { e.preventDefault(); e.stopPropagation(); setIsDragging(false); };
    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileSelect(e.dataTransfer.files[0]);
        }
    };
    
    const clearFile = () => {
        setFile(null);
        onFileChange(null);
        if(fileInputRef.current) fileInputRef.current.value = "";
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="w-full h-full bg-white rounded-xl border border-gray-200 p-4 shadow-sm flex flex-col min-h-0"
        >
            <div className="flex justify-between items-center mb-3">
                <h2 className="text-base font-semibold text-gray-800">{title}</h2>
                <div className="flex items-center bg-gray-100 rounded-full p-0.5">
                    <Button 
                        size="sm" 
                        onClick={() => setMode('file')} 
                        className={`rounded-full text-xs px-2.5 py-1 h-auto border-none transition-all ${
                            mode === 'file' ? 'bg-white text-blue-600 shadow-sm' : 'bg-transparent text-gray-500'
                        }`}
                    >
                        <Upload className="w-3 h-3 mr-1" />文件
                    </Button>
                    <Button 
                        size="sm" 
                        onClick={() => setMode('text')} 
                        className={`rounded-full text-xs px-2.5 py-1 h-auto border-none transition-all ${
                            mode === 'text' ? 'bg-white text-blue-600 shadow-sm' : 'bg-transparent text-gray-500'
                        }`}
                    >
                        <PenSquare className="w-3 h-3 mr-1" />文本
                    </Button>
                </div>
            </div>

            <div className="flex-1 min-h-0 overflow-hidden">
                <AnimatePresence mode="wait">
                    {mode === 'file' ? (
                        <motion.div 
                            key="file-upload" 
                            initial={{ opacity: 0 }} 
                            animate={{ opacity: 1 }} 
                            exit={{ opacity: 0 }}
                            onDragEnter={handleDragEnter} 
                            onDragOver={handleDragEnter} 
                            onDragLeave={handleDragLeave} 
                            onDrop={handleDrop} 
                            className={`h-full w-full relative border-2 border-dashed rounded-lg text-center transition-colors flex flex-col justify-center p-3 ${
                                isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-slate-50'
                            }`}
                        >
                            {file ? (
                                <div className="flex flex-col items-center justify-center gap-2 text-sm text-gray-700 h-full">
                                    <FileIcon className="w-7 h-7 text-blue-500" />
                                    <span className="font-medium break-all">{file.name}</span>
                                    <Button onClick={clearFile} variant="ghost" size="sm" className="absolute top-2 right-2 text-gray-400 hover:text-gray-600">
                                        <X className="w-4 h-4"/>
                                    </Button>
                                </div>
                            ) : (
                                <div className="flex flex-col justify-center items-center h-full gap-2">
                                    <p className="text-xs text-gray-500 text-center leading-tight">
                                        支持拖拽上传<br/>png, jpg, pdf, doc, docx, txt
                                    </p>
                                    <div className="grid grid-cols-3 gap-1.5 w-full max-w-[200px]">
                                        <Button variant="outline" size="sm" className="bg-white text-xs py-1.5 flex flex-col items-center gap-1 h-auto min-h-[44px]" onClick={() => fileInputRef.current?.click()}>
                                            <ImageIcon className="w-3.5 h-3.5 text-blue-500" />
                                            <span className="text-[10px]">图片</span>
                                        </Button>
                                        <Button variant="outline" size="sm" className="bg-white text-xs py-1.5 flex flex-col items-center gap-1 h-auto min-h-[44px]" onClick={() => fileInputRef.current?.click()}>
                                            <FileIcon className="w-3.5 h-3.5 text-green-500" />
                                            <span className="text-[10px]">文档</span>
                                        </Button>
                                        <Button variant="outline" size="sm" className="bg-white text-xs py-1.5 flex flex-col items-center gap-1 h-auto min-h-[44px]">
                                            <History className="w-3.5 h-3.5 text-indigo-500" />
                                            <span className="text-[10px]">聊天记录</span>
                                        </Button>
                                    </div>
                                    <input type="file" ref={fileInputRef} onChange={(e) => handleFileSelect(e.target.files[0])} className="hidden" accept=".png,.jpg,.jpeg,.pdf,.doc,.docx,.txt" />
                                </div>
                            )}
                        </motion.div>
                    ) : (
                        <motion.div key="text-input" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="h-full">
                            <Textarea 
                                placeholder={placeholder} 
                                value={text} 
                                onChange={handleTextUpdate} 
                                className="w-full h-full bg-slate-50 border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 resize-none text-sm"
                            />
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </motion.div>
    );
}
