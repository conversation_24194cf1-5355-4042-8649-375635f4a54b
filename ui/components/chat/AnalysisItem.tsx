import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import ProseContent from '../ProseContent';

interface AnalysisItemProps {
    label: string;
    content: string;
}

const AnalysisItem: React.FC<AnalysisItemProps> = ({ label, content }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
        <div className="bg-gray-50/70 dark:bg-gray-800/30 flex flex-col overflow-hidden hover:border-gray-300/80 dark:hover:border-gray-600/60 transition-colors">
            <div
                onClick={() => setIsExpanded(!isExpanded)}
                className="bg-white dark:bg-gray-800 cursor-pointer flex flex-row justify-between items-center mx-4 py-3 rounded-lg border-[0.5px] border-gray-200/80 dark:border-gray-700/40"
            >
                <p className="px-4 text-[#888888] dark:text-[#AAAAAA] text-[12px] font-normal">
                    {label}
                </p>
                <div className="flex items-center mr-4">
                    {isExpanded ? (
                        <ChevronUp size={16} className="text-[#999999] dark:text-[#888888] flex-shrink-0" />
                    ) : (
                        <ChevronDown size={16} className="text-[#999999] dark:text-[#888888] flex-shrink-0" />
                    )}
                </div>
            </div>
            {isExpanded && (
                <div className={cn(
                    "px-4 bg-gray-50/70 dark:bg-gray-800/30",
                    isExpanded ? "border-t border-gray-100/50 dark:border-gray-800/50" : ""
                )}>
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                        <ProseContent content={content} />
                    </div>
                </div>
            )}
        </div>
    );
};

export default AnalysisItem;