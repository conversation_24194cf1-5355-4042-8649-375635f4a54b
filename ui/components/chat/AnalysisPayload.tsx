import React from 'react';
import AnalysisItem from './AnalysisItem';

interface AnalysisPayloadProps {
  analysisPayload: {
    generalAnalysis: string;
    jobMatchAnalysis: string;
  };
}

/**
 * 分析建议组件
 * 
 * 展示一组分析内容供用户查看。用户可以点击展开或收起详细内容。
 * 如果没有分析内容，则不渲染任何内容。
 * 
 * @param {object} props
 * @param {object} props.analysisPayload - 包含分析内容的对象
 * @returns {JSX.Element | null} 渲染分析列表或null
 */
const AnalysisPayload: React.FC<AnalysisPayloadProps> = ({ analysisPayload = {} as {
  generalAnalysis: string;
  jobMatchAnalysis: string;
} }) => {
  const { generalAnalysis, jobMatchAnalysis } = analysisPayload;

  if (!generalAnalysis && !jobMatchAnalysis) {
    return null;
  }

  const analysiss = [];
  if (generalAnalysis) {
    analysiss.push({ label: '通用维度分析明细及修改建议', content: generalAnalysis });
  }
  if (jobMatchAnalysis) {
    analysiss.push({ label: '岗位匹配度明细及修改建议', content: jobMatchAnalysis });
  }

  return (
    <div className="flex flex-col text-black dark:text-white mb-6">
      <div className="flex flex-col gap-2">
        {analysiss.map((analysis, i) => (
          <AnalysisItem key={i} label={analysis.label} content={analysis.content} />
        ))}
      </div>
    </div>
  );
};

export default AnalysisPayload;