import { cn, getLocalItem } from '@/lib/utils';

const Avatar = () => {
  const { isVip, avatar = 'https://static.cyjiaomu.com/mp-mskl/head.png', name } = getLocalItem('__user_detail__', true)?.data?.detail || {};
  return (
    <div
      className={cn("relative flex items-center rounded-full bg-[#F1F1F1]",
        isVip && "border border-[#D2C19E]"
      )}
      title={name}
    >
      <img
        src={avatar || 'https://static.cyjiaomu.com/mp-mskl/head.png'}
        alt="avatar"
        className="w-11 h-11 rounded-full bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center shadow-md object-contain"
      />
      {isVip && (<img className="absolute bottom-0 right-0 w-[16px] h-[16px]" src="https://static.cyjiaomu.com/ai/vip.png" alt="vip" />)}
    </div>
  );
};

export default Avatar;