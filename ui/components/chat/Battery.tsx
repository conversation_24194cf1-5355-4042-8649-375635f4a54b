'use client';

import React from 'react';
import { useChatContext } from '../context/ChatContext';
import { cn, win } from '@/lib/utils';
import { toast } from 'sonner';
import { jmWx } from '@/lib/wx';
import { PlugZap } from 'lucide-react';
import { useDeviceContext } from '../context/DeviceContext';

interface BatteryProps {
    className?: string;
}

interface BatteryConsumeProps {
    consume: number;
    className?: string;
}

const batteryImage = 'https://static.cyjiaomu.com/ai/bean.png';

function Upgrade({ type = '', t }: any) {
    const { channelId } = useDeviceContext() as any;
    const handleUpgradeClick = async () => {
        try { toast.dismiss(t.id); } catch (error) { }
        if (['mp-kl'].includes(channelId)) {
            jmWx.navigateTo({ url: `/pages/training/order/member-center/memberCenter` });
            return;
        }
        if (['gy-ios', 'gy-android', 'mp'].includes(channelId)) {
            toast('建设中，敬请期待...')
            return
        }
        // 通过h5方案打开微信小程序
        win.open(`weixin://dl/business/?action=openMiniProgram&username=gh_535178aa25fe&appid=wx03ab06399e1b6616&path=pages/training/order/member-center/memberCenter&env_version=develop`)
    };

    if (type === 'button') {
        return (
            <button
                onClick={handleUpgradeClick}
                className="flex items-center gap-[5px] bg-[linear-gradient(142deg,#6498FF_0%,#0E5FFF_100%)] text-white disabled:bg-[linear-gradient(142deg,#6498FF_0%,#0E5FFF_100%)] disabled:opacity-30 hover:bg-opacity-85 transition duration-100 rounded-full py-2 pl-[15.5px] pr-[14.5px]"
            >
                <PlugZap size={17} className="text-white" />
                <span className="text-[17px] font-medium text-white">升级</span>
            </button>
        )
    }

    return (<div
        className="flex items-center cursor-pointer hover:opacity-80 transition-opacity gap-1"
        onClick={handleUpgradeClick}
    >
        <span className="text-xs font-medium">升级</span>
    </div>)
}

const Battery: React.FC<BatteryProps> = ({ className }) => {
    const { chatBattery: { battery = {} } = {} } = useChatContext() as any;
    console.log("🚀 ~ battery:", battery)
    const points = +(battery.remainingPoints || 0);
    const alarmPoints = 100;
    const showAlarm = points < alarmPoints;
    // 如果没有电池数据或不可用，则不显示组件
    // if (!battery || !battery.available) {
    //     return null;
    // }

    return (
        <div className={cn(
            "flex items-center gap-[4px] px-2 py-1 bg-white dark:bg-dark-secondary rounded-[13.5px] border border-[#FFAD15] text-[#966620]",
            className
        )}>
            <div className="flex gap-[2px] items-center font-medium text-sm">
                <img className="h-[12px]" src={batteryImage} />
                <span className={`text-xs ${showAlarm ? 'text-red-500' : ''}`}>
                    {points}
                </span>
            </div>
            <Upgrade />
        </div>
    );
};

export const NotEnoughPoints = ({ t }: any) => {
    return (
        <div className="flex flex-col gap-3 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 max-w-md">
            <div className="flex items-center gap-2">
                <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">灵豆不足</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                灵豆告急！消息发送功能已冻结，马上补充灵豆，确保重要沟通 "不断线"
            </p>
            <div className="flex justify-end gap-3 mt-2">
                <button
                    onClick={() => toast.dismiss(t.id)}
                    className="flex items-center gap-[13.5px] text-white bg-gray-200 hover:bg-gray-300 text-gray-700 transition duration-100 rounded-full py-2 pl-[15.5px] pr-[14.5px]"
                >
                    取消
                </button>
                <Upgrade type='button' t={t} />
            </div>
        </div>
    )
}

export const BatteryConsume = ({ consume, className }: BatteryConsumeProps) => {
    const { chatBattery: { battery = {} } = {}, focusMode } = useChatContext() as any;
    // if (!battery.available) {
    //     return null;
    // }
    if (battery?.taskPoints?.[focusMode] <= 0 || consume <= 0 || !battery.available) {
        // 限免
        return <div className={cn("flex items-center gap-[3px]", className)}>
            <img className="h-[12px]" src={batteryImage} />
            <span className="text-xs whitespace-nowrap">限免</span>
        </div >
    }
    return (
        <div className={cn("flex items-center gap-[3px]", className)}>
            <img className="h-[12px]" src={batteryImage} />
            <span className="text-xs">{consume}</span>
        </div >
    )
}

export default Battery;