import { getSuggestions } from "@/lib/actions";
import { getUserText } from "@/lib/utils";

export type Message = {
    messageId: string;
    chatId: string;
    createdAt: Date;
    content: string;
    role: 'user' | 'assistant';
    relatedContent?: any[];
    suggestions?: string[];
    sources?: Document[];
    deviceId?: string; // 设备标识
    reasoning_content?: string; // 推理内容
    hotQuestionId?: number;  // 热门问题ID
    metadata?: any;
    analysisPayload?: any;
};

export interface File {
    fileName: string;
    fileExtension: string;
    fileId: string;
}

export function recentMessages(messages: Message[]): Message[] {
    // 获取最近的两组消息（4条）记录并移除metadata、reasoning_content、sources
    return messages.slice(-4).map(msg => ({
        ...msg,
        metadata: undefined,
        reasoning_content: undefined,
        sources: undefined
    }));
}

export interface Task {
    id: number;
    agentType: string;
    status: string;
}

export const loadMessages = async (
    chatId: string,
    setMessages: (messages: Message[]) => void,
    setIsMessagesLoaded: (loaded: boolean) => void,
    setChatHistory: (history: [string, string][]) => void,
    setFocusMode: (mode: string) => void,
    setNotFound: (notFound: boolean) => void,
    setFiles: (files: File[]) => void,
    setFileIds: (fileIds: string[]) => void,
    share: string,
    onTaskLoaded?: (task: Task | null) => void
) => {
    let url = `${process.env.NEXT_PUBLIC_API_URL}/chats/${chatId}`;

    if (share) {
        url += `?share=${share}`;
    }
    const res = await fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Device-ID': localStorage.getItem('deviceId')!,
        },
    });

    if (res.status === 404) {
        setNotFound(true);
        setIsMessagesLoaded(true);
        return;
    }

    const data = await res.json();

    // 处理 task 信息
    if (data.task) {
        onTaskLoaded?.(data.task);
    } else {
        onTaskLoaded?.(null);
    }

    const messages = data?.messages?.map?.((msg: any) => {
        return {
            ...msg,
            ...msg.metadata,
            sources: msg?.sources || [],
        };
    }) as Message[];

    setMessages(messages);

    // 异步获取建议
    if (messages.length > 0) {
        const lastMsg = messages[messages.length - 1];
        if (lastMsg.role === 'assistant') {
            getSuggestions(recentMessages(messages), data.task?.agentType).then(({ suggestions = [], relatedContent = [] }) => {
                const updatedMessages = messages.map(msg => {
                    if (msg.messageId === lastMsg.messageId) {
                        return { ...msg, suggestions, relatedContent };
                    }
                    return msg;
                });
                setMessages(updatedMessages);
            });
        }
    }

    const history = messages.map((msg) => {
        return [msg.role, msg.content];
    }) as [string, string][];

    console.debug(new Date(), 'app:messages_loaded');

    if (messages.length > 0 && messages[0]?.content) {
        document.title = `${getUserText(messages[0].content)} - 灵通`;
    }

    const files = data.chat.files.map((file: any) => {
        return {
            fileName: file.name,
            fileExtension: file.name.split('.').pop(),
            fileId: file.fileId,
        };
    });

    setFiles(files);
    setFileIds(files.map((file: File) => file.fileId));

    setChatHistory(history);
    setFocusMode(data.chat.focusMode);

    setIsMessagesLoaded(true);
};
