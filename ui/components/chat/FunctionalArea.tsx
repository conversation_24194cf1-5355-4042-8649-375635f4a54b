import React from 'react';
import { clkLog } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { withQuery } from '@/lib/utils';

interface FunctionalAreaProps {
  topTabs: any[];
  availableTasks: any[];
  channelId: string;
}

const FunctionalArea: React.FC<FunctionalAreaProps> = ({
  topTabs,
  availableTasks,
  channelId,
}) => {
  const router = useRouter();

  const gotoTab = ({ path = '', label, link }: any) => {
    clkLog('顶部导航', { path, label });
    if (link) {
      return location.href = link;
    }
    if (!path) {
      throw new Error('path is required');
    }
    router.push(withQuery(path, {}, ['source', 'taskId']));
  };

  return (
    <div className="flex flex-col w-full mt-[13.5px] space-y-3">
      {topTabs.filter(it => it?.auth?.(availableTasks, channelId)).map((tab) => (
        <React.Fragment key={tab.key}>
          {tab.type === 'img' ? (
            <img
              src={tab.icon}
              onClick={() => gotoTab(tab)}
              alt={tab.label}
              className={tab.className}
            />
          ) : (
            <button
              onClick={() => gotoTab(tab)}
              className={`group flex items-center gap-[1px] py-1 pl-1 pr-3 rounded-md bg-white dark:bg-[#2A2A2A] text-black/50 dark:text-white/50 hover:text-black/80 dark:hover:text-white/80 text-sm transition-transform duration-200 relative border border-gray-200 dark:border-gray-700 w-full`}
            >
              <img
                src={tab.icon}
                alt={tab.label}
                className='w-[17px] h-[17px] opacity-60 group-hover:opacity-100 transition-opacity duration-200'
              />
              <span>{tab.label}</span>
              {tab.isNew && <img src={tab.newIcon} className='absolute top-[-7px] right-[-20px] w-[28px] h-[12px]' />}
            </button>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default FunctionalArea;
