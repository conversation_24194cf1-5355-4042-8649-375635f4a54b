import React from 'react';
import { MoveRight, MessageSquare } from 'lucide-react';
import { AnimatePresence } from 'framer-motion';

interface SuggestionsProps {
  suggestions: string[];
  sendMessage: (message: string) => void;
}

const Suggestions: React.FC<SuggestionsProps> = ({ suggestions, sendMessage }) => {
  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  return (
    <div className="px-4 flex flex-col gap-2 text-black dark:text-white">
      <div className="flex items-center gap-2 mb-3">
        <div className="p-1.5 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500">
          <MessageSquare className="w-4 h-4 text-white" />
        </div>
        <h3 className="text-base font-semibold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
          猜你想问
        </h3>
      </div>
      <div className="flex flex-col gap-2">
        <AnimatePresence>
          {suggestions.map((suggestion, i) => (
            <div
              className="flex flex-col space-y-3 text-sm bg-white dark:bg-black rounded-lg p-4 shadow-sm border border-gray-100 dark:border-gray-800"
              key={i}
            >
              <div
                onClick={() => {
                  sendMessage(suggestion);
                }}
                className="cursor-pointer flex flex-row justify-between font-medium space-x-2 items-center"
              >
                <p className="transition duration-200 hover:text-[#24A0ED] text-sm text-gray-700 dark:text-gray-300 font-medium">
                  {suggestion}
                </p>
                <MoveRight
                  size={20}
                  className="text-gray-400 flex-shrink-0"
                />
              </div>
            </div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Suggestions;