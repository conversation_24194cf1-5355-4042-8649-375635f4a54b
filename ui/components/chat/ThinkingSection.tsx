import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp, ChevronDown, CheckCircle, Brain } from 'lucide-react';
import { Button } from '../ui/button';

interface ThinkingSectionProps {
  content: string;
  reasoningContent: string;
  isHistory?: boolean;      // 是否历史消息
  isThinking?: boolean;     // 是否思考中
  onReasoningContentChange?: (content: string) => void; // 思考内容变化回调
}

const ThinkingSection: React.FC<ThinkingSectionProps> = ({
  content,
  reasoningContent,
  isHistory = false,
  isThinking = false,
  onReasoningContentChange,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  // 控制展开/折叠逻辑
  useEffect(() => {
    if (isHistory) {
      setIsExpanded(false);
    } else if (isThinking) {
      setIsExpanded(true);
    }
  }, [isHistory, isThinking]);

  // 思考内容变化时回调
  useEffect(() => {
    if (onReasoningContentChange) {
      onReasoningContentChange(reasoningContent);
    }
  }, [reasoningContent, onReasoningContentChange]);

  // 思考完成后自动折叠
  useEffect(() => {
    if (!isThinking && !isHistory) {
      setIsExpanded(false);
    }
  }, [isThinking, isHistory]);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-3"
    >
      <Button
        variant="ghost"
        onClick={handleToggle}
        className="w-full flex items-center justify-between p-0 h-auto text-left hover:bg-transparent"
      >
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
            <CheckCircle className="w-4 h-4 text-white" />
          </div>
          <span className="font-medium text-green-700 flex items-center gap-1.5 text-sm">
            <Brain className="w-4 h-4" />
            {content ? '已深度思考' : '思考中...'}
          </span>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-green-600" />
        ) : (
          <ChevronDown className="w-4 h-4 text-green-600" />
        )}
      </Button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-3 text-sm text-gray-700 leading-relaxed overflow-hidden"
          >
            {reasoningContent}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ThinkingSection;
