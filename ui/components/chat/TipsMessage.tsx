import React from 'react';
import { FZ_JsBridge } from '@/lib/js-bridge';
import { useExpanded } from '../context/ExpandedContext';
import { cn } from '@/lib/utils';

interface TipsMessageProps {
    tips?: string;
}

const TipsMessage: React.FC<TipsMessageProps> = ({
    tips = [4].includes(FZ_JsBridge.getAndroidChannel()) ?
        '启用R1满血版，开启全新对话' :
        '使用 DeepSeek-R1 满血版开始一段新的对话'
}) => {
    const { isExpanded } = useExpanded();
    return (
        <div
            className="fixed bottom-[10px] w-full z-10 left-[50%]"
            style={{
                marginLeft: isExpanded ? 'calc(-50% + 140px)' : '-50%'
            }}
        >
            <div className="text-center text-[#B9B9B9] dark:text-[#6D6D6D] text-[11px] font-[500] leading-[15px] font-[PingFangSC]">
                {tips}
            </div>
            <div className='hidden lg:flex gap-[3px] mt-[12px] bg-[#F6F9FD] dark:bg-[#1E1E1E] rounded-[16px] px-[20px] pt-[11px] pb-[12px] max-w-[764px] mx-auto'>
                <div className='flex flex-col gap-[3px] flex-1'>
                    <h3 className='text-[#333333] dark:text-[#E0E0E0] text-[14px] font-[600] leading-[40px]'>马上开炼</h3>
                    <div className='flex items-center gap-[10px]'>
                        <ul className='text-[#6D6D6D] dark:text-[#A0A0A0] text-[12px] leading-[24px] font-[400] flex-1 list-none'>
                            <li className='flex items-start'>
                                <div className='w-[5px] h-[5px] rounded-full bg-[#C3CAD3] dark:bg-[#4D4D4D] mt-[10px] mr-[6px] flex-shrink-0'></div>
                                <span>1. 专注于提升职场人士各类技能的提升平台。</span>
                            </li>
                            <li className='flex items-start'>
                                <div className='w-[5px] h-[5px] rounded-full bg-[#C3CAD3] dark:bg-[#4D4D4D] mt-[10px] mr-[6px] flex-shrink-0'></div>
                                <span>2. 已上线7大技能模块，733+题目，全面多维度打造个人能力体系，搞定升职加薪！</span>
                            </li>
                        </ul>
                        <div className='flex items-end gap-[8px]'>
                            <div className='flex flex-col text-[#333333] dark:text-[#E0E0E0] text-[12px] font-[400] leading-[18px]'>
                                <div> 微信扫码 </div>
                                <div className='text-[#0E5FFF] dark:text-[#4D8BFF]'> 马上开炼 </div>
                            </div>
                        </div>
                    </div>
                </div>
                <img src="https://static.cyjiaomu.com/mp-mskl/qr/qr-kl.png" alt="" className='w-[90px] h-[90px]' />
            </div>
        </div>
    );
};

export default TipsMessage;