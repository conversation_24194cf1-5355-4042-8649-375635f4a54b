'use client';

import React from 'react';
import { Message } from '../chat/ChatTools';
import UserMessageItem from "./UserMessageItem";

interface UserMessageBoxProps {
    message: Message;
}

const UserMessageBox: React.FC<UserMessageBoxProps> = ({ message }) => {
    const { recognizerPayload = {} } = message?.metadata || {};
    const { resumeFileName, resumeText, resumeFileId, jobFileName, jobText, jobFileId } = recognizerPayload;
    const fileType = resumeFileId ? (resumeFileName.split('.').pop() || 'text') : '';
    const jobFileType = jobFileId ? (jobFileName.split('.').pop() || 'text') : '';
    return (
        <div className="px-4">
            {/* resumeFileName */}
            {(resumeFileName || resumeFileId) && <UserMessageItem title="简历信息" message={{ content: (resumeFileName || resumeFileId) } as Message} fileId={resumeFileId} fileType={fileType} />}

            {/* resumeText */}
            {resumeText && <UserMessageItem title="简历信息" message={{ content: resumeText } as Message} />}

            {/* jobFileName */}
            {(jobFileName || jobFileId) && <UserMessageItem title="岗位信息" message={{ content: (jobFileName || jobFileId) } as Message} fileId={jobFileId} fileType={jobFileType} />}

            {/* jobText */}
            {jobText && <UserMessageItem title="岗位信息" message={{ content: jobText } as Message} />}

            <UserMessageItem message={message} />
        </div>
    );
};

export default UserMessageBox;