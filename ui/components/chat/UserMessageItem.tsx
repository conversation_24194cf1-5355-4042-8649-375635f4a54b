'use client';

import React, { useRef, useState, useEffect } from 'react';
import { cn, getUserText, gotoWebPage } from '@/lib/utils';
import { Message } from '../chat/ChatTools';
// Import icons from lucide-react
import { FileText, FileImage, FileType as FilePdf, FileCode, File } from 'lucide-react';
import { useDeviceContext } from '../context/DeviceContext';

interface UserMessageBoxProps {
    message: Message;
    title?: string;
    fileType?: string;
    fileId?: string;
}

// Updated function to get icon based on fileType using lucide-react
const getFileIcon = (fileType?: string) => {
    if (!fileType) return null;

    const type = fileType.toLowerCase();
    const iconProps = { size: 16, className: "shrink-0" };

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type)) {
        return <FileImage {...iconProps} />;
    }
    if (type === 'pdf') {
        return <FilePdf {...iconProps} />;
    }
    if (['doc', 'docx'].includes(type)) {
        // Using FileText for Word, adjust if a specific Word icon is preferred/available
        return <FileText {...iconProps} />;
    }
    if (type === 'txt') {
        return <FileText {...iconProps} />;
    }
    // Example for code files
    if (['js', 'ts', 'tsx', 'jsx', 'py', 'java', 'html', 'css', 'json', 'md'].includes(type)) {
        return <FileCode {...iconProps} />;
    }

    // Default icon for other/unknown types
    return <File {...iconProps} />;
};

const UserMessageBox: React.FC<UserMessageBoxProps> = ({ message, fileType, fileId, title }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const contentRef = useRef<HTMLHeadingElement>(null);
    const [shouldShowExpand, setShouldShowExpand] = useState(false);
    const { channelId } = useDeviceContext() as any;

    useEffect(() => {
        if (contentRef.current) {
            const lineHeight = parseInt(window.getComputedStyle(contentRef.current).lineHeight);
            const contentHeight = contentRef.current.scrollHeight;
            // Check if content height is greater than 3 lines
            setShouldShowExpand(contentHeight > lineHeight * 3);
        }
    }, [message.content]);

    const handleIconClick = () => {
        if (fileId && fileType) {
            gotoWebPage({ url: `https://jmso.cyjiaomu.com/ai/files/file/${fileId}`, channelId });
        }
    };

    return (
        <div className={cn('w-full mb-4 flex justify-end')}> {/* Align container to the right */}
            <div className="relative flex flex-col items-end max-w-[85%]"> {/* Limit width and align content end */}

                <div
                    className={cn(
                        'bg-gradient-to-r from-blue-500 to-cyan-500 text-white p-4 rounded-2xl shadow-sm w-fit flex flex-col items-end gap-1 ml-auto w-full',
                    )}
                >
                    {title && (
                        <div className="text-white text-[14px] font-[500] leading-[20px]">
                            {title}
                        </div>
                    )}
                    <div className={cn('flex items-center gap-1 w-full', fileType && 'cursor-pointer hover:opacity-75 transition-opacity underline')} onClick={handleIconClick}>
                        {fileType && getFileIcon(fileType)}
                        <h2
                            ref={contentRef}
                            className={cn(
                                'text-white text-[14px] font-[400] leading-[20px] text-justify break-words whitespace-pre-wrap overflow-hidden',
                                !isExpanded && 'line-clamp-3',
                            )}
                            title={getUserText(message.content)} // Add title for potentially truncated text
                        >
                            {getUserText(message.content)}
                        </h2>
                    </div>
                </div>
                {shouldShowExpand && (
                    <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="expand-button text-[#24A0ED] text-sm mt-1 hover:text-opacity-80 transition-colors duration-200 self-end" // Align button to the end
                    >
                        {isExpanded ? '收起' : '展开'}
                    </button>
                )}
            </div>
        </div>
    );
};

export default UserMessageBox;