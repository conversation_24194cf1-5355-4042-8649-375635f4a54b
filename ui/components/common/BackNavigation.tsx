'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useExpanded } from '../context/ExpandedContext';
import { cn, withQuery } from '@/lib/utils';

interface BackNavigationProps {
  title?: string;
  onBack?: () => void;
  children?: React.ReactNode;
  className?: string;
}

const BackNavigation: React.FC<BackNavigationProps> = ({
  title = '返回',
  onBack,
  children,
  className
}) => {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);
  const { isExpanded } = useExpanded();
  console.log("🚀 BackNavigation ~ isExpanded:", isExpanded)
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.push(withQuery('/', { refresh: `${Math.random()}` }, ['source', 'taskId']));
    }
  };

  if (!isMounted) {
    return <div className="h-10"></div>;
  }

  return (
    <div className={cn("flex items-center justify-between h-10 w-full lg:px-4 mx-auto",
      isExpanded ? "" : "lg:pl-[150px]",
      className
    )}>
      <button
        onClick={handleBack}
        className="cursor-pointer flex items-center text-gray-700 dark:text-gray-300 hover:text-black dark:hover:text-white"
      >
        <img src="https://static.cyjiaomu.com/mp-mskl/<EMAIL>" className="w-auto h-5 mr-1" />
        <span className="w-100 max-w-[260px] lg:max-w-[500px] truncate">{title}</span>
      </button>

      {children && (
        <div className="flex items-center">
          {children}
        </div>
      )}
    </div>
  );
};

export default BackNavigation;
