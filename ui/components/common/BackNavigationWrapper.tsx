'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// 在客户端组件中使用dynamic导入，禁用SSR
const BackNavigation = dynamic(
    () => import('@/components/common/BackNavigation'),
    { ssr: false }
);

interface BackNavigationWrapperProps {
    title: string;
    onBack?: () => void;
    children?: React.ReactNode;
    className?: string;
}

export default function BackNavigationWrapper({ title, children, onBack, className }: BackNavigationWrapperProps) {
    return <BackNavigation title={title} onBack={onBack} className={className}>
        {children}
    </BackNavigation>;
}