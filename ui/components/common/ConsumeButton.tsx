import { ArrowUp } from 'lucide-react';
import { BatteryConsume } from '../chat/Battery';

interface AnalyzeButtonProps {
    onClick?: () => void;
    disabled?: boolean;
    consumePoints: number;
    title?: string;
}

const ConsumeButton = ({ onClick, disabled, consumePoints, title = "" }: AnalyzeButtonProps) => {
    return (
        <button
            className="flex items-center gap-[11.5px] bg-[linear-gradient(142deg,#6498FF_0%,#0E5FFF_100%)] text-white disabled:bg-[linear-gradient(142deg,#6498FF_0%,#0E5FFF_100%)] disabled:opacity-30 hover:bg-opacity-85 transition duration-100 rounded-full py-2 pl-[15.5px] pr-[14.5px]"
            onClick={onClick}
            disabled={disabled}
            title={title}
        >
            <ArrowUp size={17} />
            <BatteryConsume consume={consumePoints} />
        </button>
    );
};

export default ConsumeButton;