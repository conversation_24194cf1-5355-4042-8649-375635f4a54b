import React, { useMemo } from 'react';

interface TabBarItem {
  pagePath: string;
  iconPath: string;
  selectedIconPath: string;
  text: string;
}

interface TabBarConfig {
  custom: boolean;
  color: string;
  selectedColor: string;
  backgroundColor: string;
  borderStyle: string;
  list: TabBarItem[];
}

interface CustomTabBarProps {
  currentPage: string; // 当前页面路径
  onTabClick: (item: TabBarItem) => void; // Tab 点击回调
}

const tabBarConfig: TabBarConfig = {
  custom: true,
  color: "#999",
  selectedColor: "#0E5FFF",
  backgroundColor: "#ffffff",
  borderStyle: "white",
  list: [
    {
      pagePath: "/pages/home/<USER>",
      iconPath: "https://static.cyjiaomu.com/mp-mskl/ai/tabbar/home_normal.png",
      selectedIconPath: "https://static.cyjiaomu.com/mp-mskl/ai/tabbar/home.png",
      text: "首页",
    },
    {
      pagePath: "/pages/ai/index",
      iconPath: "https://static.cyjiaomu.com/mp-mskl/ai/tabbar/ai_normal.png",
      selectedIconPath: "https://static.cyjiaomu.com/mp-mskl/ai/tabbar/ai.png",
      text: "灵通",
    },
    {
      pagePath: "/pages/usercenter/index",
      iconPath: "https://static.cyjiaomu.com/mp-mskl/ai/tabbar/usercenter_normal.png",
      selectedIconPath: "https://static.cyjiaomu.com/mp-mskl/ai/tabbar/usercenter.png",
      text: "我的",
    },
  ],
};

const CustomTabBar: React.FC<CustomTabBarProps> = ({
  currentPage,
  onTabClick,
}) => {
  const { list, color, selectedColor, backgroundColor, borderStyle } = tabBarConfig;

  const tabBarStyles: React.CSSProperties = {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    height: '13.34vw',
    backgroundColor: backgroundColor,
    borderTop: `1px solid ${borderStyle === 'white' ? '#e0e0e0' : borderStyle}`,
    boxSizing: 'content-box',
    paddingBottom: `calc(env(safe-area-inset-bottom))`,
  };

  const tabBarItemStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    height: '100%',
  };

  const iconStyles: React.CSSProperties = {
    width: '6.4vw',
    height: '6.4vw',
  };

  const textStyles: React.CSSProperties = {
    fontSize: '2.66vw',
  };

  return (
    <div style={tabBarStyles}>
      {list.map((item, index) => {
        const isActive = currentPage.startsWith(item.pagePath);
        return (
          <div
            key={index}
            style={tabBarItemStyles}
            onClick={() => {
              console.log('点击了', item);
              onTabClick(item)
            }}
          >
            <img
              style={iconStyles}
              src={isActive ? item.selectedIconPath : item.iconPath}
              alt={item.text}
            />
            <span style={{ ...textStyles, color: isActive ? selectedColor : color }}>
              {item.text}
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default CustomTabBar;
