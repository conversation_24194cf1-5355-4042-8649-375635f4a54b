export const questions = [
    {
        title: "帮我写作",
        list: [
            {
                label: "朋友圈",
                description: "精心设计的朋友圈文案",
                icon: `<svg t="1741671571126" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2588" width="16" height="16"><path d="M381.8288 18.901489c42.635029-11.310603 86.403882-17.002232 130.205481-17.002232 68.479651 0 135.072325 13.469779 197.999516 40.101323l-8.362458 369.71439L381.8288 18.901489 381.8288 18.901489z" fill="#F57659" p-id="2589"></path><path d="M71.201134 255.390621c22.278399-38.284955 49.218982-73.313749 80.134085-104.090707 48.393173-48.396243 104.980983-85.958744 168.285774-111.666242l255.614725 267.305999L71.201134 255.390621 71.201134 255.390621z" fill="#F0B360" p-id="2590"></path><path d="M18.900977 642.275066c-11.276834-42.463114-17.000186-86.234013-17.000186-130.276089 0-68.548212 13.502525-135.140886 40.099277-197.933001l369.749182 8.293897L18.900977 642.275066 18.900977 642.275066z" fill="#EFDE58" p-id="2591"></path><path d="M255.39011 952.798355c-38.010709-22.1423-72.968895-49.079812-104.021122-80.134085-48.428989-48.325635-85.993536-104.911398-111.664196-168.283728l267.302929-255.513418L255.39011 952.798355 255.39011 952.798355z" fill="#BBEF5C" p-id="2592"></path><path d="M512.034281 1022.100744c-68.481697 0-135.072325-13.470803-197.999516-40.031738l8.362458-369.71439 319.843562 392.81348C599.535147 1016.411161 555.80211 1022.100744 512.034281 1022.100744L512.034281 1022.100744z" fill="#61F15E" p-id="2593"></path><path d="M448.830797 717.096145l504.036631 51.54905c-22.107507 37.940101-48.976458 72.900334-80.167854 104.019075-48.327682 48.464805-104.980983 86.029352-168.283728 111.734804L448.830797 717.096145 448.830797 717.096145z" fill="#5BE4EE" p-id="2594"></path><path d="M612.320335 701.707666l392.81348-319.915193c11.276834 42.499953 16.966417 86.302575 16.966417 130.206504 0 68.753897-13.502525 135.282103-40.066531 198.002586L612.320335 701.707666 612.320335 701.707666z" fill="#5C9BF6" p-id="2595"></path><path d="M768.60989 71.235414c37.975916 22.038946 73.004711 48.942689 104.089683 80.0645 48.464805 48.326659 86.027305 104.980983 111.664196 168.284751l-267.302929 255.580956L768.60989 71.235414 768.60989 71.235414z" fill="#6D58F1" p-id="2596"></path></svg>`,
                input: "帮我生成一条关于朋友圈文案",
                prompt: `
                    <<|>>
                    你的任务是生成一条关于指定主题的朋友圈。请仔细阅读以下主题信息，并按照指示撰写朋友圈。
                    主题:
                    <theme>
                    {{THEME}}
                    </theme>
                    在撰写朋友圈时，请遵循以下指南:
                    1. 使用自然、亲切的语言风格。
                    2. 围绕主题展开内容，可以分享观点、经历、感受等。
                    3. 内容尽量生动有趣，能够吸引他人的关注。
                    4. 控制朋友圈的长度，保持简洁明了。
                    5. 确保没有任何拼写或语法错误。
                    请在写下你的朋友圈内容。
                `,
                list: [
                    "帮我生成一条关于 <span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span> 的朋友圈。",
                    "帮我生成一条关于 <span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">销售战报</span> 的朋友圈。",
                    "帮我生成一条关于 <span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">公司营销活动</span> 的朋友圈。",
                ],
            },
            {
                label: "小红书",
                description: "打造吸睛的小红书内容",
                icon: `<svg t="1741671814341" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3698" width="16" height="16"><path d="M726.51776 457.45152c-6.70208-0.0768-13.39392 0-20.00384-0.0768-2.37056 0-3.0464 1.05984-3.0464 3.23072 0.0768 5.10976 0.0768 10.13248 0.0768 15.232v0.01024c0.07168 4.87936 0 9.7536 0.07168 14.56128 0 3.90656 0.68096 4.66944 4.45952 4.66944 7.1424 0.0768 14.27456 0 21.41696 0.0768 2.67776 0 3.72736-1.28 3.65056-3.75808-0.08704-9.1648-0.08704-18.31936-0.15872-27.48416a6.7584 6.7584 0 0 0-6.46656-6.46144z" fill="#FF2E4D" p-id="3699"></path><path d="M849.92 51.2h-675.84c-67.8656 0-122.88 55.0144-122.88 122.88v675.84c0 67.8656 55.0144 122.88 122.88 122.88h675.84c67.8656 0 122.88-55.0144 122.88-122.88V174.08c0-67.8656-55.0144-122.88-122.88-122.88zM250.78784 505.73312c-0.73728 10.59328-1.41312 21.25312-2.60608 31.8464-2.08896 18.39104-6.24128 36.26496-14.6432 52.864-2.16064 4.12672-5.13536 7.79776-8.18176 12.45696-1.85344-3.90656-3.41504-6.97856-4.82816-10.13248a3203.59424 3203.59424 0 0 1-14.79168-33.56672c-0.52736-1.2032-0.896-2.92352-0.36864-3.97824 3.19488-6.83008 3.41504-14.12096 3.85536-21.40672 0.60416-9.15968 1.35168-18.24256 2.01728-27.39712 0.51712-7.00416 0.80896-13.9776 1.39776-20.96128 0.67584-8.10496 1.49504-16.21504 2.16064-24.24832 0.14848-1.96608 1.04448-2.56 2.82624-2.56 11.0848 0 22.07744 0 33.16224-0.07168 2.37056 0 3.0464 0.98304 2.89792 3.23072-0.96768 14.63296-1.86368 29.28128-2.89792 43.92448z m71.29088 87.32672c-0.73728 9.46176-5.13536 17.49504-12.5696 23.5008-5.43232 4.352-11.74528 6.15936-18.6624 6.08256-5.87264 0-11.66848-0.0768-17.54112 0-2.00192 0-3.27168-0.60416-4.09088-2.55488-3.41504-7.6544-6.90688-15.32416-10.32192-22.97344-0.52736-1.13152-0.67584-2.33472-1.13152-3.456-1.63328-4.12672-1.5616-4.28544 2.97472-4.36224h13.90592c5.94944 0 8.47872-2.46784 8.5504-8.56576 0.07168-4.57216 0.07168-9.14944 0.07168-13.73696V494.2336c0.14848 0.15872 0.22016 0.15872 0.29696 0.15872V408.63744c0-4.28544 0.14848-4.43392 4.38784-4.43392h29.21472c5.13536 0 5.20704 0.14848 5.20704 5.40672 0 27.1872 0 54.36416 0.0768 81.47968 0.0768 23.87456 0.29696 47.75936 0.29696 71.6288 0 10.14272 0.14848 20.26496-0.6656 30.34112z m75.58656-28.90752c-4.98688 11.56096-10.19904 22.97344-15.31904 34.4576-0.45568 1.13664-1.19296 2.25792-2.3808 4.42368v0.01024c-2.97472-4.5056-6.0928-8.18176-8.11008-12.39552-2.82624-6.13888-4.5312-12.83584-7.35744-18.9952-3.0464-6.6816-4.15744-13.88032-5.57568-20.94592-1.1776-6.02112-1.40288-12.25216-1.8432-18.3296-1.2032-15.39584-2.23744-30.78656-3.44064-46.09536a2449.95584 2449.95584 0 0 0-2.0736-25.1648c-0.14848-1.50016 0.2304-2.176 1.94048-2.176 11.52512 0 22.97344-0.14848 34.49856-0.22016 2.1504 0 3.0464 0.96768 3.11808 2.9952 0.29696 4.65408 0.51712 9.31328 0.88576 13.97248 0.29696 3.83488 0.73728 7.6544 1.04448 11.41248 0.51712 5.40672 1.04448 10.81344 1.41312 16.14336 0.51712 6.90688 0.51712 13.81888 1.4848 20.63872 1.34144 10.4448 0.29696 21.10464 3.93216 31.32928 0.89088 2.40128-0.96768 6.08768-2.21696 8.93952z m84.28032 22.016c-2.89792 6.6816-6.02112 13.21472-8.99072 19.82464-1.64352 3.74784-3.19488 7.49568-4.76672 11.25376-1.85344 4.51072-3.11808 5.40672-7.87456 5.40672h-22.2976c-7.52128 0-15.0272 0.23552-22.53312-0.0768-3.56352-0.14336-7.0656-1.27488-10.62912-2.02752-1.792-0.36864-2.16064-1.42336-1.41312-3.14368a3709.71648 3709.71648 0 0 0 13.45024-29.21472c1.04448-2.24768 1.85344-4.65408 3.0464-6.90688 0.29696-0.6144 1.41312-1.28 2.00192-1.13152 12.42112 3.15392 25.13408 2.77504 37.76512 2.63168a874.6496 874.6496 0 0 1 20.07552 0c3.19488 0.00512 3.50208 0.45568 2.16576 3.38432z m3.84-21.86752a4.48512 4.48512 0 0 1-2.74944 1.4336c-13.89568 0.0768-27.8784 0.14848-41.77408-0.0768-4.23936-0.08704-8.5504-1.05472-11.74528-4.28544-3.3536-3.3792-4.98688-7.36256-3.28192-11.93984a897.52576 897.52576 0 0 1 9.58464-24.10496c3.88096-9.15456 7.81312-18.31936 12.05248-28.2368-2.30912-0.14848-3.712-0.29696-5.04832-0.29696-4.09088-0.07168-8.18176 0.29696-12.27264-0.2304-4.45952-0.51712-8.99072-1.04448-12.48256-4.79232-3.42528-3.6864-3.94752-8.04352-2.60608-12.32384 2.1504-6.83008 4.97664-13.44 7.80288-20.04992 2.67776-6.15424 5.72416-12.16 8.47872-18.24256 2.97472-6.53824 5.86752-13.07136 8.77056-19.6096a1361.99168 1361.99168 0 0 0 7.6544-17.33632c0.73728-1.80736 1.8688-2.47808 3.87072-2.47808 10.93632 0.07168 21.92896 0 32.86528 0 3.6352 0 3.712 0.36864 2.29888 3.6864-6.31296 14.63296-12.71808 29.20448-18.95936 43.84768a11.52 11.52 0 0 0-1.19296 4.87936c0.22016 3.90656 1.04448 4.5056 5.06368 4.5056 8.17152 0.0768 16.35328 0 24.448 0 1.64864 0 3.3536 0.22016 4.98688 0.29696 2.30912 0.0768 2.60608 1.05984 1.63328 3.072a2455.21408 2455.21408 0 0 0-13.3888 29.21472c-3.03616 6.91712-5.93408 13.89568-8.9088 20.8128a1530.1632 1530.1632 0 0 1-6.1696 13.80864c-1.94048 4.20352-0.60416 6.31296 4.15232 6.38976 6.02112 0 12.04224 0.0768 18.05824 0 2.08896 0 3.13344 0.60416 2.08896 2.85184-3.6352 8.25344-7.21408 16.58368-10.84928 24.85248-0.67072 1.50016-1.408 3.072-2.3808 4.352z m134.81472 58.73664h-125.3376c-1.72032-0.22016-3.48672-0.22016-5.94432-0.22016v-0.01536c0.88064-2.61632 1.41312-4.41856 2.1504-6.0672 4.69504-10.29632 9.4464-20.5056 14.0544-30.79168 1.04448-2.33472 2.52928-2.92352 4.75648-2.92352h28.6976c4.54656 0 4.75648-0.2304 4.75648-4.74112V461.66016c0-3.97824-0.0768-4.05504-4.08064-4.05504-6.10304 0-12.26752-0.0768-18.36544 0-2.30912 0-3.27168-0.51712-3.27168-3.1488 0.14848-10.97216 0.0768-21.92896 0.0768-32.88576 0-3.90656 0.0768-3.90656 3.86048-3.90656h73.00096c4.23936 0 8.5504 0.0768 12.78976 0 2.01728 0 2.82624 0.82432 2.74944 2.85184-0.0768 11.41248-0.0768 22.82496-0.0768 34.31424 0 2.02752-0.73728 2.77504-2.82624 2.77504-6.6048-0.0768-13.14304 0.07168-19.77856 0.07168-2.29376 0-3.33824 1.05984-3.33824 3.46624 0.0768 18.39104 0.14336 36.7104 0.14336 55.11168 0 20.87424 0 41.74848 0.0768 62.6944 0 3.75808 0.36864 4.21376 4.17792 4.21376h31.4368c3.41504 0 3.87072 0.36864 3.93728 3.81952 0.08704 10.97216 0 21.92896 0.08704 32.89088-0.01024 2.8672-1.57184 3.16416-3.73248 3.16416z m198.69696-34.92864c-0.14848 16.37376-11.008 29.21472-26.38848 32.89088-4.31616 1.05472-8.78592 1.35168-13.24544 1.5104-6.83008 0.22016-13.7472 0.07168-20.58752 0.07168-4.23936 0-5.42208-0.83456-6.9888-4.66432-3.33824-7.95136-6.83008-15.90784-10.26048-23.87456l-0.66048-1.57184c-1.19296-3.072-0.81408-3.61472 2.45248-3.61472 9.43616-0.07168 18.95424 0.15872 28.3904-0.29184 5.65248-0.29696 8.03328-2.85696 8.18688-8.64256 0.22016-11.04384-0.29696-22.07744-0.14848-33.11104 0.0768-5.48864-6.84032-11.42272-11.74528-11.71968a32.8448 32.8448 0 0 0-2.74944-0.14336c-18.73408 0-37.54496 0-56.2688 0.07168-5.27872 0-5.65248 0.53248-5.65248 5.8624l0.20992 77.55776c0 4.14208-0.0768 4.21376-4.23936 4.21376h-31.22176c-4.01408 0-4.3008-0.3072-4.3008-4.28544v-39.94112c0.06144 0.14336 0.13312 0.14336 0.20992 0.14336v-40.99584c0-2.78016-1.85344-2.93888-3.78368-2.93888-10.19392 0.08704-20.44416 0.31232-30.62272 0.31232-6.92224 0-6.17984 0.8192-6.25664-6.38976-0.0768-9.90208 0-19.90144 0-29.80352 0-3.59936 0.36864-4.05504 3.94752-4.13184 10.7008-0.07168 21.33504 0 32.04096-0.07168 4.09088 0 4.31104-0.15872 4.38272-4.21376 0.0768-9.90208-0.0768-19.8144 0-29.73184 0-2.4832-1.04448-3.23072-3.41504-3.23072-6.84544 0.0768-13.76256-0.07168-20.60288 0-2.1504 0-2.89792-0.74752-2.89792-2.92352 0.09216-11.26912 0.09216-22.46144-0.06144-33.72544 0-2.70336 1.03424-3.29216 3.41504-3.29216 6.31296 0.0768 12.6464 0 18.95936 0 4.23424 0 4.45952-0.3072 4.5312-4.74112 0-2.61632 0.14848-5.24288 0-7.87456-0.07168-2.4832 1.04448-3.15904 3.34336-3.15904 9.07776 0.0768 18.22208 0.0768 27.28448 0.0768h4.97664c3.94752 0 4.0192 0 4.1728 4.05504 0.06656 2.4064-0.1536 4.87936-0.08704 7.28576 0.0768 3.3792 0.9728 4.2752 4.31616 4.36224 5.65248 0.0768 11.30496 0.0768 17.024 0.0768 14.6432 0.07168 27.3664 5.09952 37.0176 16.29184 5.35552 6.22592 8.69888 13.81888 9.216 22.14912 0.52736 8.47872 0.15872 17.03936 0.3072 25.52832 0 3.15904 0.22016 6.38976 0.36864 9.53344 0.14336 3.15904 0.896 3.97824 4.09088 3.90656a48.56832 48.56832 0 0 1 19.03104 3.15904c13.00992 5.03808 21.03296 14.18752 23.63904 28.01152a44.4416 44.4416 0 0 1 0.73728 8.33024c0.08192 17.88928 0.06656 35.78368-0.06656 53.6832zM810.14272 453.632c-5.94432 3.90656-12.1856 3.75808-19.4048 3.6864-2.23744 0-5.20192 0.07168-8.09984-0.0768-0.7424-0.07168-2.00704-0.98304-2.08896-1.5872-0.6656-8.84736-1.77152-17.792 1.35168-26.35264 2.75456-7.5776 9.58464-12.01664 17.61792-12.16a19.99872 19.99872 0 0 1 19.32288 14.336c2.30912 8.2688-1.55648 17.42336-8.69888 22.15424z" fill="#FF2E4D" p-id="3700"></path></svg>`,
                input: "帮我生成一篇小红书文案",
                prompt: `
                    <<|>>
                    你的任务是生成一篇关于指定主题的小红书文案。请仔细阅读以下主题，并按照指示撰写文案。
                    主题:
                    <theme>
                    {{THEME}}
                    </theme>
                    在撰写小红书文案时，请遵循以下指南:
                    1. 标题需根据主题改编为小红书风格，适当使用 emoji 来增强视觉吸引力和情感表达。
                    2. 文案整体语言风格要活泼、生动，符合小红书平台的调性。
                    3. 内容要围绕主题展开，有一定的实用性、趣味性或独特性。
                    4. 确保文案没有任何拼写或语法错误。
                    请写下你的文案。
                `,
                list: [
                    "帮我生成一篇关于<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>   的小红书文案。文案需要包含标题和正文标题需根据主题改编为小红书风格，适当使用表情来增强视觉吸引力和情感表达。 ",
                    "帮我生成一篇关于<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">兔子</span>   的小红书文案。文案需要包含标题和正文标题需根据主题改编为小红书风格，适当使用表情来增强视觉吸引力和情感表达。 ",
                ],
            },
            {
                label: "取名字",
                description: "好名传天下 🌟",
                icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-md" style="color: rgb(226, 197, 65);"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3C8.41496 3 5.5 5.92254 5.5 9.53846C5.5 11.8211 6.662 13.8298 8.42476 15H15.5752C17.338 13.8298 18.5 11.8211 18.5 9.53846C18.5 5.92254 15.585 3 12 3ZM14.8653 17H9.13473V18H14.8653V17ZM13.7324 20H10.2676C10.6134 20.5978 11.2597 21 12 21C12.7403 21 13.3866 20.5978 13.7324 20ZM8.12601 20C8.57004 21.7252 10.1361 23 12 23C13.8639 23 15.43 21.7252 15.874 20C16.4223 19.9953 16.8653 19.5494 16.8653 19V16.5407C19.0622 14.9976 20.5 12.4362 20.5 9.53846C20.5 4.82763 16.6992 1 12 1C7.30076 1 3.5 4.82763 3.5 9.53846C3.5 12.4362 4.93784 14.9976 7.13473 16.5407V19C7.13473 19.5494 7.57774 19.9953 8.12601 20Z" fill="currentColor"></path></svg>`,
                before: "帮我取一个名字， ",
                after: " 内容不要太长",
                input: "我需要",
                prompt: `
                    <<|>>
                    你的任务是根据给定的信息为某事物起名字。请仔细阅读以下内容，并按照指示进行操作。
                    名字类型:
                    <name_type>
                    {{NAME_TYPE}}
                    </name_type>
                    需要体现的关键特征:
                    <key_features>
                    {{KEY_FEATURES}}
                    </key_features>
                    在起名字时，请遵循以下指南:
                    1. 名字要与给定的名字类型相符合。
                    2. 充分体现关键特征，让名字具有代表性和独特性。
                    3. 尽量简洁易记，避免生僻字和复杂的组合。
                    4. 确保名字没有歧义或不良含义。
                    请写下你起的名字。
                `,
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>中英文名需谐音呼应",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">为跨境电商平台命名</span>中英文名需谐音呼应",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">为AI医疗科技公司命名</span>，要求三字中文",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">为儿童教育科技产品命名</span>，要求包含'鹿'或'星'元",
                ],
            },
            {
                label: "活动策划",
                description: "制定高效的营销推广方案",
                icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-md" style="color: rgb(203, 139, 208);"><path d="M3 6H10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"></path><path d="M3 10H7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"></path><path d="M13.4282 17.5718L20.5 10.5C21.6046 9.39543 21.6046 7.60457 20.5 6.5C19.3954 5.39543 17.6046 5.39543 16.5 6.5L9.42819 13.5718C9.14899 13.851 8.95868 14.2066 8.88124 14.5938L8 19L12.4062 18.1188C12.7934 18.0413 13.149 17.851 13.4282 17.5718Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path></svg>`,
                before: "帮我写一个",
                after: " 的方案，需要包含但不限于策划目标、详细计划、所需资源和预算、效果评估、风险应对等。",
                input: "帮我写",
                prompt: `
                    <<|>>
                    你的任务是起草一篇详细的活动策划方案。请仔细阅读以下信息，并按照指示撰写方案。
                    在撰写活动策划方案时，请遵循以下指南:
                    1. 使用清晰、有条理的语言，确保方案具有可操作性。
                    2. 方案应包含活动主题、活动时间、活动地点、参与人员、活动流程、宣传推广、预算安排等基本要素。
                    3. 活动流程要详细且合理，每个环节的时间安排要明确。
                    4. 宣传推广部分要说明具体的宣传渠道和方式。
                    5. 预算安排要根据给定的预算进行合理分配，列出各项费用的明细。
                    6. 方案整体要围绕活动目标和活动类型展开，确保方案的针对性和有效性。
                    请写下你的活动策划方案。
                `,
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">周末郊游</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">团建</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">品牌升级</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">宴会菜单</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">新的锻炼</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">品牌活动</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">下次假期出行计划</span>",
                ],
            },
            {
                label: "给我惊喜",
                description: "科技织星宴，忆梦享清闲🌌✨",
                icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-md" style="color: rgb(118, 208, 235);"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.9969 3.39017C14.5497 2.17402 15.961 1.60735 17.2013 2.10349L19.4044 2.98475C20.7337 3.51645 21.3458 5.05369 20.7459 6.35358L19.0629 10H20C20.5523 10 21 10.4477 21 11V19C21 20.6569 19.6569 22 18 22H6C4.34315 22 3 20.6569 3 19V11C3 10.4504 3.44331 10.0044 3.99183 10L3.84325 9.89871C3.83307 9.89177 3.82303 9.88465 3.81311 9.87733C2.55917 8.9526 2.79737 7.01262 4.23778 6.41871L6.35774 5.5446L7.08184 3.36883C7.57382 1.8905 9.49246 1.51755 10.5024 2.70393L11.9888 4.45002L13.5103 4.46084L13.9969 3.39017ZM15.5096 4.89554C16.2552 5.48975 16.5372 6.59381 15.9713 7.51403L14.8266 9.37513C14.8265 9.38763 14.8266 9.40262 14.8273 9.42012C14.8294 9.47125 14.8357 9.52793 14.8451 9.58262C14.8548 9.63855 14.8654 9.67875 14.8714 9.69773C14.9032 9.79819 14.9184 9.89994 14.9184 10H16.8602L18.93 5.51547C19.0499 5.25549 18.9275 4.94804 18.6617 4.8417L16.4585 3.96044C16.2105 3.86122 15.9282 3.97455 15.8176 4.21778L15.5096 4.89554ZM12.8885 10C12.8572 9.84122 12.8358 9.66998 12.8289 9.50115C12.8194 9.26483 12.8254 8.81125 13.0664 8.41953L14.2677 6.46628L11.9746 6.44997C11.3934 6.44584 10.8427 6.18905 10.4659 5.74646L8.97951 4.00037L8.25541 6.17614C8.07187 6.72765 7.65748 7.17203 7.12012 7.39359L5.00091 8.26739L7.06338 9.67378C7.19188 9.7614 7.29353 9.87369 7.3663 10H12.8885ZM5 12V19C5 19.5523 5.44772 20 6 20H18C18.5523 20 19 19.5523 19 19V12H5ZM9.5 14.5C9.5 13.9477 9.94771 13.5 10.5 13.5H13.5C14.0523 13.5 14.5 13.9477 14.5 14.5C14.5 15.0523 14.0523 15.5 13.5 15.5H10.5C9.94771 15.5 9.5 15.0523 9.5 14.5Z" fill="currentColor"></path></svg>`,
                after: " 给我惊喜",
                input: "给我惊喜",
                prompt: `
                    <<|>>
                    你的任务是给用户一个惊喜。为了能更好地完成这个任务，我会提供一些你需要考虑的信息。
                    在制造惊喜时，请遵循以下要求：
                    1. 惊喜内容要紧密结合用户的偏好。
                    2. 尽量发挥创意，让惊喜独特且有趣。
                    3. 确保惊喜内容积极正面，符合公序良俗。
                    请写下能给用户带来惊喜的内容。
                    [在此写下给用户的惊喜内容]
                `,
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">用来自《老农年鉴》的提示信息</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">用我的季节性色彩分析</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">用我今天的星座运势</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">讲讲你自己的故事，</span>",
                ],
            },
        ]
    },
    {
        title: "生活与金融",
        list: [
            {
                label: "个股专家",
                description: "数赋股策，风控稳资📈",
                icon: `<svg t="1741229409021" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10114" width="16" height="16"><path d="M192.682417 617.135502c10.498098 8.652054 23.62507 13.31833 37.040614 13.31833 2.300392 0 4.601808-0.1361 6.901177-0.407276 15.794731-1.89721 30.119995-10.181897 39.748283-22.918989L396.811538 447.300442l119.918183 101.526301c12.158924 10.277064 27.935236 15.387455 43.767829 13.491269 15.813151-1.664919 30.273491-9.69685 40.055275-22.317285l275.42491-354.942965c2.010797-2.593058 3.596921-5.380544 5.124717-8.16803l48.350195-57.257052c25.903973-30.679743 14.769378-50.598398-24.746614-44.248783l-227.867778 36.583197c-39.534412 6.349615-47.749514 33.8172-18.268062 61.070914l45.565778 42.100864L545.629935 419.407162l-120.55468-102.08605c-12.275581-10.336416-28.14706-15.21554-44.192501-13.452383-15.930831 1.780553-30.448476 10.083659-40.134069 22.955828l-120.960932 160.526043-58.093093-47.792493c-25.01472-20.595061-61.901839-16.917298-82.35466 8.284687-20.452821 25.163099-16.780175 62.250786 8.255011 82.807984L192.682417 617.135502z" fill="#F45944" p-id="10115"></path><path d="M878.626052 349.259522 610.025571 676.327626c-12.063757 15.291265-42.744523-6.658653-57.551764-19.240203l-107.951641-94.964862c-14.808264-12.580526-37.000706 8.710382-49.315172 23.809265l-125.447107 153.884786c-12.294001 15.098883-34.411741-2.206248-49.103348-14.924921l-109.457948-87.046518c-19.641339-18.891256-38.375006 0.813528-38.375006 38.48143l0.676405 233.131665c0.037862 19.550265 15.928784 35.538401 35.280528 35.557844l810.558793 0.639566c19.371186 0.01842 35.20378-15.969717 35.20378-35.519981 0 0 0.811482-503.446183 0.811482-541.656437C955.37504 317.243341 914.912489 307.410392 878.626052 349.259522z" fill="#F45944" p-id="10116"></path></svg>`,
                prompt: `
                    <<|>>
                    你是一位专业的股票专家，负责对给定的股票信息进行详细分析。你的任务是仔细研究股票的相关信息，并提供全面、深入的分析。
                    以下是你需要分析的股票信息：
                    在分析时，请考虑以下几个重要方面：
                    1. 基本面分析：包括公司的财务状况、行业地位、竞争优势等。
                    2. 技术面分析：查看股票的价格走势、成交量、均线等技术指标。
                    3. 市场环境分析：评估当前宏观经济环境、行业趋势对该股票的影响。
                    4. 风险评估：指出投资该股票可能面临的风险。
                    5. 投资建议：基于以上分析，给出是否适合投资该股票的建议。
                    请在写下你的分析内容，确保分析内容丰富、全面、有条理。
                    [在此进行详细的股票分析]
                `,
                before: `请分析 `,
                after: ` 的股票趋势`,
                input: `请分析`,
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[股票名称/代码]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">特斯拉</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">英伟达</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">阿里巴巴</span>",
                ],
            },
            {
                label: "问问法律",
                description: "知法明法界 ⚖️",
                icon: `<svg t="1741229481465" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11317" width="16" height="16"><path d="M945.4 554l0.1-0.1c0.4-0.5 0.7-1 1-1.5 0-0.1 0.1-0.1 0.1-0.2 0.3-0.5 0.6-1 0.9-1.6 1.2-2.2 2-4.6 2.5-7.2v-0.1c0.1-0.6 0.2-1.2 0.2-1.8v-0.1c0-0.6 0.1-1.1 0.1-1.7v-0.3-0.2c0-0.5 0-1-0.1-1.5v-0.2c-0.2-2.8-0.9-5.5-2-8L800.7 175.1c-3.8-9.1-12.7-15-22.6-15h-0.1c-0.8 0-1.6 0.1-2.4 0.1-2.3-0.7-4.8-1.1-7.3-1.1H260.8c-3.1 0-6.1 0.6-8.9 1.6-1.8-0.4-3.7-0.7-5.6-0.7h-0.1c-9.9 0-18.8 5.9-22.7 14.9L73.6 529.7c-0.6 1.3-1 2.6-1.3 4v0.1c-0.3 1.2-0.5 2.4-0.6 3.6v0.3c0 0.5-0.1 1-0.1 1.6v0.4c0 0.5 0 1.1 0.1 1.6v0.2c0.1 0.6 0.1 1.2 0.2 1.8 0.4 2.6 1.3 5 2.5 7.2 0.3 0.5 0.6 1.1 0.9 1.6 0 0.1 0.1 0.1 0.1 0.2 0.3 0.5 0.7 1 1 1.5 0 0 0 0.1 0.1 0.1 0.3 0.5 0.7 0.9 1.1 1.3l0.2 0.2c0.4 0.4 0.8 0.9 1.2 1.3l0.2 0.2c0.4 0.4 0.9 0.8 1.4 1.2l148.9 119.2c4.5 3.6 10 5.4 15.4 5.4 5.5 0 10.9-1.8 15.4-5.4l148.9-119.2c0.5-0.4 0.9-0.8 1.4-1.2l0.2-0.2 1.2-1.2c0.1-0.1 0.1-0.2 0.2-0.2 0.4-0.4 0.7-0.8 1-1.2 0-0.1 0.1-0.1 0.1-0.2 0.3-0.5 0.7-0.9 1-1.4 0.1-0.1 0.1-0.2 0.2-0.3l0.9-1.5s0-0.1 0.1-0.1c0.6-1.1 1.1-2.2 1.5-3.4v-0.1c0.4-1.2 0.7-2.3 0.9-3.6v-0.2c0.1-0.6 0.2-1.2 0.2-1.8v-0.2c0-0.5 0.1-1 0.1-1.5v-0.4-0.2c0-0.5 0-1-0.1-1.5v-0.3c0-0.6-0.1-1.1-0.2-1.7-0.1-0.6-0.2-1.3-0.4-1.9v-0.1c-0.3-1.4-0.8-2.9-1.4-4.2L282.5 207.7h207.4v606.5H319.3c-13.6 0-24.6 10.9-24.6 24.3s11 24.3 24.6 24.3h390.3c13.6 0 24.6-10.9 24.6-24.3s-11-24.3-24.6-24.3H539V207.7h202.4l-136.1 322c-0.6 1.3-1 2.6-1.3 4v0.1c-0.3 1.2-0.5 2.4-0.6 3.6v0.3c0 0.5-0.1 1-0.1 1.6v0.4c0 0.6 0 1.1 0.1 1.6v0.2c0.1 0.6 0.1 1.2 0.2 1.8 0.4 2.6 1.3 5 2.5 7.2 0.3 0.5 0.6 1.1 0.9 1.6 0 0.1 0.1 0.1 0.1 0.2 0.3 0.5 0.7 1 1 1.5v0.1c0.4 0.5 0.7 0.9 1.1 1.4l0.2 0.2c0.4 0.4 0.8 0.9 1.3 1.3l0.2 0.2c0.5 0.4 0.9 0.8 1.4 1.2l148.9 119.2c4.5 3.6 10 5.4 15.4 5.4 5.5 0 10.9-1.8 15.4-5.4l148.9-119.2c0.5-0.4 1-0.8 1.4-1.2l0.2-0.2c0.4-0.4 0.8-0.8 1.2-1.3l0.2-0.2c0.8-0.4 1.1-0.8 1.5-1.3z m-56.3-39H664.8l112.9-267.2L889.1 515zM246 247.8L357.4 515H133.1L246 247.8z m-0.9 379.4l-79.5-63.6h159l-79.5 63.6z m531.8 0l-79.5-63.6h159l-79.5 63.6z" fill="#5F9BEB" p-id="11318"></path></svg>`,
                prompt: `
                    <<|>>
                    你的任务是根据提供的法律文档回答相关法律问题。请仔细阅读以下信息，并按照指示完成任务。
                    首先，请仔细阅读相关法律文档：
                    <法律文档>
                    {{LAW_DOCUMENT}}
                    </法律文档>
                    现在，这里有一个问题：
                    <问题>
                    {{QUESTION}}
                    </问题>
                    在回答问题时，请遵循以下步骤：
                    1. 仔细阅读法律文档和问题。
                    2. 找出法律文档中与问题相关的内容。
                    3. 如果有相关内容，准确引用这些内容，并按编号顺序列出。引用应该相对简短。如果没有相关引用，请写“没有相关引用”。
                    4. 根据引用内容回答问题，以“回答：”开头。不要在回答中逐字引用或参考引用内容。在回答时，不要说“根据引用[1]”，而是仅在相关句子末尾添加相应的括号编号，以表明引用内容。
                    请按照以下格式输出：
                    [1] “具体引用内容” 
                    回答：具体回答内容
                    如果法律文档不能回答问题，请说明。
                    立即回答问题，无需前言。
                `,
                before: "请分析 ",
                after: ` 的法律咨询`,
                input: "请分析",
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">劳动法律中关于加班工资的规定是怎样的？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">我在签订合同时遇到了一些问题，能帮我解答一下吗？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">如果遇到消费纠纷，我应该如何维权？</span>",
                ],
            },
            {
                label: "置业顾问",
                description: "专顾智选优房，无忧安家之选🏠",
                icon: `<svg t="1741229507379" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12453" width="16" height="16"><path d="M693.956633 911.308405 693.956633 326.005912l219.257679 0 0 583.185272L693.956633 909.191184 693.956633 911.308405 693.956633 911.308405 693.956633 911.308405zM108.649024 911.308405 108.649024 106.748233l510.849804 0 0 802.442952L108.649024 909.191184 108.649024 911.308405 108.649024 911.308405 108.649024 911.308405zM985.549781 911.308405 985.549781 253.543553 693.956633 253.543553 693.956633 34.412764 36.190759 34.412764l0 874.777397L0.020978 909.190161l0 72.335469 1021.699608 0 0-72.335469L985.549781 909.190161 985.549781 911.308405 985.549781 911.308405 985.549781 911.308405zM402.364509 581.428256l146.796344 0 0 108.507296L402.364509 689.935552 402.364509 581.428256 402.364509 581.428256 402.364509 581.428256zM183.10683 581.428256l146.795321 0 0 108.507296L183.10683 689.935552 183.10683 581.428256 183.10683 581.428256 183.10683 581.428256zM766.294149 581.428256l72.335469 0 0 108.507296-72.335469 0L766.294149 581.428256 766.294149 581.428256 766.294149 581.428256zM766.294149 400.463718l72.335469 0 0 108.506273-72.335469 0L766.294149 400.463718 766.294149 400.463718 766.294149 400.463718zM402.364509 400.463718l146.796344 0 0 108.506273L402.364509 508.96999 402.364509 400.463718 402.364509 400.463718 402.364509 400.463718zM183.10683 400.463718l146.795321 0 0 108.506273L183.10683 508.96999 183.10683 400.463718 183.10683 400.463718 183.10683 400.463718zM402.364509 217.499639l146.796344 0 0 108.506273L402.364509 326.005912 402.364509 217.499639 402.364509 217.499639 402.364509 217.499639zM183.10683 217.499639l146.795321 0 0 108.506273L183.10683 326.005912 183.10683 217.499639 183.10683 217.499639 183.10683 217.499639z" fill="#d81e06" p-id="12454"></path></svg>`,
                prompt: `
                    <<|>>
                    你将扮演一名专业的置业顾问，根据客户的需求为其推荐合适的房产，并提供相关建议。
                    首先，请仔细阅读以下房产信息：
                    <房产信息>
                    {{PROPERTY_INFO}}
                    </房产信息>
                    接着，请了解客户的需求：
                    <客户需求>
                    {{CLIENT_REQUIREMENTS}}
                    </客户需求>
                    在为客户推荐房产时，请遵循以下指南：
                    1. 详细分析客户需求，包括购房预算、房屋类型、地段偏好、房屋面积等。
                    2. 从房产信息中筛选出符合客户需求的房产。
                    3. 对筛选出的房产进行评估，考虑房屋性价比、周边配套设施、发展潜力等因素。
                    4. 给出推荐的房产，并详细说明推荐理由。
                    5. 提供其他相关建议，如购房流程、贷款政策等。
                    请在写下推荐的房产及理由，其他相关建议。
                    [在此写下推荐的房产及理由]
                    [在此写下其他相关建议]
                `,
                before: "请分析 ",
                after: ` 的置业咨询`,
                input: "请分析",
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">首次购房应该注意哪些事项？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">如何评估一个楼盘的投资价值？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">贷款购房有哪些流程和注意事项？</span>",
                ],
            },
            {
                label: "车达人",
                description: "专业汽车选购、保养、维修指南🚗",
                icon: `<svg t="1741229544726" class="icon" viewBox="0 0 1341 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13651" width="16" height="16"><path d="M454.697248 519.124155c-57.040854 0-93.812391-55.97739-81.728048-111.727273l49.379677-201.767274c12.084344-55.749882 68.654311-101.362461 125.695165-101.36246h458.072822c57.035564 0 110.690262 46.146956 119.20327 102.547615l34.057321 199.386383c8.518298 56.400659-31.18438 112.912427-88.219943 112.912427l-616.460264 0.010582z" fill="#FFE35F" p-id="13652"></path><path d="M454.697248 500.981766c-21.98885 0-40.68678-8.174392-52.665306-23.015278-11.967945-14.840886-15.989005-45.226345-11.333041-66.723144l49.268568-201.296387c10.338357-47.654853 59.776233-87.526839 108.065992-87.526839h458.072821c47.750089 0 94.129843 39.893149 101.267225 87.10886l34.099649 199.73029c3.264466 21.62378-2.179838 52.554198-15.391137 67.903008-13.206008 15.354101-32.724022 23.808909-54.935089 23.808908l-616.449682 0.010582z m616.460264 36.295359c32.956821 0 62.247069-12.936174 82.452896-36.438212 20.205827-23.496747 28.62889-64.760232 23.697801-97.346692-0.052909-0.343906-34.104939-199.73029-34.104939-199.730289-9.777525-64.712614-71.305036-117.642463-137.091697-117.642464h-458.072822c-65.257573 0-129.610408 51.887548-143.430156 115.663679l-49.268569 201.296387c-7.147963 32.903912-0.55025 74.109197 18.438677 97.669434 18.994218 23.549656 47.734217 36.522866 80.918545 36.522866l616.460264 0.005291z" fill="#6E6E96" p-id="13653"></path><path d="M189.100935 830.814515s-55.480048-0.126981-78.923887-0.290998c-23.443839-0.174599-42.459221-21.369818-42.27404-47.141639l0.804212-171.656941 0.380942-26.914649c0-50.13098 41.11534-81.749211 93.473776-93.616629l188.413122-39.305863 823.888767-0.00529c24.274505 8.629407 102.526452 10.206086 102.526452 108.145354l1.412662 230.708325c0.222216 25.777112-20.676714 46.686625-46.432663 46.533189l-47.316238-0.328033" fill="#FFE35F" p-id="13654"></path><path d="M134.843077 671.167846l0.375652-51.681205c0-49.014607 40.20002-54.003896 91.394464-65.606771l166.852832-34.803333 823.04752-3.640118c8.354282 2.973468 23.26924 5.105688 39.088937 10.788081-17.803773-57.241907-74.939863-59.485236-94.870565-66.569709l-805.645853 0.010582-184.243917 38.438161c-51.205026 11.608166-91.399755 42.533293-91.399755 91.542608l-0.380942 25.750658-0.783049 167.858097c-0.169308 25.195116 18.417514 45.93003 41.337556 46.099338l14.475817 0.089945 0.751303-158.276334z" fill="#FFFFFF" p-id="13655"></path><path d="M1267.030758 570.397962c0-97.939269-78.251947-99.515948-102.526451-108.134772H340.610248L152.197126 501.574343C99.838691 513.447053 58.728641 545.070574 58.728641 595.185682l-0.380942 26.914648-0.804212 171.656941c-0.17989 25.771821 18.846074 46.977622 42.27404 47.141639 23.44913 0.153435 78.923887 0.296289 78.923887 0.296289l995.947815 6.121535 47.310947 0.333325a46.136374 46.136374 0 0 0 46.437953-46.538481l-1.407371-230.713616z m-118.748255 199.465746l-43.157615-0.269834-908.516211-4.888762s-50.607158-0.111108-71.992849-0.232799c-21.385691-0.126981-38.729158-17.063051-38.570432-37.623366l0.740722-111.108241 0.354488-42.194677c0-40.014839 37.491095-65.252282 85.262347-74.733519l171.873867-31.380142 751.567885-0.005291c22.147576 6.88342 93.521393 8.147938 93.521393 86.325813l1.285681 178.947757c0.190471 20.597351-18.861947 37.284751-42.369276 37.163061z" fill="#EDCA4C" p-id="13656"></path><path d="M189.143262 812.672126l-78.839233-0.296288c-13.253626-0.111108-24.359159-13.317116-24.258633-28.861689l0.809503-171.688686 0.375652-26.877612c0-52.411344 55.485339-70.643678 79.331283-76.045655l188.116834-39.242372a18.237624 18.237624 0 0 1-3.708899 0.386233l823.888767-0.00529c-2.07402 0-4.126878-0.354488-6.068626-1.047592 4.50782 1.603133 9.68229 2.820033 15.682135 4.237985 31.55474 7.438961 74.765264 17.623883 74.765264 86.801991l1.412662 230.824724a27.861714 27.861714 0 0 1-8.16381 20.068265 27.829969 27.829969 0 0 1-20.015356 8.216719l-47.289784-0.328034a18.147679 18.147679 0 1 0-0.253962 36.295358l47.305657 0.328034a63.823748 63.823748 0 0 0 45.956484-18.888401 63.839621 63.839621 0 0 0 18.766711-45.956484l-1.412662-230.660708c0-97.82287-71.987558-114.790685-102.738086-122.039175-4.745909-1.116373-9.227275-2.179838-11.856837-3.111031a18.147679 18.147679 0 0 0-6.073917-1.047592H350.985641c-1.248645 0-2.486708 0.126981-3.708899 0.380943l-188.413121 39.311153C91.182829 488.770441 50.951065 530.388414 50.951065 584.810288l-0.380943 26.782377-0.809503 171.699268c-0.24338 35.792726 26.80354 65.141174 60.30003 65.373972l79.00854 0.290998a18.137098 18.137098 0 1 0 0.074073-36.284777z" fill="#6E6E96" p-id="13657"></path><path d="M461.390196 852.067934h395.841626a18.147679 18.147679 0 1 0 0-36.30065H461.390196a18.147679 18.147679 0 1 0 0 36.30065zM350.985641 451.887796h36.30065v245.083616h-36.30065z" fill="#6E6E96" p-id="13658"></path><path d="M129.218884 764.525222s25.570768-139.657768 188.921045-139.657768c163.355568 0 202.248743 141.625971 202.248743 141.625971l-391.169788-1.968203z" fill="#EDCA4C" p-id="13659"></path><path d="M481.908185 697.378809h367.900548v52.517161H481.908185z" fill="#EDCA4C" p-id="13660"></path><path d="M808.55054 769.710273s35.94087-150.02787 199.291147-150.02787c197.058401 0 191.889222 151.996073 191.889223 151.996073l-391.18037-1.968203z" fill="#EDCA4C" p-id="13661"></path><path d="M1310.955549 832.163686c0 14.2536-11.666365 25.930547-25.919965 25.930547H68.093479c-14.258891 0-25.925256-11.676947-25.925256-25.930547v-51.035718c0-14.269473 11.666365-25.935838 25.925256-25.935838h1216.931523c14.2536 0 25.919965 11.666365 25.919966 25.935838l0.010581 51.035718z" fill="#F0F0FF" p-id="13662"></path><path d="M1292.802579 832.163686a7.883394 7.883394 0 0 1-7.777577 7.782868H68.093479a7.883394 7.883394 0 0 1-7.782868-7.782868v-51.035718c0-4.222113 3.571336-7.788159 7.782868-7.788159h1216.931523c4.211531 0 7.777577 3.566045 7.777577 7.788159v51.035718z m36.30065-51.030427c0-24.300959-19.777267-44.083517-44.078227-44.083518H68.093479c-24.30625 0-44.078226 19.782558-44.078226 44.083518v51.035718c0 24.311541 19.771976 44.078226 44.078226 44.078226h1216.931523c24.300959 0 44.078226-19.766685 44.078227-44.078226v-51.035718z" fill="#6E6E96" p-id="13663"></path><path d="M42.162932 809.65104h1261.231966v36.290068H42.162932z" fill="#6E6E96" p-id="13664"></path><path d="M326.150304 832.417648m-137.710728 0a137.710728 137.710728 0 1 0 275.421456 0 137.710728 137.710728 0 1 0-275.421456 0Z" fill="#F0F0FF" p-id="13665"></path><path d="M326.150304 725.827809c58.776259 0 106.600421 47.824161 106.600421 106.589839a105.939062 105.939062 0 0 1-31.221416 75.379005 105.886153 105.886153 0 0 1-75.384296 31.210834c-58.765678 0-106.589839-47.81358-106.589839-106.589839 0-58.765678 47.81887-106.589839 106.59513-106.589839z m-168.821035 106.589839c0 93.092833 75.728202 168.821035 168.826326 168.821035 93.103415 0 168.831617-75.728202 168.831617-168.821035 0-93.098124-75.728202-168.826326-168.831617-168.826326-93.098124 0-168.826326 75.738784-168.826326 168.826326z" fill="#6E6E96" p-id="13666"></path><path d="M320.483784 780.122703c53.443064 0 98.293757 36.708046 110.764334 86.278194 2.259201-8.95215 3.454937-18.306405 3.454937-27.962241 0-63.093608-51.136245-114.229853-114.219271-114.229853-63.088317 0-114.240435 51.136245-114.240435 114.229853 0 9.645253 1.211609 19.010091 3.465519 27.962241 12.475868-49.570148 57.331852-86.278194 110.774916-86.278194z" fill="#6E6E96" p-id="13667"></path><path d="M323.32498 834.332942m-37.422313 0a37.422314 37.422314 0 1 0 74.844627 0 37.422314 37.422314 0 1 0-74.844627 0Z" fill="#6E6E96" p-id="13668"></path><path d="M1020.402209 832.417648m-137.710728 0a137.710728 137.710728 0 1 0 275.421456 0 137.710728 137.710728 0 1 0-275.421456 0Z" fill="#F0F0FF" p-id="13669"></path><path d="M1020.402209 725.827809c58.770968 0 106.600421 47.824161 106.600421 106.589839a105.939062 105.939062 0 0 1-31.221416 75.379005 105.875572 105.875572 0 0 1-75.379005 31.210834c-58.765678 0-106.589839-47.81358-106.589838-106.589839-0.010582-58.765678 47.808289-106.589839 106.589838-106.589839z m-168.831617 106.589839c0 93.092833 75.722911 168.821035 168.821036 168.821035 93.103415 0 168.831617-75.728202 168.831617-168.821035 0-93.098124-75.728202-168.826326-168.831617-168.826326-93.092833 0-168.821035 75.738784-168.821036 168.826326z" fill="#6E6E96" p-id="13670"></path><path d="M1014.730398 780.122703c53.448355 0 98.299048 36.708046 110.769625 86.278194 2.259201-8.95215 3.449646-18.306405 3.449647-27.962241 0-63.093608-51.130954-114.229853-114.219272-114.229853-63.083026 0-114.235144 51.136245-114.235144 114.229853 0 9.645253 1.211609 19.010091 3.460228 27.962241 12.475868-49.570148 57.337143-86.278194 110.774916-86.278194z" fill="#6E6E96" p-id="13671"></path><path d="M1017.566304 834.332942m-37.422314 0a37.422314 37.422314 0 1 0 74.844627 0 37.422314 37.422314 0 1 0-74.844627 0Z" fill="#6E6E96" p-id="13672"></path><path d="M761.520009 458.628363h36.295358v279.908112h-36.295358zM669.040916 540.742644h51.855804v36.295358h-51.855804z" fill="#6E6E96" p-id="13673"></path><path d="M421.449429 518.944265v129.494009l-34.168429-13.862076V520.3728z" fill="#FFFFFF" p-id="13674"></path><path d="M514.976114 384.11706c-41.713208 0-42.676146-34.205466-33.840396-76.008618l10.179631-55.691683c11.772183-55.649356 47.289784-76.024491 91.928842-76.024491h388.090503c55.210214 0 75.89751 34.612862 82.119572 76.913357l9.354255 53.903369c6.232643 42.300494 2.867651 76.908066-38.850848 76.908066h-508.981559z" fill="#AADBF7" p-id="13675"></path><path d="M514.976114 365.96409c-8.121483 0-13.555205-1.624297-16.168895-4.841144-3.740644-4.613637-6.756439-16.904325 0.089945-49.263278 0.100527-0.492051 10.264285-56.183734 10.264285-56.183734 8.793424-41.554482 32.358952-61.135987 74.07216-61.135986h388.101085c38.115417 0 57.892684 18.94131 64.162363 61.405821 0.084654 0.449724 9.417746 54.353093 9.417747 54.353093 3.809425 25.941129 2.634853 42.983017-3.5925 50.183888-1.169282 1.349171-4.730037 5.476049-17.375213 5.476049l-508.970977 0.005291z m508.970977 36.30065c19.422779 0 34.507045-6.073917 44.861275-18.057735 17.930754-20.766659 15.798534-53.474809 11.946781-79.648736l-9.412455-54.347802c-11.174314-75.934546-60.559282-91.965878-99.997416-91.965878h-388.101085c-58.395317 0-97.346691 32.110282-109.674415 90.415654-0.105817 0.497342-10.274867 56.183734-10.274867 56.183733-5.417849 25.618386-9.587054 58.268336 7.338435 79.146104 9.825143 12.126671 24.761265 18.279951 44.358642 18.279951l508.955105-0.005291z" fill="#6E6E96" p-id="13676"></path><path d="M760.647015 176.392268h36.30065V384.11706h-36.30065z" fill="#6E6E96" p-id="13677"></path><path d="M513.161346 241.115464h519.346371l-22.941207-57.517033h-467.003808z" fill="#6E6E96" p-id="13678"></path><path d="M796.947665 241.115464h44.078226v124.848626h-44.078226z" fill="#6E6E96" p-id="13679"></path><path d="M605.074315 282.415984h-72.596008l5.185051-38.893175h67.410957z" fill="#FFFFFF" p-id="13680"></path><path d="M842.756005 243.522809h67.410957v38.893175h-67.410957z" fill="#FFFFFF" p-id="13681"></path><path d="M69.220434 653.95665H139.361479v101.124372H69.220434z" fill="#FFFFFF" p-id="13682"></path><path d="M139.361479 773.599062H69.220434a18.51804 18.51804 0 0 1-18.51804-18.51804v-101.124372a18.51804 18.51804 0 0 1 18.51804-18.51804H139.361479a18.51804 18.51804 0 0 1 18.51804 18.51804v101.124372a18.51804 18.51804 0 0 1-18.51804 18.51804z m-51.623005-37.036081h33.104965v-64.088291h-33.104965v64.088291z" fill="#6E6E96" p-id="13683"></path><path d="M1220.455242 643.591838h59.63338v111.489184h-59.63338z" fill="#FF5F5F" p-id="13684"></path><path d="M1280.088622 773.599062h-59.63338a18.51804 18.51804 0 0 1-18.51804-18.51804v-111.489184a18.51804 18.51804 0 0 1 18.51804-18.51804h59.63338a18.51804 18.51804 0 0 1 18.51804 18.51804v111.489184c0 10.221958-8.296082 18.51804-18.51804 18.51804z m-41.11534-37.036081h22.5973v-74.453102h-22.5973v74.453102z" fill="#6E6E96" p-id="13685"></path><path d="M1083.802688 540.742644h51.861094v36.295358h-51.861094z" fill="#6E6E96" p-id="13686"></path></svg>`,
                prompt: `
                    <<|>>
                    你将扮演一位车达人，专门为用户解答关于汽车的各种问题。
                    首先，请仔细阅读以下汽车相关信息：
                    <car_info>
                    {{CAR_INFO}}
                    </car_info>
                    这是用户提出的问题：
                    <question>
                    {{QUESTION}}
                    </question>
                    在回答问题时，请遵循以下规则：
                    - 仅依据提供的汽车相关信息来回答问题。如果问题超出了信息范围，请说：“抱歉，根据提供的信息，我无法回答这个问题。”
                    - 保持回答准确、清晰、简洁。
                    - 若需要引用信息中的内容来回答问题，先准确引用相关内容，然后给出回答。引用内容需相对简短。
                    [在此准确引用汽车相关信息中与问题相关的内容]
                    [在此给出回答]
                `,
                before: "请分析 ",
                after: ` 的车相关咨询`,
                input: "请分析",
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">油车跟电车怎么选，各有何优缺点？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">SUV和轿车怎么选？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">怎么跟车上说才能拿到最低价？</span>",
                ],
            },
            {
                label: "情感专家",
                description: "AI 情感智析专家，全维服务赋能决策💬",
                icon: `<svg t="1741229988070" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14779" width="16" height="16"><path d="M511.2832 510.1568m-450.816 0a450.816 450.816 0 1 0 901.632 0 450.816 450.816 0 1 0-901.632 0Z" fill="#FF5C64" p-id="14780"></path><path d="M755.3536 282.88c-103.8336-68.0448-188.0064-26.88-242.944 24.4224C457.4208 256 373.2992 214.8864 269.4144 282.88 201.1136 327.6288 171.7248 390.656 181.6064 463.5136h213.0944l43.9808-93.9008c4.7104-10.0864 13.9264-17.7152 24.8832-19.456 16.896-2.6624 32.3584 7.5776 37.0176 23.1424l39.68 132.864 13.5168-24.6272a33.05472 33.05472 0 0 1 28.928-17.152h98.816c18.0736 0 33.8432 13.9264 34.3552 32a32.99328 32.99328 0 0 1-32.9728 33.9968h-80.64l-42.0864 76.7488a33.02912 33.02912 0 0 1-40.96 14.8992c-9.7792-3.7888-16.9472-12.3904-19.9168-22.4256l-37.1712-124.416-16.5376 35.328a32.9728 32.9728 0 0 1-29.9008 18.9952H200.2432c36.8128 87.552 123.392 184.3712 260.1472 278.4768 15.2576 10.496 33.4336 16.0768 51.968 16.0768s36.7616-5.5808 52.0192-16.0768c293.9392-202.24 355.9424-417.024 190.976-525.1072z" fill="#FFFFFF" p-id="14781"></path></svg>`,
                before: "请分析 ",
                after: ` 的情感咨询`,
                input: "请分析",
                prompt: `
                    <<|>>
                    你将扮演一位专业的情感专家，为遇到情感问题的人提供分析和建议。请仔细阅读以下情感情境描述，并根据专业知识和经验给出恰当的建议。
                    情感情境描述:
                    <emotional_situation>
                    {{EMOTIONAL_SITUATION}}
                    </emotional_situation>
                    在分析和提供建议时，请遵循以下步骤：
                    1. 仔细阅读整个情感情境描述，理解其中的关键信息和情感冲突。
                    2. 分析导致当前情感问题的可能原因，考虑当事人的性格、经历、沟通方式等因素。
                    3. 思考可能的解决方案和应对策略，要具有可操作性和针对性。
                    4. 形成初步的分析和建议。
                    5. 再次检查，确保建议合理且能够帮助当事人改善情感状况。
                    首先，详细分析导致情感问题的原因以及你考虑建议的过程。然后给出具体的建议，建议要丰富、全面，尽量涵盖多个方面，以帮助当事人更好地处理情感问题。
                    [在此详细分析情感问题的原因和考虑建议的过程]
                    [在此给出具体的建议]
                `,
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">发现老公出轨了，该怎么办？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">夫妻没法沟通，常年冷暴力怎么办？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">想离婚离不了，也没法儿好好过日子，怎么办？</span>",
                ],
            },
        ]
    },
    {
        title: "AI 阅读",
        list: [
            {
                label: "PDF解析",
                description: "PDF 智析引擎，内容提取无忧📄",
                icon: `<svg t="1740727479375" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23142" width="14" height="14"><path d="M704 0H192c-35.328 0-64 28.672-64 64v320h576c35.328 0 64 28.672 64 64v415.744c0 35.328-28.672 64-64 64H128v31.744c0 35.328 28.672 64 64 64h768c35.328 0 64-28.672 64-64v-640L704 0z" fill="#EAEAEA" p-id="23143"></path><path d="M704 0v256c0 35.328 28.672 64 64 64h256L704 0z" fill="#434854" p-id="23144"></path><path d="M768 320l256 256V320z" opacity=".1" p-id="23145"></path><path d="M704 832c0 17.92-14.336 31.744-31.744 31.744H31.744C13.824 863.744 0 849.408 0 832V480.256c0-17.92 14.336-31.744 31.744-31.744h640c17.92 0 31.744 14.336 31.744 31.744V832z" fill="#CD4050" p-id="23146"></path><path d="M192 544.256h-48.128c-8.704 0-15.872 7.168-15.872 15.872v192c0 8.704 7.168 15.872 15.872 15.872s15.872-7.168 15.872-15.872v-79.872h31.744c35.328 0 64-28.672 64-64s-28.16-64-63.488-64z m158.72 0c34.816 0 62.976 27.648 64 62.464v97.792c0 34.816-27.648 62.976-62.464 64h-49.664c-8.704 0-15.36-6.656-15.872-14.848V560.64c0-8.704 6.656-15.36 14.848-15.872h49.152z m227.328 0c8.704 0 15.872 7.168 15.872 15.872S586.752 576 578.048 576h-80.384v64h48.128c8.704 0 15.872 7.168 15.872 15.872s-7.168 15.872-15.872 15.872h-48.128v79.872c0 8.704-7.168 15.872-15.872 15.872s-15.872-7.168-15.872-15.872v-192c0-8.704 7.168-15.872 15.872-15.872h96.256zM350.72 576h-31.744v159.744h31.744c17.408 0 31.232-13.824 31.744-30.72v-97.28C382.976 590.336 368.64 576 350.72 576zM192 576c17.92 0 31.744 14.336 31.744 31.744S209.92 640 192 640h-31.744v-64H192z" fill="#FFFFFF" p-id="23147"></path></svg>`,
                type: "popup-file",
                title: "上传PDF文件",
                fileType: ".pdf",
                placeholder: "智能提取pdf中的关键信息，轻松应对复杂的阅读需求，文件大小≤2M",
                input: `
                    <<|>>
                    你的任务是分析并概要一份PDF文件。请仔细阅读以下PDF文件的内容，并按照指示完成任务。
                    PDF文件内容:
                    <pdf_content>
                    {{PDF_CONTENT}}
                    </pdf_content>
                    在分析和概要时，请遵循以下指南:
                    1. 仔细阅读整个PDF文件内容，理解其核心主题和关键信息。
                    2. 识别PDF文件中的主要观点、重要事实和关键结论。
                    3. 提取重要信息时，关注内容的逻辑结构和连贯性。
                    4. 用简洁、清晰的语言概括内容，避免使用过于复杂的句子和术语。
                    5. 确保概要内容涵盖了PDF文件的主要内容，但不包含过多细节。
                    6. 对文件进行一定的分析，如指出文件的优点、不足、适用场景等。
                    请写下你的分析和概要结果。
                `,
                submitText: "开始分析",
            },
            {
                label: "Word解析",
                description: "Word 智析引擎，文档处理无忧📝",
                icon: `<svg t="1740727509531" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25996" width="14" height="14"><path d="M535.119473 0h69.599248v95.247413C729.226717 96.331138 853.614299 93.92286 977.881468 96.331138a40.459078 40.459078 0 0 1 44.914393 45.516463c2.047037 234.566322 0 469.614299 1.204139 703.819379-1.204139 24.082785 2.287865 50.694262-11.318909 72.248354-16.978363 12.041392-38.893697 10.837253-58.761994 12.041392h-349.200376V1023.518344h-72.248354C354.980245 990.886171 177.490122 960.541863 0 928.752587V95.488241C178.33302 63.578551 356.786453 32.511759 535.119473 0z" fill="#2A5699" p-id="25997"></path><path d="M604.718721 131.010348H988.598307v761.979304H604.718721v-95.247413h302.479774v-48.165569H604.718721v-59.002822h302.479774v-48.16557H604.718721v-59.002822h302.479774v-48.165569H604.718721v-60.206961h302.479774V428.673565H604.718721v-60.206961h302.479774v-46.96143H604.718721v-59.604892h302.479774V214.336783H604.718721zM240.827846 341.373471c22.156162-1.324553 44.19191-2.287865 66.348071-3.492003 15.533396 80.4365 31.30762 160.632173 48.165569 240.827845 13.125118-82.724365 27.695202-165.087488 41.783632-247.571025 23.239887-0.842897 46.479774-2.167451 69.719661-3.612418-26.370649 115.356538-49.369708 231.796802-78.148636 346.430856-19.386642 10.355597-48.165569 0-71.52587 1.204139C301.034807 596.169332 283.093133 517.779868 269.245532 438.667921c-13.606773 76.944497-31.30762 153.16651-46.841016 229.508937-22.39699-1.204139-44.793979-2.528692-67.311383-4.094073-19.266228-104.760113-42.024459-208.918156-60.206962-313.919097 19.868297-0.963311 39.857008-1.806209 60.206962-2.528693 12.041392 75.860771 25.648166 151.360301 36.124177 227.341487 16.135466-77.907808 32.873001-155.695202 49.610536-233.603011z" fill="#FFFFFF" p-id="25998"></path></svg>`,
                type: "popup-file",
                title: "上传Word文件",
                fileType: ".docx",
                placeholder: "深入分析Word文档，精准提炼重点内容，高效获取核心信息，文件大小≤2M",
                input: `
                    <<|>>
                    你的任务是分析并概要一份Word文件。请仔细阅读以下Word文件的内容，并按照指示完成任务。
                    Word文件内容:
                    <word_content>
                    {{WORD_CONTENT}}
                    </word_content>
                    在分析和概要时，请遵循以下指南:
                    1. 仔细阅读整个Word文件内容，理解其核心主题和关键信息。
                    2. 识别Word文件中的主要观点、重要事实和关键结论。
                    3. 提取重要信息时，关注内容的逻辑结构和连贯性。
                    4. 用简洁、清晰的语言概括内容，避免使用过于复杂的句子和术语。
                    5. 确保概要内容涵盖了Word文件的主要内容，但不包含过多细节。
                    6. 对文件进行一定的分析，如指出文件的优点、不足、适用场景等。
                    请写下你的分析和概要结果。
                `,
                submitText: "开始分析",
            },
            {
                label: "论文分析",
                description: "论文智析引擎，学术洞察无忧📚",
                icon: `<svg t="1740727543303" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="29981" width="14" height="14"><path d="M999.18019 258.048c10.044952 4.096 15.067429 16.384 15.067429 24.576v704.512c0 20.48-20.089905 36.864-45.202286 36.864H54.954667C29.842286 1024 9.752381 1007.616 9.752381 987.136V36.864C9.752381 16.384 29.842286 0 54.954667 0H662.674286c10.044952 0 25.112381 4.096 30.134857 12.288l306.371047 245.76z m-26.282666 103.960381L599.186286 37.059048v324.949333h373.711238zM109.714286 502.052571c-6.972952 0-13.897143 4.291048-13.897143 8.582096v55.783619c0 4.291048 6.92419 8.582095 13.897143 8.582095h758.54019c6.92419 0 13.897143-4.291048 13.897143-8.582095v-55.783619c0-4.291048-6.972952-8.582095-13.897143-8.582096H109.714286z m0 163.05981c-6.972952 0-13.897143 4.291048-13.897143 8.582095v55.783619c0 4.291048 6.92419 8.582095 13.897143 8.582095h361.862095c6.972952 0 13.945905-4.291048 13.945905-8.582095v-55.783619c0-4.291048-6.972952-8.582095-13.945905-8.582095h-361.813333z" fill="#7DC13A" p-id="29982"></path></svg>`,
                type: "popup-file",
                title: "上传论文文件",
                placeholder: "解析学术论文，提炼研究成果，方法和结论，快速掌握论文精髓，文件大小≤2M",
                input: `
                    <<|>>
                    你的任务是对给定的论文进行详细分析，并回答相关问题。请仔细阅读以下论文：
                    <paper>
                    {{PAPER}}
                    </paper>
                    在分析时，请遵循以下要求：
                    1. 首先，详细阐述你分析问题的思路和依据，包括从论文中提取的关键信息和推理过程。这部分内容有助于理解你的思考逻辑，但不会显示给用户。
                    2. 然后，给出清晰、准确、完整的回答。回答应基于论文内容，避免主观臆断，并且要紧扣问题，提供有针对性的答案。
                    3. 如果论文中没有足够的信息来回答问题，请在<回答>标签中明确说明“论文中未提供足够信息来回答该问题”。
                    4. 确保回答的语言表达清晰、流畅，逻辑连贯，易于理解。
                    请按照以下格式输出你的分析和回答：
                    [在此详细说明你对问题的分析思路和依据]
                    [在此给出问题的回答]
                `,
                submitText: "开始分析",
            },
            {
                label: "图书分析",
                description: "书籍解析引擎，知识获取无忧📚",
                icon: `<svg t="1740727593891" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="33317" width="14" height="14"><path d="M511.981761 1024a811.427093 811.427093 0 0 0-511.981761-176.249721V300.825763a811.427093 811.427093 0 0 1 511.981761 176.249721A811.491091 811.491091 0 0 1 1023.963521 300.825763v546.924516A811.491091 811.491091 0 0 0 511.981761 1024z m415.98518-621.161871a704.614898 704.614898 0 0 0-355.507335 148.794699L511.981761 600.687081l-60.477846-49.054253A704.486903 704.486903 0 0 0 95.99658 402.838129v353.747398a908.767625 908.767625 0 0 1 415.985181 148.122723 908.767625 908.767625 0 0 1 415.98518-148.122723V402.838129zM511.469779 298.361851a149.178686 149.178686 0 1 1 149.146687-149.178686 149.306681 149.306681 0 0 1-149.146687 149.178686z" fill="#0590DF" p-id="33318"></path></svg>`,
                type: "popup-file",
                title: "上传图书文件",
                placeholder: "分析图书内容，提取核心思想和要点，帮助快速了解书籍精华，文件大小≤2M",
                input: `
                    <<|>>
                    你需要对图书进行详细分析，依据给定的图书内容来回答相关问题。
                    首先，请仔细阅读以下图书内容：
                    <图书内容>
                    {{BOOK_CONTENT}}
                    </图书内容>
                    在进行分析时，请遵循以下要求和步骤：
                    1. 详细分析如何从图书内容中找到答案，阐述你的思考过程和依据。
                    2. 然后给出清晰、准确、全面的答案。答案应紧密结合图书内容，不要包含无关信息。
                    3. 如果图书内容无法回答问题，请在说明“图书内容未提供相关信息，无法回答该问题”。
                    4. 尽量使用简洁明了的语言，确保回答具有逻辑性和条理性。
                    请按照上述要求完成图书分析，具体格式如下：
                    [在此详细说明你的思考过程和依据]
                    [在此给出针对问题的答案]
                `,
                submitText: "开始分析",
            }
        ]
    },

    {
        title: "命理",
        list: [
            {
                label: "生肖百科",
                description: "了解生肖的起源、特征和生活规律",
                icon: `<svg t="1740965433300" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13241" width="20" height="20"><path d="M673.043456 471.595c-26.540032 2.204-74.533888 28.677-99.943424 27.574-18.239488-0.792-35.308544-32.535-42.660864-54.067 18.302976 0.084 40.685568-6.481 47.435776-3.785 11.787264 4.709 32.679936 26.094 37.501952 31.096 4.822016 5.007 56.786944 3.957 41.786368-38.943-5.720064-16.36-5.820416-29.36-2.58048-38.763 8.48896 7.89 15.643648 16.238 15.643648 16.238l7.902208-20.408 42.34752 12.683-5.897216-17.589c6.506496 0.305 11.061248-1.486 11.959296-6.829l12.575744 5.669c0-14.128-8.406016-28.804-20.887552-39.687 8.213504-1.512 20.105216-4.221 22.511616-7.579 3.712-5.18 13.179904-25.219-10.544128-29.173 7.453696-4.973 13.698048-10.74 18.359296-15.654 4.862976 0.721 13.630464 0.833 16.29184-6.968 3.749888-10.986-5.482496-18.834-19.616768-23.02 0 0 51.762176-61.735 39.974912-71.152 0 0-18.751488-9.417-49.287168 24.59 0 0-13.39392-4.709-19.288064 0-5.892096 4.708-19.284992 17.265-25.713664 36.622l-31.072256-8.371c0 0 50.359296-57.026 50.359296-72.199 0-15.172-28.931072-24.589-60.53888 23.543 0 0-6.9632 0-9.107456-15.172-1.195008-8.465-7.894016-4.223-14.979072 10.718-24.409088-90.799-116.844544-97.651-116.844544-97.651l15.248384 57.91-48.626688 1.655c0 0 13.04576 54.594 7.964672 62.867-5.085184 8.268-94.862336 3.306-57.595904 13.23 2.364416 0.634 4.666368 1.352 6.905856 2.142 33.06496 11.652 52.380672 39.218 52.380672 39.218 27.109376 62.854-94.86336 87.673-188.034048 94.295-93.167616 6.613-81.306624 57.896-81.306624 57.896l22.020096-1.656 11.85792 26.47 15.24736-21.512 22.023168 21.512c6.771712-18.201 37.267456-41.36 37.267456-41.36 6.776832 16.542 40.656896 54.59 40.656896 54.59 8.467456 87.673 81.31072 129.033 81.31072 129.033-22.023168 13.23-22.023168 79.408-22.023168 79.408-81.043456 42.431-44.043264 87.674-44.043264 87.674-3.390464-8.266 5.085184-46.313 18.599936-23.154 13.523968 23.154 22.057984 13.235 22.057984 13.235 15.24736-33.093 10.166272-14.896 35.576832-8.278 25.40544 6.613 12.358656-57.896 12.358656-57.896-2.879488-26.47 48.626688-87.682 82.50368-43.012 33.881088 44.667 0 107.525-98.2528 153.853-98.2528 46.313-28.8 74.437-28.8 74.437-72.839168 99.258 33.88416 43.012 33.88416 43.012 16.943104 71.136 67.757056-31.431 67.757056-31.431 38.964224 62.854 66.06848-51.284 66.06848-51.284 44.043264 24.81 33.881088-82.711 33.881088-82.711 52.514816-29.781 55.90016-130.694 47.429632-180.316-8.467456-49.634 14.687232-63.976 14.687232-63.976l36.701184 19.856 0-33.083 32.750592 13.26-7.911424-40.285 24.846336-10.476C699.026432 392.743 673.043456 471.595 673.043456 471.595zM665.736192 317.046l-0.775168-0.277c11.102208-4.431 18.937856 0.3 23.349248 4.636-3.914752 2.605-7.172096 4.956-9.567232 6.418-3.634176-0.188-7.279616 0.008-10.891264 0.626C662.216704 326.844 657.477632 323.166 665.736192 317.046zM617.467904 304.08c11.802624 0.068 15.755264 5.631 16.960512 10.454-8.255488 6.446-20.660224 14.191-20.660224 2.512C613.768192 311.627 615.15776 307.387 617.467904 304.08z" fill="#d81e06" p-id="13242"></path></svg>`,
                input: "生肖",
                prompt: `
                    <<|>>
                    你的任务是提供指定生肖的百科信息。请仔细阅读以下要查询的生肖名称，然后根据已知知识输出全面、丰富的该生肖的百科内容。
                    在输出百科信息时，请包含以下方面：
                    1. 生肖的基本信息，如对应的地支、阴阳五行属性等。
                    2. 生肖的起源和传说故事。
                    3. 与该生肖相关的文化习俗，如节日、传统活动等。
                    4. 人们普遍认为该生肖所代表的性格特点。
                    5. 其他与该生肖相关的有趣信息。
                    请写下你的内容。
                `,
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[主题]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">十二生肖的起源是什么？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">属龙的人在本命年要注意哪个事情？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">十二生肖与五行、八字有什么关系？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">请告诉我‘闻鸡起舞’中指的哪个生肖动物？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">请问‘胆小如鼠’是指的哪个生肖动物？</span>",
                ],
            },
            {
                label: "周公解梦",
                description: "输入关键词，获取相关的解梦信息",
                appHide: true,
                icon: `<svg t="1740964643972" class="icon" viewBox="0 0 1780 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8094" width="20" height="20"><path d="M1031.042568 62.529464s51.645496-74.693899 153.495962-52.979316c0 0 76.027718 31.211379 80.029177 103.184286a1513.885271 1513.885271 0 0 1 252.678789 97.368833s156.750482 78.001771 261.428646 154.189548c0 0 7.842859 20.647528-13.498255 26.356276 0 0-108.519564 63.489814-464.169228 78.215182A186.734747 186.734747 0 0 1 1217.510551 640.233418a249.477622 249.477622 0 0 1-132.154848 52.605846l1.974053 1.760642h39.48106s-9.230032 42.362111-25.182514 50.95191c0 0-1.653936 2.720992 0.640233 3.788047a120.31053 120.31053 0 0 1 50.845204 46.096807s20.434117 28.010212 12.377846 56.500599c0 0 325.185224 4.855103 393.210025 11.790965 0 0 40.281353 9.710207-56.874069 13.178138l-350.474444 7.629448s-32.011671 39.534414-82.430052 11.310791a62.956286 62.956286 0 0 0-9.336737-5.335279c-1.387172 18.1933-2.774345 35.746366-4.001459 51.325379a19.793883 19.793883 0 0 1 2.027405 9.816913 38.147241 38.147241 0 0 1-3.627989 10.990673L1051.049862 1003.032356l0.586881-35.906425a10.670557 10.670557 0 0 1-10.670557 4.001459l3.627989 8.216329 6.882509 4.108164-98.756005 1.387173s3.627989-11.897671-11.897671-11.897671l-132.368259 0.373469s-4.054812 1.440525-2.934403 10.243735l-82.216642-0.96035 0.853645 41.401761s-1.387172-16.539363-3.414578-41.455114h-8.856562s-3.147814-12.591257 7.682801-14.191841c-4.641692-0.480175-29.023915-4.21487-19.740531-28.437034a215.651956 215.651956 0 0 0 16.005836-19.207003c-0.640233-7.842859-1.33382-16.005835-1.974053-23.9554a77.78836 77.78836 0 0 1-39.534414-3.147814c-6.295629-2.454228-30.517793 32.705257-69.038504 2.400875l-230.323972-1.440525s-16.005835-1.120408-18.353358-26.142865c0 0 1.120408-25.022456 21.661231-27.263273l196.284895-1.120408s-0.586881-71.972907 70.69244-92.887199a7.789507 7.789507 0 0 0 4.001459-8.803209 52.819257 52.819257 0 0 1-16.005835-43.429167l32.598551-1.707289s-65.357161-6.829156-135.409368-66.690981c0 0-55.433543-34.465899-57.08748-154.723076 0 0-326.732455-16.539363-432.584379-54.900016 0 0-59.96853-14.992133-34.145783-43.322461 0 0 156.430365-117.749596 298.775596-182.146408 0 0 145.813161-61.035586 206.848747-79.12218 0 0 3.307873-87.285156 114.601781-110.92044 0 0 68.931798-11.844318 114.228313 59.061533a890.991507 890.991507 0 0 1 290.079091 2.720992z m-646.262284 794.956494l86.164748 1.33382s19.687178 11.364143-1.280467 21.874642H386.060751q-17.286302-12.484552-1.280467-23.208462z m336.816131 52.87261c0.640233-1.227114 1.227114-2.400875 1.707289-3.521284 2.187464-5.335278 1.280467-9.230032-2.027405-11.044026l0.320116 14.56531z m331.05403-20.327411a16.806127 16.806127 0 0 0-2.240816 0.480175 47.270567 47.270567 0 0 1-26.676393-1.760642s-4.374928 7.896212-1.173761 11.790966c6.829156 8.376387 21.341114 22.248111 29.50409 34.946074l0.693586-45.456573z" fill="#221714" p-id="8095"></path><path d="M520.1363 106.70557s9.069973-70.532382 90.699734-94.701193c0 0 48.337623-22.141406 121.857761 48.337623A593.869848 593.869848 0 0 0 520.1363 106.70557zM1257.471787 106.70557S1248.455166 35.906424 1166.825405 11.737613c0 0-48.337623-22.141406-121.911113 48.337623A593.656437 593.656437 0 0 1 1257.471787 106.70557z" fill="#C76F30" p-id="8096"></path><path d="M755.635492 61.462408s89.579326 32.545199 107.612567 41.18835c0 0 28.223623 6.50904 46.256865 0.746939 0 0 96.835305-39.000886 119.243474-41.18835-87.125098-5.708748-176.384307-8.483093-273.112906-0.746939z" fill="#AF9672" p-id="8097"></path><path d="M656.239254 101.850466s-35.532955 101.370291 8.002918 160.965352c0 0 7.896212 22.888345-24.168812 0-5.335278 1.493878-80.029177-95.661543 16.005836-160.858646z" fill="#DCDDDD" p-id="8098"></path><path d="M661.841296 96.035013s-39.534414 34.999427-28.597092 129.007034c0 0 11.31079 49.938207 39.961235 56.767363s-3.627989-177.611421-3.627989-177.611421L661.841296 96.035013z" fill="#A55A3E" p-id="8099"></path><path d="M1121.849007 101.850466s41.081644 99.022769 0.906998 161.178763c0 0-6.615745 23.315167 24.168811-1.440525 5.335278 1.173761 74.427135-99.929766-25.075809-159.738238z" fill="#DCDDDD" p-id="8100"></path><path d="M1121.31548 99.076122s39.481061 34.946074 28.597092 128.953681c0 0-11.364143 49.938207-39.961236 56.767363s3.627989-177.558068 3.62799-177.558068l7.736154-8.162976z" fill="#A55A3E" p-id="8101"></path><path d="M771.1078 452.431616s1.707289 43.642578 65.730631 44.762986l5.655395 1.120409 13.018079 44.762986 67.971448-1.120408 11.364143-47.057157s59.488355 0 61.142292-52.018965c0 0 25.235867 1.600584 37.346949 88.832387a1199.050487 1199.050487 0 0 1 15.472308 167.154275s-51.218673 14.56531-140.317824 12.858021l4.588339-126.766217s-19.420414-47.430626-54.259782 1.120409l6.829157 125.645808s-94.167665 2.240817-138.717241-10.35044c0.320117-116.09566 13.498255-202.31376 43.855989-248.944094z" fill="#C76F30" p-id="8102"></path><path d="M651.704267 280.208826c-43.962695-13.231491-92.246965 24.915751-107.825978 85.364456s7.46939 119.883708 51.485437 133.061845 92.300318-24.915751 107.825979-85.364455-7.46939-119.830355-51.485438-133.061846z" fill="#FFFFFF" p-id="8103"></path><path d="M640.980357 309.446152c-29.557443-8.856562-62.049289 16.806127-72.506434 57.300891s5.015162 80.616058 34.625957 89.525973 62.049289-16.752774 72.559788-57.300891-5.068515-80.882822-34.679311-89.525973z" fill="#B5CAD7" p-id="8104"></path><path d="M634.89814 333.134789c-19.953942-6.028865-41.881936 11.31079-48.924504 38.627416s3.414578 54.313135 23.36852 60.342 41.828583-11.31079 48.871151-38.627417-3.467931-54.366488-23.315167-60.341999z" fill="#221714" p-id="8105"></path><path d="M862.074298 590.82874l22.194758 307.952274s4.268223 11.417496 9.603502-5.335279l17.339655-300.696295s-21.341114-55.486896-49.137915-2.080759zM976.836138 425.328401s40.601469-4.641692 62.63617 112.521023a1939.267024 1939.267024 0 0 1 15.045485 300.482885l-2.29417 144.052519s19.74053-251.771792 19.740531-257.533893 9.283385-184.49393-16.005836-251.771792c0 0-27.850154-83.497108-74.267076-82.376699-30.891262 6.13557-30.197676 24.382223-4.58834 34.839368zM796.343667 425.328401s-40.601469-4.641692-62.636169 112.521023a1934.945449 1934.945449 0 0 0-15.098839 300.482885l2.347523 144.052519s-19.74053-251.771792-19.74053-257.533893-9.283385-184.49393 16.005835-251.771792c0 0 27.850154-83.497108 74.267077-82.376699 30.944615 6.13557 30.251029 24.382223 4.641692 34.839368z" fill="#DCDDDD" p-id="8106"></path><path d="M890.991507 423.78117s57.781066-18.246652 37.34695-63.062991c0 0 21.341114-3.041109 44.869692 28.863856 0 0-20.540822 1.547231-15.205544 25.876101 0 0 9.870265 14.405252 23.528578 15.152191 0 0 10.670557-4.534987 10.670557 9.923618 0 0 8.323034 34.946074-34.199135 49.404678l-29.664148 2.987756 3.041108-9.870265s32.705257-18.246652 2.29417-24.275517q-39.534414-2.29417-42.682228-34.999427zM876.746314 422.287292s-57.781066-18.246652-37.34695-63.062991c0 0-21.341114-3.041109-44.869692 28.863856 0 0 20.540822 1.493878 15.205544 25.822748 0 0-9.870265 14.458605-23.528578 15.205544 0 0-10.670557-4.534987-10.670557 9.870265 0 0-8.323034 34.946074 34.199135 49.404679l29.664148 3.041108 4.534987-9.870265s-40.281353-18.246652-9.870265-24.32887q39.534414-2.240817 42.682228-34.946074z" fill="#A55A3E" p-id="8107"></path><path d="M883.57547 349.620799c5.868806 0 10.670557 2.774345 10.670557 6.188923s-4.748398 6.13557-10.670557 6.135571-10.670557-2.774345-10.670557-6.135571 4.801751-6.188923 10.670557-6.188923zM846.335226 464.809462s30.411087 0.800292 40.281353-18.1933c0 0 21.341114 22.781639 38.734122 24.275517l-3.041109 68.39827-64.55687-0.746939-11.417496-73.733548z" fill="#FFFFFF" p-id="8108"></path><path d="M1233.089564 221.307352s-26.196217 26.196217-41.721878 24.222164l1.974053 7.789507s90.166206 115.402074 97.795655 178.411712c0 0 35.853071 141.598291-71.812849 203.64758 0 0-36.97348 33.612254-145.439691 61.195644l-0.96035-156.803834s1.600584-110.440265-76.82801-148.85427a59.701766 59.701766 0 0 0-21.020998-3.041109s-14.351899-30.037618-45.669984-30.037618a75.760955 75.760955 0 0 0-88.352211-3.147814s-33.825666 8.749857-50.738499 33.825665c0 0-57.300891 1.493878-82.643463 108.839682a1155.354556 1155.354556 0 0 0-10.670557 199.752826s-86.484864-13.391549-146.93357-61.88923c0 0-85.364456-45.563278-73.893607-169.608503 0 0 11.790965-109.639973 111.453968-213.41114 0 0 6.989215-9.069973-3.467931-9.069973a108.732976 108.732976 0 0 1-39.26765-25.28922c11.044026-1.173761 46.150159-4.748398 57.194185-5.975512a112.040848 112.040848 0 0 0 47.537332 62.476111s28.757151 2.454228 22.888344-7.789507c0 0-22.888345-37.026833-20.967644-59.435002 0 0 40.281353-7.362684 152.375553-10.35044 0 0 228.776742-3.041109 322.837702 10.670557a114.014901 114.014901 0 0 1-9.816913 51.16532s-15.045485 27.103215 30.090971 13.551608c0 0 29.397384-30.83791 40.654822-59.435003l45.02975 8.80321zM652.771323 276.954306c-46.203512-13.658313-97.262127 26.089512-113.588079 88.51227s8.002918 124.5254 54.259782 138.183713 97.208774-26.036159 113.534726-88.458917-8.056271-124.5254-54.206429-138.237066z m462.942114 1.600584c47.590684-13.711666 100.249883 26.089512 117.05601 88.565623s-8.269682 124.685458-55.860366 138.343771-100.249883-26.036159-117.05601-88.565623 8.269682-124.632105 55.860366-138.343771z" fill="#C76F30" p-id="8109"></path><path d="M1120.835305 503.810348s73.893607 30.83791 116.149012-55.273486c0 0 37.346949-10.670557 61.729172 18.673475l25.182515 133.381962L1211.108217 692.252384l-86.111395-4.855104c-43.589225-60.342-38.467358-121.110822-4.054812-183.586932zM651.38415 504.82405s-73.94696 30.891262-116.202365-55.220132c0 0-37.346949-10.670557-61.729172 18.673475l-25.182515 133.381962L561.217944 693.586203l86.111395-4.855103c43.589225-60.342 38.414005-121.110822 4.054811-183.640286zM888.910749 182.039702a49.191268 49.191268 0 1 1-49.137915 49.191268 49.137915 49.137915 0 0 1 49.137915-49.191268z" fill="#A55A3E" p-id="8110"></path><path d="M888.910749 170.195384a76.507894 76.507894 0 1 1-76.347835 76.45454 76.561246 76.561246 0 0 1 76.347835-76.45454z m1.067055 4.588339a61.248997 61.248997 0 1 1-61.088938 61.248997 61.30235 61.30235 0 0 1 61.088938-61.248997z" fill="#A55A3E" p-id="8111"></path><path d="M12.324493 374.056375S247.877039 241.688115 600.538946 211.010264c0 0-5.335278-91.980201 58.688064-114.281665 0 0-20.914292 101.743761-6.989215 103.130933 0 0 296.801542-21.341114 479.694889 0.853645 0 0 15.152191-20.167353-13.124785-100.996822 0 0 58.688063 7.09592 61.622466 96.995363l-2.027406 13.124785s382.752879 28.330329 594.616788 160.645235c0 0 8.216329-7.042568-63.169698-51.53879 0 0-240.994529-151.575262-445.869223-203.807638 0 0-122.017819-37.080185-232.511436-51.378732 0 0-71.439379 32.491846-114.335018 42.682228 0 0-10.403793 9.123326-58.688063 5.335278 0 0-88.352212-31.158026-109.159798-46.737039 0 0-173.556609 30.624499-264.202991 56.393893 0 0-176.757776 64.343459-281.80941 125.325692A1705.688532 1705.688532 0 0 0 12.324493 374.056375z" fill="#EDA83E" p-id="8112"></path><path d="M649.943625 696.467254s30.677851 52.87261 51.378732 67.384567l-2.400875-62.155994s-3.788048 2.080759-9.976971-5.335279zM734.7212 746.298755s47.750742-18.993591 30.357735 30.944615c0 0-29.290679 21.341114-41.775231 0.533528l0.586881-70.532382s38.520711 4.321576 56.393894 5.335279l65.677278 60.235294s-21.341114-7.042568-21.714584 9.230032c0 0 0 22.248111 23.315167 5.975512 0 0 5.335278-5.975512 4.908457 14.672015l-10.670557 3.788048s-7.576095-15.739072-13.551608-1.120408c0 0-3.788048 14.138488 7.629449 13.60496a8.42974 8.42974 0 0 0 9.176679-3.788048l9.230031 1.067056s-1.067056 14.672016-13.018079 15.739071c0 0-91.713437 3.788048-105.798573 18.460064h-5.335278l1.760642-49.404679s44.176106 7.042568 55.006721-28.757151c0 0 8.162976-44.496223-33.078727-49.351326-23.421873 1.867347-27.369979 10.670557-18.993591 23.315167zM1125.15688 696.467254c-2.774345 9.016621-36.226541 65.997395-53.352785 67.384567l1.9207-62.155994a12.111082 12.111082 0 0 0 12.431199-5.335279z" fill="#1D96D5" p-id="8113"></path><path d="M653.518262 749.019747s36.653363 39.961236 48.497681 42.682228l-0.373469 4.321575 6.615745 89.632679c0.640233 9.069973-31.42479 5.655395-34.359193 0 0 0-27.743448 37.93383-63.59652 7.736154-70.052207-49.884854-16.699422-127.566509 43.215756-144.319283zM721.169593 800.291773s4.108164 3.627989 7.736154 2.400875l-1.280467 77.094774-6.562392 3.62799V800.291773zM1071.590684 773.188558s19.847236-9.123326 24.008753-20.540822c0 0 51.752201 23.208461 65.090398 70.425676 0 0 17.499713 79.549002-62.422758 79.175533 0 0-18.673475 0.800292-27.049862-14.832074l-3.788048 1.54723 4.161517-115.775543zM1049.555984 789.621216s-15.632366 16.005835-31.638201 13.711666l8.002917 80.029177a20.274058 20.274058 0 0 0 22.834992 3.467931l0.800292-97.102068z" fill="#F7D39F" p-id="8114"></path><path d="M1040.379305 746.298755s-47.750742-18.993591-30.357735 30.944615c0 0 29.290679 21.341114 41.775231 0.533528l-0.533528-70.532382s-38.520711 4.321576-56.447246 5.335279l-65.677278 60.235294s21.341114-7.042568 21.714583 9.230032c0 0 0 22.248111-23.315167 5.975512 0 0-5.335278-5.975512-4.855103 14.672015l10.670557 3.788048s7.576095-15.739072 13.551607-1.120408c0 0 3.788048 14.138488-7.576096 13.60496a8.483093 8.483093 0 0 1-9.230031-3.788048l-9.230032 1.067056s1.067056 14.672016 13.018079 15.739071c0 0 54.526546 2.240817 85.951337 10.030324l-4.321576-43.162403a38.040536 38.040536 0 0 1-27.369978-26.676393s-8.162976-44.496223 33.078726-49.351326c23.421873 1.867347 27.423331 10.670557 18.993592 23.315167z" fill="#1D96D5" p-id="8115"></path><path d="M724.210702 905.076643s59.328297-16.379305 153.869431-16.592717l-1.9207-26.676392c-33.665607 0-104.251342 4.108164-144.052519 16.005835-11.790965 5.335278-13.60496 13.071432-7.949565 27.263274z m176.06419-16.379305a855.832022 855.832022 0 0 1 121.857761 11.790965s8.536446-20.327411-1.974054-28.223623c0 0-71.332673-10.670557-118.336476-10.670557l-1.547231 27.049862z" fill="#F3C91D" p-id="8116"></path><path d="M804.559996 899.314542v52.285729a8.80321 8.80321 0 0 0 9.710207 7.09592l121.377585 0.853645a13.498255 13.498255 0 0 0 7.095921-11.524202l-0.906998-56.660657c0-0.586881-29.023915 0-41.935289-1.067056-4.21487 15.312249-19.74053 24.755692-21.66123-0.746939-30.411087 2.240817-68.39827 1.974053-73.680196 9.76356z" fill="#1D96D5" p-id="8117"></path><path d="M871.944563 895.153025a29.077268 29.077268 0 1 1-29.077268 29.077267 29.023915 29.023915 0 0 1 29.077268-29.077267z" fill="#FFFFFF" p-id="8118"></path><path d="M800.291773 962.804356l-73.200021-2.454228s11.737613-37.346949 11.737613-56.020424l56.553952-8.696504 5.015162 67.011097z" fill="#C76F30" p-id="8119"></path><path d="M721.756474 923.910176h84.617517v46.523628h-84.617517z" fill="#A55A3E" p-id="8120"></path><path d="M718.768718 964.61835c-33.879018-6.882509-17.126244-25.716042-3.25452-41.881936 1.120408 14.778721 2.240817 28.917209 3.25452 41.881936z m3.201167-49.724795a40.761528 40.761528 0 0 0 4.695045-7.416037s9.550148-5.868806 9.230032-1.33382c-0.96035 14.458605-5.335278 46.897098-13.178138 56.447247l-0.746939-47.69739z" fill="#D71518" p-id="8121"></path><path d="M956.56208 964.404939l73.306726-2.347522s-10.670557-38.414005-10.670557-56.980774L960.350128 902.72912l-3.521284 61.675819z" fill="#C76F30" p-id="8122"></path><path d="M950.586568 925.457406h84.617517v46.523629h-84.617517z" fill="#A55A3E" p-id="8123"></path><path d="M1036.964727 966.432345a36.173188 36.173188 0 0 0 14.085135-5.335278l0.320117-22.141406c-8.376387-13.284843-25.342573-28.063565-29.237326-36.97348 0 0-1.387172-1.280467-1.067056 3.201167 1.067056 16.005835 6.348981 56.660658 16.005835 61.035586z m18.300005-9.923618a11.737613 11.737613 0 0 0 0.586881-7.576095c0 2.560934-0.426822 5.335278-0.586881 7.576095z" fill="#D71518" p-id="8124"></path><path d="M1167.892461 856.899078l388.781743 10.403793s18.1933 0-9.069973 5.815453L1157.755432 882.881884a54.79331 54.79331 0 0 0 10.35044-25.982806z" fill="#D4B465" p-id="8125"></path><path d="M1156.368259 883.468765l0.693586-0.96035a54.046371 54.046371 0 0 0 10.297088-25.66269v-0.533528h0.533528l388.72839 10.403793c6.348981 0 6.455687 1.173761 6.50904 1.600584s0 2.027406-15.472308 5.335278z m12.05773-25.982807a55.326838 55.326838 0 0 1-9.76356 24.809045l388.888449-9.710206a48.390976 48.390976 0 0 0 14.351899-4.161518 18.033241 18.033241 0 0 0-5.335278-0.533528z" fill="#221714" p-id="8126"></path><path d="M377.951128 840.679831l195.698015 1.33382a97.155421 97.155421 0 0 0 30.304382 49.671443l-229.416975 2.027405c-30.46444-19.687178-17.126244-46.897098 3.681342-53.032668z m5.335279 16.005836l83.92393 0.426822s19.313708 12.111082-1.013703 23.795342l-81.309644 1.013703q-16.966186-13.391549-1.600583-25.235867z" fill="#D4B465" p-id="8127"></path><path d="M374.056375 894.29938c-13.178138-8.536446-19.527119-19.420414-17.926536-30.677851a30.251029 30.251029 0 0 1 21.767936-23.421873l196.17819 1.387173v0.426822a97.955713 97.955713 0 0 0 30.144324 49.297973l-0.37347 1.067056z m4.001459-53.032668a28.917209 28.917209 0 0 0-20.860939 22.408169c-1.547231 10.670557 4.534987 21.341114 17.232949 29.450738l227.97645-2.027406a99.556297 99.556297 0 0 1-29.237326-48.497682z m6.615745 41.241703a19.420414 19.420414 0 0 1-9.123326-14.085136 16.005835 16.005835 0 0 1 7.576095-12.057729l84.083989 0.426822c0.320117 0.266764 8.696504 5.602042 8.536446 12.804669 0 4.374928-3.307873 8.42974-9.550149 12.004376h-0.320117z m-1.173761-25.182515a14.725369 14.725369 0 0 0-6.935862 10.990674 18.033241 18.033241 0 0 0 8.536445 13.071432l80.936175-1.067056c5.815454-3.361225 8.80321-7.042568 8.909915-10.990673 0-6.082217-6.882509-10.670557-7.896212-11.68426z" fill="#221714" p-id="8128"></path><path d="M1118.114313 282.76976c44.016048-13.178138 92.300318 24.969103 107.879331 85.364456s-7.522743 119.830355-51.485438 133.061845-92.300318-24.915751-107.879331-85.364456 7.46939-119.723649 51.485438-133.061845z" fill="#FFFFFF" p-id="8129"></path><path d="M1128.838222 311.90038c29.610796-8.909915 62.102642 16.752774 72.559788 57.300891s-5.335278 80.616058-34.625958 89.472621-62.102642-16.699422-72.559787-57.247539 4.855103-80.616058 34.625957-89.525973z" fill="#B5CAD7" p-id="8130"></path><path d="M1135.027145 336.122545c19.953942-6.028865 41.828583 11.257438 48.871151 38.574063s-3.361225 54.366488-23.315167 60.342-41.881936-11.257438-48.924503-38.627416 3.414578-54.579899 23.368519-60.288647z" fill="#221714" p-id="8131"></path></svg>`,
                before: "梦见",
                input: "梦见",
                prompt: `
                    <<|>>
                    你的任务是进行周公解梦，为用户提供对其梦境的解读。请仔细阅读以下梦境描述，并根据周公解梦的相关知识进行解读。
                    在解梦时，请遵循以下指南:
                    1. 运用常见的周公解梦理念和象征意义进行分析。
                    2. 解读应尽量全面，考虑梦境中的各种元素及其可能代表的含义。
                    3. 避免使用过于模糊或不确定的表述。
                    4. 语言表达清晰、易懂。
                    请写下你的解梦结果。
                `,
                list: [
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">[场景]</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">蛇了，这有什么预兆吗？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">掉牙代表什么？周公如何解梦？</span>",
                    "<span class=\"text-[#4D6BFE] dark:text-[#4D6BFE] bg-[#4D6BFE]/10 dark:bg-[#4D6BFE]/10 rounded-lg px-1\">怀孕是什么意思？</span>",
                ],
            },
            {
                label: "测运势",
                description: "数字命理引擎，每日运势先知先觉📊✨",
                appHide: true,
                icon: `<svg t="1740714445026" class="icon" viewBox="0 0 1459 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8615" width="24" height="24"><path d="M1225.756111 814.294818c5.484971-50.713501-59.975009-98.909311-145.396687-106.372468-8.002662-0.71934-16.005325-1.168928-24.007987-1.168928-22.749141-40.013312-82.724151-71.934044-155.827123-79.037531-70.045775-6.474064-133.077982 12.048952-165.628136 44.239437a171.832448 171.832448 0 0 0-106.192633-44.059602C566.480597 621.87125 512.530064 642.012782 492.478449 674.383102a234.774736 234.774736 0 0 0-42.890674-8.182498c-82.00481-7.642992-152.859844 25.446668-157.985144 74.002148a55.838802 55.838802 0 0 0 5.125301 30.931639c-49.63449 11.959035-85.151925 40.193147-88.928462 75.440829-5.395053 53.051358 62.942289 102.955601 152.859843 110.958263a260.221404 260.221404 0 0 0 77.419015-4.04629c32.280402 32.999743 89.917555 57.906905 158.704485 64.021299 65.639815 6.114394 125.884577-6.384146 164.639043-30.482051a197.189198 197.189198 0 0 0 79.037531 24.007987c51.432842 4.76563 97.920217-8.182498 123.187051-31.201392 5.215218 0.71934 10.340519 1.438681 15.645654 1.978187 85.961183 7.912745 160.053248-26.975267 165.628137-77.59885a56.64806 56.64806 0 0 0-1.079011-17.983511c46.037788-12.318705 78.677861-38.574631 82.274563-71.934044z" fill="#F3E8D8" p-id="8616"></path><path d="M629.422886 1023.892638a223.624959 223.624959 0 0 1-33.629166-1.708433c-65.280145-6.114394-124.985402-29.852628-159.513743-63.661629a263.188684 263.188684 0 0 1-76.609757 3.596702c-59.165751-5.484971-110.958263-28.593783-137.843612-61.863278a73.102972 73.102972 0 0 1-17.983511-53.950533c3.866455-35.067846 35.967022-64.021299 87.220029-77.868603a59.165751 59.165751 0 0 1-4.136208-28.413947c5.574888-50.893336 78.677861-85.871265 162.93061-78.048438a242.237893 242.237893 0 0 1 40.912488 7.283322c22.029801-32.460237 78.048438-51.073171 138.113364-45.5882A181.723379 181.723379 0 0 1 734.44659 665.391346c35.967022-32.460237 98.459723-48.645397 165.897889-42.441086 71.934044 6.563982 132.988064 37.49562 158.075062 79.037531a154.388442 154.388442 0 0 1 22.299554 1.079011c56.64806 5.215218 106.102715 26.975267 131.819135 59.885092a69.686105 69.686105 0 0 1 17.983511 52.062264c-3.596702 33.08966-33.898918 60.69435-81.555222 74.181983a63.571711 63.571711 0 0 1 0 15.465819c-5.844641 53.141275-82.36448 89.917555-170.843355 81.555223a88.478874 88.478874 0 0 1-13.757386-1.708434c-26.975267 24.007987-75.081158 35.967022-125.165236 30.931639a203.303592 203.303592 0 0 1-78.408108-22.749141A260.76091 260.76091 0 0 1 629.422886 1023.892638z m-190.5353-75.081158l1.708434 1.708433c32.819908 33.269495 91.176401 56.827895 155.827123 62.942289 61.77336 5.754724 122.287875-5.395053 161.851599-29.852628l2.158021-1.258846 2.427774 1.258846a193.412661 193.412661 0 0 0 77.059345 22.928976c48.645397 4.585795 94.593268-6.923652 119.860101-30.122381l1.618516-1.438681h2.158021l15.285985 1.978187c83.982996 7.553075 155.917041-25.356751 160.772588-73.642478a52.96144 52.96144 0 0 0-0.899175-16.814583l-0.989094-4.136207 4.226126-1.168928c46.037788-12.048952 75.530746-37.405703 78.857695-67.618002a61.233855 61.233855 0 0 0-15.555737-45.498283c-24.007987-29.582876-71.934044-51.073171-125.884577-55.928719-7.912745-0.71934-16.005325-1.258846-24.007987-1.258846h-2.607609l-1.348763-2.158021c-22.749141-40.10323-82.634233-70.225611-152.410256-76.789592-65.909568-6.114394-129.481279 10.790107-161.851599 42.890674l-3.237032 3.237032-2.96728-3.68662c-22.119719-22.749141-60.784267-38.844384-103.315271-42.800756-58.356493-5.395053-112.576779 12.678375-131.909053 44.149519l-1.618516 2.877362-3.416867-0.899176a233.785643 233.785643 0 0 0-42.441086-7.732909c-79.487119-7.463157-148.184131 23.91807-152.859844 69.955857a53.141275 53.141275 0 0 0 5.395054 28.593783l2.427774 4.855548-5.305136 1.348763c-49.27482 11.959035-82.094728 39.653642-85.601512 71.934044a63.841464 63.841464 0 0 0 16.364995 47.926057c25.176915 30.931639 75.710581 53.950533 131.819135 58.626246a257.703713 257.703713 0 0 0 76.160169-4.04629z" fill="#F7F3ED" p-id="8617"></path><path d="M1394.531362 214.274972h-124.625732a20.321367 20.321367 0 0 1-13.937221-5.395053 18.523016 18.523016 0 0 1-5.844641-13.127963 19.152439 19.152439 0 0 1 19.781862-17.444006h20.141533a32.72999 32.72999 0 1 0 0-65.45998h-182.892307a32.72999 32.72999 0 0 0 0 65.45998h37.405703a18.433099 18.433099 0 1 1 2.967279 36.77628h-54.400121A49.724408 49.724408 0 0 0 1088.002417 314.712881h306.259192a49.724408 49.724408 0 0 0 4.945466-98.90931 44.958778 44.958778 0 0 0-4.945466 0z m-965.894377 440.59602h-71.934044a19.87178 19.87178 0 1 1-5.395053-39.383889 17.983511 17.983511 0 0 1 5.395053 0h22.749141a42.620921 42.620921 0 0 0 27.784525-10.250601 32.37032 32.37032 0 0 0 3.866455-45.768036 31.381227 31.381227 0 0 0-3.866455-3.866454 42.531004 42.531004 0 0 0-27.784525-10.160684H96.301701a42.620921 42.620921 0 0 0-27.874442 10.160684 32.550155 32.550155 0 0 0-3.776537 45.857953 37.405703 37.405703 0 0 0 3.776537 3.776537 42.620921 42.620921 0 0 0 27.874442 10.250601H215.802132a19.87178 19.87178 0 0 1 5.395054 39.383889 17.983511 17.983511 0 0 1-5.395054 0h-62.942288a53.950533 53.950533 0 0 0-13.397716 106.372468 55.658967 55.658967 0 0 0 13.397716 0h275.777141a53.950533 53.950533 0 0 0 13.397716-106.372468 55.658967 55.658967 0 0 0-13.397716-0.179835z" fill="#FFCB27" opacity=".26" p-id="8618"></path><path d="M101.87659 8.993194A11.959035 11.959035 0 0 0 89.917555 20.862311v49.544573a12.408623 12.408623 0 0 0 24.727328 0V21.221982A12.13887 12.13887 0 0 0 103.045518 8.993194z m0 117.432327A12.048952 12.048952 0 0 0 89.917555 138.384556v49.454655a12.408623 12.408623 0 1 0 24.727328 0v-49.005067a11.959035 11.959035 0 0 0-11.509447-12.49854z m-40.013312-33.988836H12.318705a12.408623 12.408623 0 0 0 0 24.727328h49.544573a12.408623 12.408623 0 0 0 0-24.727328z m129.571197 0h-49.454656a12.408623 12.408623 0 1 0 0 24.727328h49.454656a12.408623 12.408623 0 1 0 0-24.727328z m1204.895238 516.576354a7.73291 7.73291 0 0 0-7.642992 7.553075V647.407835a7.73291 7.73291 0 1 0 15.375902 0v-30.571968a7.73291 7.73291 0 0 0-7.37324-7.822828z m0 72.83322a7.642992 7.642992 0 0 0-7.642992 7.642992v30.571969a7.642992 7.642992 0 0 0 7.642992 7.642992 7.73291 7.73291 0 0 0 7.73291-7.642992v-30.841722a7.73291 7.73291 0 0 0-7.37324-7.373239z m-24.63741-21.220543h-30.571969a7.73291 7.73291 0 0 0-7.73291 7.642992 7.73291 7.73291 0 0 0 7.73291 7.553075h30.571969a7.642992 7.642992 0 1 0 0-15.196067z m79.936706 0h-30.571968a7.642992 7.642992 0 0 0-7.642993 7.642992 7.553075 7.553075 0 0 0 7.642993 7.553075h30.571968a7.553075 7.553075 0 0 0 7.642992-7.553075 7.642992 7.642992 0 0 0-7.642992-7.642992zM728.332196 899.17699c-159.333908 0-284.139474-40.4629-284.139474-92.255412V355.805204a6.024476 6.024476 0 0 1 6.024476-5.934558h556.229996a6.024476 6.024476 0 0 1 6.024476 5.934558v450.846621C1012.47167 858.444337 887.666104 899.17699 728.332196 899.17699z" fill="#F34541" p-id="8619"></path><path d="M1006.447194 355.805204v450.846621c0 47.926057-124.535814 86.320853-278.114998 86.320853S450.217198 854.218212 450.217198 806.651825V355.805204h556.229996m0-11.959035H450.217198a12.048952 12.048952 0 0 0-12.048952 11.959035v450.846621c0 63.841464 149.532894 98.279888 290.16395 98.279888s290.16395-34.438424 290.16395-98.279888V355.805204a12.048952 12.048952 0 0 0-12.048952-11.959035z" fill="#F1B622" p-id="8620"></path><path d="M728.332196 821.038634c-153.579184 0-278.114998-38.664549-278.114998-86.320853v71.934044c0 47.926057 124.535814 86.320853 278.114998 86.320853s278.114998-38.664549 278.114998-86.320853v-71.934044c0 47.566387-124.535814 86.320853-278.114998 86.320853z" fill="#E53939" p-id="8621"></path><path d="M450.217198 729.772316c0 47.926057 124.535814 86.320853 278.114998 86.320853s278.114998-38.394796 278.114998-86.320853" fill="#F34541" p-id="8622"></path><path d="M728.332196 822.207562c-159.333908 0-284.139474-40.552817-284.139474-92.345329a6.024476 6.024476 0 0 1 6.024476-5.934558 5.934559 5.934559 0 0 1 5.934559 5.934558c0 38.844384 109.339747 80.386294 272.180439 80.386295s272.180439-41.54191 272.180439-80.9258a6.024476 6.024476 0 1 1 11.959035 0c0 51.882429-124.805566 92.435247-284.139474 92.435247z" fill="#F1B622" p-id="8623"></path><path d="M450.217198 432.594796c0 47.926057 124.535814 86.320853 278.114998 86.320853s278.114998-38.664549 278.114998-86.320853" fill="#F34541" p-id="8624"></path><path d="M728.332196 524.850208c-159.333908 0-284.139474-40.4629-284.139474-92.255412a6.024476 6.024476 0 0 1 11.959035 0C456.151757 471.43918 565.491504 512.531503 728.332196 512.531503s272.180439-41.451993 272.180439-80.296377a6.024476 6.024476 0 1 1 11.959035 0c0 52.152182-124.805566 92.615082-284.139474 92.615082z" fill="#F1B622" p-id="8625"></path><path d="M728.332196 452.916164c-159.333908 0-284.139474-40.4629-284.139474-92.255412S568.998289 268.315423 728.332196 268.315423s284.139474 40.642735 284.139474 92.345329S887.666104 452.916164 728.332196 452.916164z" fill="#F6C48B" p-id="8626"></path><path d="M728.332196 274.160064c153.579184 0 278.114998 38.754466 278.114998 86.320853s-124.535814 86.320853-278.114998 86.320853-278.114998-38.574631-278.114998-86.320853 124.535814-86.320853 278.114998-86.320853m0-11.959035c-140.631056 0-290.16395 34.528341-290.16395 98.279888S587.70114 458.58097 728.332196 458.58097s290.16395-34.438424 290.16395-98.369806S868.963252 262.201029 728.332196 262.201029z" fill="#FA993F" p-id="8627"></path><path d="M677.978365 368.663415l-34.168671 57.816987L576.101775 395.638681 494.546553 92.166933 534.38003 17.98495l62.222948 46.847046z" fill="#F34541" p-id="8628"></path><path d="M644.439117 432.594796a6.923652 6.923652 0 0 1-2.427774-0.989093l-68.337342-30.482051a5.664806 5.664806 0 0 1-3.326949-3.956373l-81.824975-303.201995a6.204311 6.204311 0 0 1 0-4.40596l39.743559-74.181983a6.204311 6.204311 0 0 1 3.956373-3.057197 5.844641 5.844641 0 0 1 4.855548 1.07901l62.222948 46.936964a5.395053 5.395053 0 0 1 2.158021 3.237032L683.373419 367.854157a5.574888 5.574888 0 0 1 0 4.495877l-33.988836 57.727071a5.934559 5.934559 0 0 1-4.945466 2.517691z m-62.942288-40.82257l59.975009 26.975267 30.122381-51.073172-80.026624-299.605293L536.538051 26.976705l-35.967022 65.909568z" fill="#F1B622" p-id="8629"></path><path d="M545.260054 170.21537l0.629423-1.07901H548.497086a6.474064 6.474064 0 0 0 2.158021-1.258846 19.242357 19.242357 0 0 1 3.506785-2.517692l2.607609-1.978186a23.468482 23.468482 0 0 1 2.517692-1.528598 5.844641 5.844641 0 0 0 2.427774-1.888269 21.040708 21.040708 0 0 1-1.798351-2.427774 1.708434 1.708434 0 0 1 0-1.348763v-0.719341h-0.719341v-1.43868a1.528598 1.528598 0 0 1 0-0.899176l-0.899175-0.989093a18.882687 18.882687 0 0 1-0.539506-2.158021v-12.318705A6.563982 6.563982 0 0 1 557.488842 134.877771a14.476726 14.476726 0 0 0 0-2.787444l-0.899176-1.528598a1.888269 1.888269 0 0 0 0-0.989093v-2.60761l2.158021 0.809258a8.991756 8.991756 0 0 1 3.147115 0h0.989093a12.948128 12.948128 0 0 1 2.967279 2.517692 12.768293 12.768293 0 0 0 4.585795 2.877362h0.899176a3.506785 3.506785 0 0 1 2.337856 0l2.337857 1.438681a8.991756 8.991756 0 0 1 2.517691 1.888268 2.967279 2.967279 0 0 1 0 1.258846v1.168928h-6.294228a5.395053 5.395053 0 0 1-3.147115-0.71934l-1.079011 1.079011v1.798351a8.991756 8.991756 0 0 1 0 2.247938v5.215219a1.258846 1.258846 0 0 0 0 0.809258v2.517691h0.719341v2.877362h0.71934a0.809258 0.809258 0 0 0 0 0.629423h0.989093l4.04629-1.348763V152.861282a34.348506 34.348506 0 0 1 5.305136-0.809258 4.495878 4.495878 0 0 0 2.337857-0.71934h3.776537l1.618516 0.809258V152.861282a5.484971 5.484971 0 0 0 1.348763 1.528599v1.618516h-1.348763l-1.978186-0.629423v0.71934H584.464108l-1.348763 0.719341a7.283322 7.283322 0 0 1-2.877362 0.989093v0.539505H575.472353v0.719341l-1.348764 0.899175a6.024476 6.024476 0 0 0-2.247939 1.978186 5.844641 5.844641 0 0 1-1.348763 1.618516l-2.068104 1.168929-1.978186 1.798351h-0.899176a19.602027 19.602027 0 0 0-1.618516 1.888268 6.204311 6.204311 0 0 1-2.068103 1.978187 1.348763 1.348763 0 0 1-1.168928 0H557.488842a2.697527 2.697527 0 0 0-2.068104 0h-2.158022a8.991756 8.991756 0 0 1-2.607609-0.989094l-2.427774 3.956373-2.877361-2.517692z m19.781862 74.092066l0.719341-1.168928h2.517691a3.68662 3.68662 0 0 0 2.247939-1.348764c1.168928-0.899176 2.247939-1.708434 3.506785-2.517691l2.517691-1.888269 2.607609-1.528598a8.09258 8.09258 0 0 0 2.427774-1.798351l-1.798351-2.517692c0-0.629423-0.71934-0.989093-0.629423-1.258846v-0.809258h-0.71934a3.866455 3.866455 0 0 0 0-1.438681v-0.809258l-0.719341-1.07901a8.991756 8.991756 0 0 1 0-2.158022l-0.71934-2.697526v-2.877362a2.158021 2.158021 0 0 0 0-1.079011v-5.754723a9.801014 9.801014 0 0 1 0-3.237032 6.833734 6.833734 0 0 0 0-2.697527l-0.809258-1.618516v-2.158021l0.629423-1.438681 2.158021 0.899176a8.272415 8.272415 0 0 1 2.96728 0h1.07901a15.735572 15.735572 0 0 1 2.967279 2.517691 12.408623 12.408623 0 0 0 4.495878 2.877362h1.079011A2.517692 2.517692 0 0 1 593.455864 206.811815l2.427774 1.438681a15.375902 15.375902 0 0 1 2.337856 1.888269 3.776537 3.776537 0 0 1 0.629423 1.168928v1.258846h-4.945466a5.934559 5.934559 0 0 1-3.237032-0.719341l-0.899175 1.079011a3.057197 3.057197 0 0 0 0 1.798351 10.160684 10.160684 0 0 1 0 2.337857 2.967279 2.967279 0 0 0 0 1.258845v4.675713l0.539505 1.618516v3.776538h0.719341v0.539505h0.989093l3.956372-1.258846a25.806338 25.806338 0 0 1 5.125301-0.989093 4.855548 4.855548 0 0 0 2.517691-0.71934h3.596702l1.798352 0.71934v1.079011l1.348763 1.438681v1.43868h-2.607609v0.629423h-1.708434l-1.168928 0.809258a17.264171 17.264171 0 0 1-2.877362 0.809258v0.899176h-1.978186l-2.337856 0.629423v0.71934l-1.348764 0.809258a6.474064 6.474064 0 0 0-2.247939 2.158022c-0.71934 0.989093-1.258846 1.438681-1.43868 1.43868l-1.978187 1.168929-1.978186 1.798351h-0.899175a8.991756 8.991756 0 0 0-1.618516 1.978186 5.934559 5.934559 0 0 1-2.068104 1.888269H575.472353a9.711096 9.711096 0 0 1-2.697527-0.989093l-1.618516-0.719341-3.237032-3.237032z m14.027139 85.871265a6.653899 6.653899 0 0 1 0-1.888269l1.438681-2.877361a23.108812 23.108812 0 0 0 2.068103-5.395054 58.536328 58.536328 0 0 1 2.877362-8.45225 38.304878 38.304878 0 0 0 2.607609-7.642992 73.91223 73.91223 0 0 1 5.395054-12.948128 11.059859 11.059859 0 0 1-4.675713-2.427774l-0.899176-1.618516v-0.899176a2.247939 2.247939 0 0 0-1.978186 0.719341l-2.787444 2.337856a17.983511 17.983511 0 0 0-3.32695 5.934559 3.057197 3.057197 0 0 1-2.787444 0 5.215218 5.215218 0 0 1-1.708434-1.438681 4.04629 4.04629 0 0 1-1.168928-1.528598 4.40596 4.40596 0 0 1 1.348764-2.517692c3.32695-5.035383 5.484971-8.272415 6.204311-9.711096l1.079011-2.247939h-4.316043a2.247939 2.247939 0 0 1-1.798351-0.989093 18.523016 18.523016 0 0 1 3.776537-1.888269 9.890931 9.890931 0 0 1 5.125301 0 12.948128 12.948128 0 0 1 3.596702 1.618516l1.168928 1.258846a17.264171 17.264171 0 0 1-2.517691 6.114394h1.348763a46.757129 46.757129 0 0 0 1.888269-4.945466l1.258845-5.305135v8.272415l0.719341 1.618516v1.07901a3.776537 3.776537 0 0 1 1.618516-1.258845l4.675713 1.258845a2.337856 2.337856 0 0 0 0.989093 1.258846h0.989093l1.168928-0.899175 1.708434-2.517692 2.158021-3.147114c0.809258-1.079011 1.168928-1.888269 0.809258-2.427774l-0.719341-0.899176a19.062522 19.062522 0 0 0-4.76563 5.934559 4.495878 4.495878 0 0 1-4.226125-1.168928 4.585795 4.585795 0 0 1-1.348763-1.888269 1.798351 1.798351 0 0 1 0-0.989093l3.506784-11.149777v-0.71934a8.002662 8.002662 0 0 1-4.76563-1.079011l-1.708434 1.348763a1.888269 1.888269 0 0 1 0.809258-1.348763 9.621178 9.621178 0 0 1 1.978187-1.348763 30.751804 30.751804 0 0 1 5.754723 0c2.967279 0 4.76563 0.629423 5.215218 1.798351l0.719341 1.978186a4.76563 4.76563 0 0 1 0 2.247939 12.588458 12.588458 0 0 0 0 2.607609 17.444006 17.444006 0 0 0 6.024476-3.416867h6.204311v1.618516a26.166009 26.166009 0 0 0-6.204311 2.427774 14.296891 14.296891 0 0 1-3.68662 1.438681l0.809258 0.989093 2.247939 0.539505c1.438681 0.539505 2.337856 1.258846 2.517692 2.158021a4.40596 4.40596 0 0 1 0 2.158022l-1.438681 1.978186a11.149777 11.149777 0 0 1-1.978187 1.618516l-3.237032 2.697527h-3.057196l-1.888269 0.899175a51.522759 51.522759 0 0 0 17.354088 7.193404 41.362075 41.362075 0 0 1 7.553075 1.258846h1.618516s0 0.809258 0.71934 0.809258h1.168928l1.168929 0.629423h1.798351l2.158021 1.168928 2.697527 1.528599 2.517691 2.158021 1.438681 0.989093 0.719341 1.438681 0.71934 0.719341V305.721126h-2.158021a34.348506 34.348506 0 0 0-6.743817-0.71934l-7.193404-1.438681-8.991756 0.629423h-0.71934l-2.517692-0.719341-4.04629-1.978186v0.629423l-7.373239 3.956372h-1.528599l-1.07901 0.629423h-1.348764l-1.528598 0.719341-2.068104 0.539505a2.967279 2.967279 0 0 0-1.798351 2.158021 6.833734 6.833734 0 0 1 1.798351 1.798351v1.618516l0.899176 1.258846v1.348763h0.809258a4.495878 4.495878 0 0 0 1.708433 4.04629l2.877362 2.877362a5.395053 5.395053 0 0 0 2.247939-1.168928 28.234112 28.234112 0 0 0 4.04629-5.664806 25.176915 25.176915 0 0 0 3.057197-5.484971 3.416867 3.416867 0 0 0 0-1.348763c0-0.71934-1.258846-1.258846-2.697527-1.798351s0 1.079011 0 1.258845l-0.899176 1.258846 0.719341 2.337857v0.809258l-0.989093 2.787444a10.969942 10.969942 0 0 1-5.844641-2.697527 11.149777 11.149777 0 0 1-3.506785-3.596702l2.877362 0.899176c1.528598-1.168928 3.057197-2.337856 4.40596-3.596703a7.912745 7.912745 0 0 1 8.991755 0l3.866455 3.057197h0.719341v3.506785a4.945466 4.945466 0 0 1-1.348764 3.237032 3.506785 3.506785 0 0 1-3.326949 1.708433v0.809258h2.427774c1.528598 0 2.517692 0 2.967279-1.07901h0.719341a10.250601 10.250601 0 0 0 3.866455 2.158021 9.711096 9.711096 0 0 1 4.136207 2.427774l-4.316043 0.809258a4.76563 4.76563 0 0 1-2.247938 0H620.43113a26.975267 26.975267 0 0 0-12.048952 4.76563 48.105892 48.105892 0 0 1-6.833734 4.226125l-3.32695-0.989093a1.438681 1.438681 0 0 1 0.539505 1.888269 1.438681 1.438681 0 0 1-1.888268 0.539505l-2.607609-1.438681c-1.168928-0.539505-1.798351-1.079011-1.798352-1.43868H593.455864a3.776537 3.776537 0 0 0 2.427774-1.438681l1.978186-1.348764 2.787444-2.247938h1.798351a12.768293 12.768293 0 0 1-3.68662-2.517692 14.746479 14.746479 0 0 1-1.438681-2.607609 6.024476 6.024476 0 0 0-1.168928-2.877362 3.866455 3.866455 0 0 1 0-1.438681v-3.866455l0.719341-4.04629c0.809258 0 1.258846 0 1.438681-0.989093s-1.348763-0.71934-1.618516-0.629423a1.798351 1.798351 0 0 0 0 0.989094l-1.528599 4.76563-1.888269 3.866455c-0.809258 1.528598-1.618516 3.057197-2.517691 4.40596l-3.596702 4.136208a12.318705 12.318705 0 0 1-1.438681 1.888268l-2.068104 1.348764-0.71934 0.71934h-0.989093z m15.735572-41.451993z m1.618516 13.757386h0.989093l2.517692-1.528598a15.285984 15.285984 0 0 1 4.585795-2.158022h1.258846l2.517691 0.719341h1.348763l2.068104 1.258845c-3.596702-2.607609-7.193404-5.484971-10.700189-8.45225v1.978186l-1.708433 1.798352c-1.079011 2.068104-1.978186 4.226125-2.877362 6.384146z" fill="#F6C48B" p-id="8630"></path><path d="M899.804974 395.638681l-58.536329 32.999743L797.838466 368.663415l81.465305-303.921337 71.394539-44.329354 30.482051 71.934044z" fill="#F34541" p-id="8631"></path><path d="M841.268645 435.112488a6.204311 6.204311 0 0 1-4.76563-2.517692l-43.520097-59.975009a6.204311 6.204311 0 0 1-0.899175-5.035383l81.465305-303.921336a5.664806 5.664806 0 0 1 2.607609-3.506785l71.934044-44.419272a6.384146 6.384146 0 0 1 4.945465-0.629423 5.664806 5.664806 0 0 1 3.68662 3.326949l30.482051 71.934045a7.37324 7.37324 0 0 1 0 3.956372l-81.64514 303.201996a6.114394 6.114394 0 0 1-2.877361 3.776537l-58.446411 32.909825a6.743817 6.743817 0 0 1-2.96728 0.899176z m-36.956115-67.528084l38.664549 53.950533 51.612677-29.133288 80.206459-299.695211-26.975267-62.942289-63.841464 39.653642z" fill="#F1B622" p-id="8632"></path><path d="M884.159319 157.806748l1.079011-0.629423h0.71934l1.618516 0.989093a5.934559 5.934559 0 0 0 2.517692 0h7.373239a8.991756 8.991756 0 0 1 3.147115 0 8.182498 8.182498 0 0 0 2.967279 0 17.983511 17.983511 0 0 1 0-3.147114v-6.384147a19.512109 19.512109 0 0 1 0.629423-2.158021l0.71934-2.607609 0.989093-1.798351v-0.809258l0.899176-0.899176v-1.258846l2.158021-2.517691v-1.258846a10.610272 10.610272 0 0 1 1.978187-2.517691 6.653899 6.653899 0 0 0 1.528598-2.247939 6.024476 6.024476 0 0 0 0-1.798351v-1.888269l1.168928-0.989093 1.618516 1.798351a14.746479 14.746479 0 0 1 2.337857 1.798351h1.07901a25.716421 25.716421 0 0 1 1.348764 3.68662 12.408623 12.408623 0 0 0 2.427774 4.855548h1.07901a3.147114 3.147114 0 0 1 1.798351 1.258846l1.258846 2.517691a11.059859 11.059859 0 0 1 1.258846 2.787444v2.068104h-0.719341l-2.337856-1.258846h-1.258846l-1.07901-0.539505V143.869527a5.215218 5.215218 0 0 1-2.427774-2.158022h-1.438681a4.316043 4.316043 0 0 0-1.438681 2.158022 9.531261 9.531261 0 0 1-1.438681 1.888268l-0.71934 1.079011v0.719341l-0.809258 1.07901v4.04629l-0.719341 0.629423-1.348763 2.607609v1.348764a0.809258 0.809258 0 0 0 0 0.629422h0.809258l4.226125 0.809258a33.539248 33.539248 0 0 1 5.035383 1.798352 6.923652 6.923652 0 0 0 2.427774 0.71934l1.528598 0.629423 0.719341 0.899175 1.07901 1.618516v1.079011a6.653899 6.653899 0 0 0 0 1.888269 0.71934 0.71934 0 0 0 0 0.629423v1.258845h-1.43868a4.40596 4.40596 0 0 1-1.438681-1.438681h-1.708434a3.866455 3.866455 0 0 0-1.438681 0 8.002662 8.002662 0 0 1-2.877361-0.71934H917.159062l-2.517692-0.539505h-1.528598a5.934559 5.934559 0 0 0-3.057197 0.539505l-1.888269 0.71934h-5.844641a11.959035 11.959035 0 0 0-2.517691 0.899176 5.484971 5.484971 0 0 1-2.697527 0.71934 2.158021 2.158021 0 0 1-1.168928-0.629422h-2.247939a5.215218 5.215218 0 0 0-1.708434-1.438681 8.09258 8.09258 0 0 1-1.708433-1.348764 9.890931 9.890931 0 0 1-1.888269-1.978186l-1.07901-1.618516-1.168929-4.316042z m-19.961697 74.092065l1.079011-0.629423h0.71934l1.528598 0.989093a6.384146 6.384146 0 0 0 2.60761 0h7.463157a8.182498 8.182498 0 0 1 2.967279 0 8.991756 8.991756 0 0 0 3.057197 0 17.983511 17.983511 0 0 1 0-3.147114 1.708434 1.708434 0 0 1 0-1.348763l0.539505-1.528599v-0.71934a1.798351 1.798351 0 0 0 0-1.348763v-4.405961l0.719341-2.607609 0.989093-1.708433v-1.168929l0.809258-0.899175v-1.258846l2.068103-2.517691v-1.258846a16.724665 16.724665 0 0 1 1.888269-2.517692 7.103487 7.103487 0 0 0 1.708434-2.247939v-3.686619l1.168928-0.899176 1.618516 1.798351a9.621178 9.621178 0 0 1 2.517691 1.708434h0.899176a14.027139 14.027139 0 0 1 1.348763 3.686619 12.588458 12.588458 0 0 0 2.517692 4.855548h0.989093a3.147114 3.147114 0 0 1 1.798351 1.258846l1.258846 2.517692a11.689282 11.689282 0 0 1 1.258846 2.877362 3.416867 3.416867 0 0 1 0 1.348763h-0.899176l-2.158021-1.168928h-1.258846l-1.079011-0.629423v-0.719341a4.585795 4.585795 0 0 1-2.337856-2.158021h-1.438681a4.585795 4.585795 0 0 0-1.168928 1.438681 8.991756 8.991756 0 0 1-1.348764 1.888269l-0.809258 1.07901v0.719341l-0.71934 1.07901v0.899176l-0.71934 0.71934a0.989093 0.989093 0 0 0 0 0.719341V224.795326l-0.809258 0.539506v0.629423l-1.348764 2.517691v1.888269c0 0.629423 0 0 0.989093 0l4.04629 0.899175a32.550155 32.550155 0 0 1 4.945466 1.798351 5.484971 5.484971 0 0 0 2.337856 0.719341l0.899176 0.539505v0.899176h0.809258l0.71934 0.899175 1.168928 1.618516V242.778837h0.629423v-0.629422l-1.438681-1.528599h-3.147114a16.454913 16.454913 0 0 1-2.877362-0.629423H899.175551l-2.337857-0.629423h-2.337856a11.869117 11.869117 0 0 0-2.967279 0.719341l-1.888269 0.629423h-8.991755a7.103487 7.103487 0 0 0-2.337857 0.71934 5.215218 5.215218 0 0 1-2.787444 0.719341h-1.079011v-0.719341H872.200284a4.495878 4.495878 0 0 0-1.528598-1.348763l-1.798351-1.258846-1.798351-2.158021a10.610272 10.610272 0 0 0-1.168929-1.618516l-1.258845-4.316043z m-30.841721 80.9258l1.43868-1.348764a26.076091 26.076091 0 0 1 2.60761-1.708433 25.176915 25.176915 0 0 0 4.495877-3.68662 42.980591 42.980591 0 0 1 6.294229-5.844641 39.204054 39.204054 0 0 0 6.114394-5.395053 90.816731 90.816731 0 0 1 11.059859-8.542168 11.509447 11.509447 0 0 1-2.787444-4.40596 7.283322 7.283322 0 0 1 0-1.978186H863.208529a2.247939 2.247939 0 0 0-2.158022 0l-3.506784 0.629423a17.174253 17.174253 0 0 0-5.844641 3.596702 4.226125 4.226125 0 0 1-2.427774-1.438681 20.23145 20.23145 0 0 1-0.719341-2.158021 3.32695 3.32695 0 0 1 0-1.888269 6.384146 6.384146 0 0 1 2.158022-1.708434 90.546978 90.546978 0 0 0 10.160683-5.305135l2.158022-1.438681-3.057197-3.32695a1.798351 1.798351 0 0 1-1.079011-1.708433 15.196067 15.196067 0 0 1 4.04629 0 10.430436 10.430436 0 0 1 4.40596 2.877361 11.509447 11.509447 0 0 1 2.337857 3.147115v1.708433a19.062522 19.062522 0 0 1-5.305136 3.956373l0.989093 0.809258a36.506527 36.506527 0 0 0 4.136208-3.237032l3.776537-3.956373v0.629423l-0.71934 1.258846a8.45225 8.45225 0 0 0-1.079011 1.978186l-2.787444 3.68662v2.607609a4.04629 4.04629 0 0 1 2.068103 0l3.416868 3.416867a2.517692 2.517692 0 0 0 0 1.618516l0.71934 0.719341h1.618516l2.607609-1.168929a31.830815 31.830815 0 0 1 3.506785-1.708433q1.888269-0.809258 1.888268-1.618516v-1.348763a26.435761 26.435761 0 0 0-7.193404 2.967279 5.215218 5.215218 0 0 1-3.147114-3.237032 4.76563 4.76563 0 0 1 0-2.247939v-0.809258l8.991755-7.912745v-0.899175a8.272415 8.272415 0 0 1-3.596702-3.416867h-1.978186s0-0.629423 1.438681-0.719341a4.76563 4.76563 0 0 1 2.247938 0 17.983511 17.983511 0 0 1 5.125301 2.517692c2.697527 1.438681 3.866455 2.877362 3.596702 4.04629a7.463157 7.463157 0 0 0 0 2.158021 8.991756 8.991756 0 0 1-1.528598 1.708433 7.463157 7.463157 0 0 0-1.798351 1.978187 15.016232 15.016232 0 0 0 6.923651 0 11.689282 11.689282 0 0 0 2.068104 1.618516 7.73291 7.73291 0 0 1 1.798351 1.168928v0.71934l0.629423 0.809258v0.719341h-0.899175a41.092323 41.092323 0 0 0-6.653899-1.079011 14.566644 14.566644 0 0 1-3.956373 0 2.158021 2.158021 0 0 0 0 1.079011 14.656561 14.656561 0 0 0 1.528599 1.798351 2.877362 2.877362 0 0 1 1.07901 3.147114 4.76563 4.76563 0 0 1-0.989093 1.888269l-2.158021 0.899176a11.7792 11.7792 0 0 1-2.517692 0H881.19204l-2.877362-0.989094h-2.158021a51.702594 51.702594 0 0 0 11.959035 14.836397 50.713501 50.713501 0 0 1 5.844641 4.855548 4.226125 4.226125 0 0 0 1.348763 1.168928 1.168928 1.168928 0 0 0 0 1.079011l0.809258 0.989093v1.168928h0.989093v0.809258l1.168928 2.158021 1.708434 2.697527 1.079011 3.057197a3.32695 3.32695 0 0 0 0.71934 1.618516v2.517692h-0.809258l-0.899176-0.989094a34.528341 34.528341 0 0 0-5.48497-4.04629l-5.305136-4.585795-7.553075-5.395053v-0.989093l-1.888268-1.798351-2.517692-3.776538h-10.880024l-1.079011-0.71934h-3.68662a2.877362 2.877362 0 0 0-2.427774 1.528598 5.574888 5.574888 0 0 1 0 2.427774v4.40596a9.890931 9.890931 0 0 0 0 4.585796l1.168929 3.776537a5.574888 5.574888 0 0 0 2.427774 0 28.953453 28.953453 0 0 0 6.384146-2.877362 33.809001 33.809001 0 0 0 5.484971-3.237032v-0.809258a3.68662 3.68662 0 0 0-1.348763-2.877361s-0.71934 0.809258-0.899176 0.809258l-1.348763 0.71934v2.337856l-2.158022 1.888269a9.531261 9.531261 0 0 1-3.866455-5.305136 12.048952 12.048952 0 0 1-1.258845-4.76563l2.158021 2.158021a55.479131 55.479131 0 0 0 5.574888-0.809258 8.45225 8.45225 0 0 1 7.553075 4.136208L881.19204 314.712881v0.719341l-0.989093 1.978186v1.258846c0 0.809258-1.168928 1.528598-2.787444 2.158021a3.237032 3.237032 0 0 1-4.316043 1.798351 5.125301 5.125301 0 0 1-1.168928-0.71934h-2.068104a13.847303 13.847303 0 0 1 3.057197 1.528598c1.258846 0.71934 2.247939 0.989093 3.057197 0h0.71934a10.700189 10.700189 0 0 0 2.337857 3.68662 10.250601 10.250601 0 0 1 2.337856 4.226125l-4.136208-1.348763-2.068103-0.809258-2.96728-1.618516a26.975267 26.975267 0 0 0-13.127963-2.068104 59.435504 59.435504 0 0 1-8.002662 0l-2.427774-2.517691h-0.809258l-1.168928-1.888269c-0.899176-1.258846-1.079011-1.978186-0.899176-2.337857a4.675713 4.675713 0 0 0 0.989093 0.899176 7.73291 7.73291 0 0 0 2.877362 0h7.283322a12.13887 12.13887 0 0 1-1.978186-4.226125 8.272415 8.272415 0 0 1 0-2.967279v-3.057197a2.517692 2.517692 0 0 1 0-1.618516l0.809258-1.618516 0.71934-1.258846 2.607609-3.237032h1.798351s-0.71934-1.258846-0.899175-1.258846h-1.079011l-3.776537 3.416867-3.596702 2.517692a49.454655 49.454655 0 0 1-4.316043 2.517691l-5.125301 1.618516c-1.438681 0.71934-2.158021 1.079011-2.247939 0.989093H836.233262z m29.04337-15.016232a1.528598 1.528598 0 0 0 0.899175 0h2.877362a23.558399 23.558399 0 0 1 5.035383 0H872.200284l1.798351 1.888269 0.989094 0.71934 1.168928 2.158022c-1.348763-3.147114-2.787444-7.013569-4.316043-11.509448h-0.809258l-1.079011 0.989094-2.337856 0.71934a49.454655 49.454655 0 0 0-5.574888 5.035383z m5.395053-12.85821z" fill="#F6C48B" p-id="8633"></path><path d="M742.719005 440.597459a6.114394 6.114394 0 0 1-3.866455-1.348764l-57.547235-46.936963a6.204311 6.204311 0 0 1-2.158021-4.675713v-314.711443a5.754724 5.754724 0 0 1 1.708433-4.04629l57.547235-60.964102a5.664806 5.664806 0 0 1 4.316043-1.888269 5.574888 5.574888 0 0 1 4.40596 2.337856l48.015975 61.323773a5.934559 5.934559 0 0 1 1.258845 3.68662v314.711442a5.664806 5.664806 0 0 1-1.798351 4.316043L746.315707 439.248695a6.114394 6.114394 0 0 1-3.596702 1.348764z" fill="#FFCB27" p-id="8634"></path><path d="M742.719005 11.960474l47.926057 61.41369v314.711442l-47.926057 46.847047-57.547235-46.936964v-314.711443l57.547235-61.41369m0-11.869117a11.959035 11.959035 0 0 0-8.991756 3.866455l-57.547235 61.323772a12.048952 12.048952 0 0 0-3.237032 8.182498v314.711442a12.228787 12.228787 0 0 0 4.40596 8.991756l57.547236 46.936964a11.869117 11.869117 0 0 0 15.915407-0.719341l48.015974-46.936964a12.49854 12.49854 0 0 0 3.596703-8.991755v-314.711443a11.959035 11.959035 0 0 0-2.158022-6.563981L752.160348 4.677152a12.048952 12.048952 0 0 0-8.991755-4.675713z" fill="#FA993F" p-id="8635"></path><path d="M713.85547 161.853038l0.809258-0.989093h0.71934l1.798351 0.629423a4.945466 4.945466 0 0 0 2.517692-0.629423l3.956372-1.618516 2.96728-1.079011 3.057197-0.899175a5.215218 5.215218 0 0 0 2.697526-1.168929 27.424854 27.424854 0 0 1-1.168928-2.877361 3.416867 3.416867 0 0 1 0-1.348764v-1.258846a3.866455 3.866455 0 0 0 0-1.43868v-2.158022a5.125301 5.125301 0 0 1 0-2.337856v-5.215218a2.158021 2.158021 0 0 0 0-1.079011v-1.348763l1.438681-2.96728v-1.258845a11.959035 11.959035 0 0 1 1.168928-2.96728c0.71934-1.348763 1.079011-2.158021 0.989093-2.607609a5.395053 5.395053 0 0 0 0-1.708433v-2.158022l0.809258-1.168928 1.978186 1.258846a15.735572 15.735572 0 0 1 2.877362 1.07901h1.079011a26.076091 26.076091 0 0 1 2.247939 3.237032 10.880024 10.880024 0 0 0 3.596702 3.956373h0.989093a2.967279 2.967279 0 0 1 2.158021 0.899175l1.888269 1.978187a12.49854 12.49854 0 0 1 1.888269 2.427774 3.416867 3.416867 0 0 1 0 1.348763v0.539505h-5.754724v-0.539505a5.844641 5.844641 0 0 1-2.877362-1.438681l-1.258845 0.719341a2.967279 2.967279 0 0 0-0.719341 1.618516 7.822827 7.822827 0 0 1-0.899175 2.158021V152.861282a1.618516 1.618516 0 0 1 0 2.158022 1.618516 1.618516 0 0 1-2.158022-0.449588h5.215218a28.953453 28.953453 0 0 1 6.833735 0 11.7792 11.7792 0 0 0 2.517691 0h2.337857l0.989093 0.809258 1.438681 1.258846v0.899175a7.013569 7.013569 0 0 0 0.989093 1.798351v1.618516h-1.079011l-1.798351-1.07901v0.539505h-3.147115a5.574888 5.574888 0 0 1-2.967279 0h-4.495878a0.71934 0.71934 0 0 1 0 0.629423h-1.43868a7.822827 7.822827 0 0 0-2.697527 1.438681c-0.989093 0.71934-1.438681 1.079011-1.708434 1.07901l-2.247939 0.629423-2.427774 1.168928h-0.809258a7.013569 7.013569 0 0 0-2.158021 1.438681 7.013569 7.013569 0 0 1-2.517691 1.438681 2.967279 2.967279 0 0 1-1.258846 0h-3.057197a6.294229 6.294229 0 0 0-2.068104-0.989093l-1.888268-0.71934a11.419529 11.419529 0 0 1-2.427774-1.618516l-4.04629 1.618516-2.337857-3.776538z m0 76.340004l0.809258-0.899175h2.517691a5.934559 5.934559 0 0 0 2.517692 0l3.956372-1.708434 2.96728-1.079011 3.057197-0.809258a6.204311 6.204311 0 0 0 2.697526-1.258845 27.424854 27.424854 0 0 1-1.168928-2.877362 2.967279 2.967279 0 0 1 0-1.258846v-1.348763a3.866455 3.866455 0 0 0 0-1.438681v-2.158021a4.76563 4.76563 0 0 1 0-2.247939V215.803571a2.158021 2.158021 0 0 0 0-1.079011v-1.258846l1.438681-3.057196v-1.168929a10.790107 10.790107 0 0 1 1.168928-2.967279 5.395053 5.395053 0 0 0 0.989093-2.697527 4.855548 4.855548 0 0 0 0-1.618516v-2.158021l0.809258-1.258846 1.978186 1.348764a8.991756 8.991756 0 0 1 2.877362 1.07901h1.079011a20.770955 20.770955 0 0 1 2.247939 3.237032 10.880024 10.880024 0 0 0 3.596702 3.956373h0.989093a2.877362 2.877362 0 0 1 2.158021 0.809258l1.888269 2.068103a12.49854 12.49854 0 0 1 1.888269 2.427774 2.967279 2.967279 0 0 1 0 1.258846h-0.809258l-2.427774-0.539505h-2.517692v-0.629423a5.844641 5.844641 0 0 1-2.877362-1.438681l-1.258845 0.719341a2.967279 2.967279 0 0 0-0.719341 1.708433 10.969942 10.969942 0 0 1-0.899175 2.158021v7.553075l-2.517692 2.247939v3.506785a0.539505 0.539505 0 0 1 0 0.539505h5.215218a25.266833 25.266833 0 0 1 5.215218 0 5.934559 5.934559 0 0 0 2.517692 0h0.989093l0.629423 0.629423H755.307463l0.989093 0.809258 1.438681 1.258846V233.787082a11.149777 11.149777 0 0 0 0.899175 1.798351v0.989093h0.539506l-2.877362 1.528599v0.629422h-2.787444a8.182498 8.182498 0 0 1-2.96728 0H746.315707a0.71934 0.71934 0 0 1 0 0.629423h-1.438681a7.822827 7.822827 0 0 0-2.697526 1.438681c-0.989093 0.71934-1.438681 1.079011-1.708434 1.079011h-2.247939l-2.697526 0.899175h-0.809258a7.013569 7.013569 0 0 0-2.158022 1.438681 7.013569 7.013569 0 0 1-2.517691 1.438681h-4.316043a6.294229 6.294229 0 0 0-2.068104-0.989093l-1.888268-0.71934A11.419529 11.419529 0 0 1 719.340441 242.778837l-1.438681-1.258845-2.247939-3.776538z m-8.991756 86.950276a2.068104 2.068104 0 0 1 0.989093-1.708434l2.158022-2.337856a25.806338 25.806338 0 0 0 3.326949-4.855548 64.111217 64.111217 0 0 1 4.855548-7.283322 37.855291 37.855291 0 0 0 4.40596-6.743817 78.048438 78.048438 0 0 1 8.991756-11.149777 10.430436 10.430436 0 0 1-3.956372-3.416867v-1.978186l0.71934-0.539505a2.697527 2.697527 0 0 0-2.337856 0l-3.057197 1.528598a15.915407 15.915407 0 0 0-4.855548 4.945466 5.754724 5.754724 0 0 1-2.697527-0.719341l-1.258846-1.978186a3.956372 3.956372 0 0 1-0.71934-1.798351s0-1.079011 1.708433-2.158021c4.585795-3.956372 7.463157-6.563982 8.991756-7.73291l1.618516-1.978186-4.40596-1.079011a1.708434 1.708434 0 0 1-1.438681-1.258846 12.228787 12.228787 0 0 1 3.956372-0.989093 11.239694 11.239694 0 0 1 5.035383 1.708434 14.746479 14.746479 0 0 1 2.96728 2.337856 6.833734 6.833734 0 0 1 0.809258 1.618516 27.874442 27.874442 0 0 1-4.04629 5.125301h1.168928a37.49562 37.49562 0 0 0 3.237032-4.226125l2.517691-4.765631v2.158022a8.002662 8.002662 0 0 0 0 2.068103l-1.618516 4.316043v3.956373c1.079011 0 1.798351-0.71934 1.978187-0.629423L737.323952 287.737615a2.517692 2.517692 0 0 0 0 1.618516l0.809258 0.539505 1.438681-0.71934 2.247938-1.798351 2.877362-2.517692c1.258846-0.899176 1.708434-1.528598 1.438681-2.158021v-1.168928a23.198729 23.198729 0 0 0-6.114394 4.585795 4.76563 4.76563 0 0 1-3.776537-2.337856 5.574888 5.574888 0 0 1-0.71934-2.158022 1.528598 1.528598 0 0 1 0-0.899175l6.204311-9.890931V269.754104a8.362333 8.362333 0 0 1-4.316043-2.427774l-2.068104 0.989093s0-0.629423 1.079011-1.079011l2.158021-0.71934a28.6837 28.6837 0 0 1 5.664806 1.079011c2.967279 0.809258 4.585795 1.798351 4.675713 3.147114a11.239694 11.239694 0 0 1 0 1.978186 8.991756 8.991756 0 0 1-1.07901 2.068104 8.991756 8.991756 0 0 0-1.168929 2.517692 18.792769 18.792769 0 0 0 6.6539-1.798352 12.85821 12.85821 0 0 0 2.427773 0.809258l2.068104 0.899176h0.719341V278.745859l-0.719341 0.719341a28.6837 28.6837 0 0 0-6.743816 0.71934 14.566644 14.566644 0 0 1-3.956373 0 6.743817 6.743817 0 0 0 0.629423 1.079011l1.978186 1.258846a2.967279 2.967279 0 0 1 1.798351 2.697526 4.40596 4.40596 0 0 1 0 2.158022 5.395053 5.395053 0 0 1-1.888268 1.258846 8.002662 8.002662 0 0 1-2.247939 1.258845h-7.103487A52.511852 52.511852 0 0 0 755.307463 301.405083a51.882429 51.882429 0 0 1 6.833734 3.147115 4.136208 4.136208 0 0 0 2.158021 1.168928v0.989093l1.079011 0.809258 0.989093 0.809258h0.899176l0.71934 0.719341 1.708434 1.798351 2.247938 2.158021 1.798352 2.697527a6.294229 6.294229 0 0 0 1.258845 1.43868v2.96728H773.290974l-1.079011-0.719341a55.299296 55.299296 0 0 0-6.384146-2.517691l-6.294229-2.96728-7.553075-2.787444a1.258846 1.258846 0 0 1 0-0.809258l-2.337856-1.348763L746.315707 305.721126v0.539505l-8.182497 1.978187h-7.822828a2.967279 2.967279 0 0 0-1.888268 1.978186 8.182498 8.182498 0 0 1 1.168928 2.337856v4.316043h0.71934a10.880024 10.880024 0 0 0 0.629423 4.495878l2.068104 3.416867a5.125301 5.125301 0 0 0 2.337856 0 26.076091 26.076091 0 0 0 5.395054-4.495878 21.310461 21.310461 0 0 0 4.495878-4.495878 3.416867 3.416867 0 0 0 0-1.348763 4.585795 4.585795 0 0 0-2.068104-2.427774v1.07901l-1.168928 1.079011v3.147115l-1.708434 2.427774a10.340519 10.340519 0 0 1-5.035383-4.136208 12.768293 12.768293 0 0 1-2.427774-4.316043l2.517691 1.438681a26.975267 26.975267 0 0 0 5.305136-2.158021c2.427774-1.168928 5.125301 0 8.362333 2.068104l3.147114 2.967279h0.629423v3.506785a4.945466 4.945466 0 0 1-2.337856 2.877361 3.506785 3.506785 0 0 1-3.596702 0.719341L746.315707 323.704637h-0.989093a17.084335 17.084335 0 0 1 3.237032 0.809258 3.237032 3.237032 0 0 0 3.147115 0h0.809258a8.991756 8.991756 0 0 0 3.147114 2.967279 13.038045 13.038045 0 0 1 3.506785 3.506785h-6.653899l-4.04629-0.989093a30.032463 30.032463 0 0 0-13.217881 1.438681 53.950533 53.950533 0 0 1-7.642992 2.427774l-2.96728-1.798351h-0.71934l-1.798351-1.528599c-0.989093-0.899176-1.438681-1.618516-1.348763-1.978186h1.168928a6.743817 6.743817 0 0 0 2.697526-0.809258l2.247939-0.71934 3.32695-1.438681h1.348763a12.318705 12.318705 0 0 1-2.967279-3.506785 10.430436 10.430436 0 0 1-0.719341-2.877362v-2.967279a5.395053 5.395053 0 0 1 0-1.708434v-3.686619l1.798352-3.68662a2.517692 2.517692 0 0 0 1.618516 0c-0.539505-0.71934-1.079011-1.079011-1.258846-0.989093s0 0-0.899176 0.71934l-2.607609 4.226125-2.967279 3.32695a18.972604 18.972604 0 0 1-3.506785 3.596702l-4.585795 2.967279-1.888269 1.438681-2.158021 0.899176h-1.888269z m24.007988-22.209636h0.989093l2.877361-0.809258a21.939883 21.939883 0 0 1 4.855548-0.809258h1.348764l2.247939 1.258846h1.258845l1.618516 1.798351a126.514 126.514 0 0 1-8.09258-10.880025h-0.71934l-0.719341 1.168929-2.158021 1.348763a70.585281 70.585281 0 0 0-3.416867 6.923652z m1.798351-13.757386z" fill="#F34541" p-id="8636"></path><path d="M742.089582 638.41608a30.571969 30.571969 0 0 1-2.877362 3.057197 162.57094 162.57094 0 0 0 67.887754 29.582875 72.743302 72.743302 0 0 0-11.959034 15.735572 175.159397 175.159397 0 0 1-33.269496-12.408622v11.059859h-68.876847V674.383102a304.640677 304.640677 0 0 1-32.280402 13.307798 72.743302 72.743302 0 0 0-11.959035-15.735572 166.167642 166.167642 0 0 0 77.059345-41.991498l8.991755 4.316042c-4.226125-2.697527-9.711096-6.474064-13.21788-8.45225h-17.983511a64.650722 64.650722 0 0 1 6.833734 14.476726l-18.073429 5.574889a112.756614 112.756614 0 0 0-8.991755-19.691945h-2.068104a141.710067 141.710067 0 0 1-15.645655 22.749142A130.290537 130.290537 0 0 0 649.564418 638.41608 112.576779 112.576779 0 0 0 674.381663 595.615324l18.702852 4.76563c-1.168928 3.237032-2.517692 6.653899-4.04629 9.980849h35.967022v11.959034a75.620664 75.620664 0 0 0 16.634747-26.975266l18.792769 4.675713a77.508932 77.508932 0 0 1-4.40596 10.070766h46.037788v15.82549h-24.727327a96.121866 96.121866 0 0 1 9.621178 15.645654l-19.062522 4.675713a96.931124 96.931124 0 0 0-11.239694-19.691944h-9.621178a67.528084 67.528084 0 0 1-8.991756 10.340518z m43.520097 55.658966a328.738581 328.738581 0 0 1-17.264171 38.035126h33.08966v17.983511H655.678812v-17.983511h91.086483a193.142908 193.142908 0 0 0 20.860873-43.160426z m-97.560547-3.506784a164.998714 164.998714 0 0 1 16.994417 28.773617l-16.994417 8.002663a176.867831 176.867831 0 0 0-15.915408-29.582876z m64.291051-21.580213A131.009878 131.009878 0 0 1 728.332196 651.27429a158.88432 158.88432 0 0 1-24.907163 17.444006z m-22.839059 19.242356a175.339232 175.339232 0 0 1 14.656562 30.751804l-16.814583 6.743817a213.194523 213.194523 0 0 0-13.487633-31.65098z" fill="#F9CC67" p-id="8637"></path></svg>`,
                type: "popup",
                title: "AI命理师，算算您的未来",
                submitText: "开始测算",
                prompt: `
                    <<|>>
                    你的任务是为用户进行运势预测。请根据用户提供的星座，为其预测运势。
                    请输出运势预测结果，内容应丰富、全面，为每个方面的运势提供具体的描述和建议。
                    [在此输出运势预测结果]
                `,
                fields: [
                    {
                        label: "算命方向",
                        type: "radio",
                        required: true,
                        options: [
                            "运势",
                            "财运",
                            "事业运",
                            "桃花运",
                            "健康",
                            "官运",
                            "姻缘",
                            "学业",
                            "子女",
                            "人际关系",
                        ]
                    },
                    {
                        label: "出生日期",
                        required: true,
                        type: "input",
                        placeholder: "例如：农历1990年1月1日5点半",
                    },
                    {
                        label: "姓名",
                        required: true,
                        type: "input",
                        placeholder: "例如：张三",
                    },
                    {
                        label: "出生地",
                        type: "input",
                        placeholder: "例如：北京",
                    },
                    {
                        label: "性别",
                        type: "radio",
                        options: [
                            "男",
                            "女",
                        ]
                    },
                    {
                        label: "其他信息",
                        type: "textarea",
                        placeholder: "例如：学历：本科，职业：程序员",
                    }
                ]
            },
            {
                label: "紫微斗数",
                description: "紫微斗数✨古星命术，天干星宿推演人生轨迹。",
                appHide: true,
                icon: `<svg t="1741140929174" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8996" width="16" height="16"><path d="M645.632 458.72s0 0.096 0 0c-0.288 0.864-0.48 0.768 0 0zM651.296 455.456z" fill="#9a4dee" p-id="8997"></path><path d="M526.88 196.544c-177.024 0-321.12 144-321.12 321.024 0 177.024 144 321.024 321.12 321.024 177.024 0 321.024-144 321.024-321.024 0-177.024-144-321.024-321.024-321.024z m234.048 444.288c-5.76 2.016-11.52 3.072-17.28 3.744 2.304 5.376 4.128 11.04 5.184 16.992 5.472 31.488-7.872 64.704-22.56 82.176a88.0512 88.0512 0 0 1-68.16 31.872c-20.064 0-40.608-6.624-58.464-20.256-33.312-25.344-35.52-65.568-23.52-92.256 11.616-25.824 35.424-39.264 60.96-34.176 25.536 4.992 36.48 29.568 33.792 50.112-2.592 19.968-17.184 32.928-34.944 31.968-8.16-0.576-14.4-7.584-13.824-15.744 0.576-8.16 7.776-14.496 15.744-13.824 2.976 0.192 3.552-5.952 3.648-6.144 0.48-4.128-0.288-15.168-10.08-17.088-17.664-3.552-25.92 12.384-28.128 17.28-7.296 16.32-5.952 40.896 14.4 56.448 31.68 24.192 67.872 14.304 85.824-7.104 10.464-12.48 19.68-37.056 16.032-57.984-2.304-13.44-9.504-23.52-21.888-30.816l-170.88-99.648-170.88 99.648c-12.384 7.2-19.584 17.28-21.888 30.816-3.648 20.928 5.472 45.504 15.936 57.984 17.952 21.408 54.24 31.2 85.92 7.104 20.352-15.552 21.792-40.128 14.4-56.448-2.208-4.896-10.656-20.832-28.128-17.28-9.888 1.92-10.656 12.96-10.08 17.088 0 0.288 0.576 6.144 3.648 6.144 8.16-0.672 15.264 5.664 15.744 13.824 0.48 8.16-5.664 15.264-13.824 15.744-17.472 1.056-32.352-12-34.944-31.968-2.688-20.544 8.256-45.024 33.792-50.112 25.44-4.992 49.44 8.448 60.96 34.176 12 26.688 9.696 66.912-23.52 92.256-17.856 13.632-38.4 20.256-58.464 20.256-25.632 0-50.496-10.848-68.16-31.872-14.688-17.472-28.032-50.688-22.464-82.176 1.056-6.048 2.88-11.712 5.184-16.992-5.76-0.576-11.52-1.632-17.28-3.744-30.144-10.752-52.512-38.688-60.48-60-17.184-45.984 5.184-95.808 52.128-115.968 38.496-16.512 74.592 1.344 91.872 24.864 16.704 22.752 16.608 50.208-0.192 69.888-16.992 19.776-43.68 17.184-60.192 4.8-16.128-12.096-20.544-31.104-10.656-46.176 4.512-6.816 13.728-8.832 20.544-4.32 6.912 4.512 8.832 13.632 4.32 20.544-1.536 2.304 3.36 6.048 3.552 6.24 3.264 2.496 13.344 7.296 19.872-0.384 11.52-13.44 2.016-28.704-1.152-33.024-10.56-14.4-32.736-25.248-56.256-15.168-36.576 15.744-45.888 52.032-36.096 78.24 5.76 15.264 22.656 35.328 42.624 42.528 12.864 4.608 25.152 3.264 37.536-3.936l171.36-99.936v-199.68c0-14.304-5.088-25.632-15.552-34.368-16.224-13.728-42.048-18.144-58.176-15.456-27.552 4.704-54.24 31.104-49.344 70.56 3.168 25.44 23.616 39.072 41.472 40.896 5.28 0.48 23.328 1.056 29.088-15.648 3.264-9.504-5.856-15.744-9.696-17.376-0.288-0.096-5.952-2.4-7.2 0-3.648 7.392-12.576 10.272-19.968 6.624a14.928 14.928 0 0 1-6.624-19.968c8.064-16.128 26.88-21.888 45.216-14.112 19.104 8.064 34.752 29.856 26.208 54.432-8.544 24.48-32.064 38.496-60.288 35.424-29.088-3.072-62.688-25.344-67.776-66.816-6.336-50.688 25.44-95.232 73.824-103.488 22.56-3.84 57.888 1.344 82.368 21.984 4.608 3.936 8.544 8.256 12 12.96 3.456-4.704 7.392-9.024 12-12.96 24.48-20.64 59.808-25.824 82.368-21.984 48.384 8.256 80.16 52.8 73.824 103.488-5.088 41.472-38.688 63.744-67.776 66.816-27.936 2.976-51.84-10.944-60.288-35.424-8.544-24.576 7.2-46.464 26.208-54.432 18.432-7.776 37.152-2.016 45.216 14.112 3.648 7.296 0.672 16.224-6.624 19.968-7.296 3.648-16.224 0.672-19.968-6.624-1.248-2.496-6.912-0.096-7.2 0-3.84 1.632-12.96 7.872-9.696 17.376 5.76 16.704 23.712 16.128 29.088 15.648 17.76-1.92 38.304-15.456 41.472-40.896 4.896-39.552-21.792-65.856-49.44-70.56-16.032-2.688-41.952 1.728-58.176 15.456-10.464 8.832-15.552 20.064-15.552 34.368v200.16l170.88 99.456c12.48 7.296 24.672 8.544 37.536 3.936 20.064-7.104 36.96-27.264 42.624-42.528 9.888-26.208 0.576-62.592-36-78.24-23.52-10.08-45.6 0.768-56.256 15.168-3.168 4.32-12.672 19.584-1.152 33.024 6.432 7.584 16.512 2.88 19.872 0.384 0.192-0.192 5.088-3.84 3.552-6.144-4.512-6.912-2.592-16.032 4.32-20.544 6.72-4.512 16.032-2.592 20.544 4.32 9.888 15.072 5.568 34.08-10.656 46.176-16.512 12.48-43.296 14.976-60.192-4.8-16.896-19.68-16.992-47.136-0.192-69.888 17.28-23.52 53.376-41.376 91.872-24.864 46.944 20.16 69.408 69.984 52.128 115.968-7.968 21.312-30.336 49.152-60.48 59.904z" fill="#9a4dee" p-id="8998"></path><path d="M526.88 89.6C290.432 89.6 98.816 281.216 98.816 517.568c0 236.352 191.616 427.968 427.968 427.968S954.752 753.92 954.752 517.568C954.848 281.216 763.232 89.6 526.88 89.6z m0 784.704c-196.704 0-356.736-160.032-356.736-356.736 0-196.704 160.032-356.736 356.736-356.736 196.704 0 356.64 160.032 356.64 356.736 0 196.704-160.032 356.736-356.64 356.736z" fill="#9a4dee" p-id="8999"></path></svg>`,
                type: "popup",
                title: "紫微斗数",
                submitText: "提交",
                prompt: `
                    <<|>>
                    你是一位专业的紫微斗数分析师，你的任务是根据提供的出生信息进行紫微斗数分析。
                    请仔细阅读出生信息, 在进行紫微斗数分析时，请按照以下步骤进行：
                    1. 根据出生信息确定命盘，包含十二宫位、星曜分布等。
                    2. 分析命宫的主星和格局，解读命主的基本性格特质、人生趋向。
                    3. 对事业宫、财帛宫、夫妻宫、子女宫等重要宫位进行详细分析，阐述各方面的运势和发展情况。
                    4. 综合各宫位的情况，给出命主整体运势的总结和建议。
                    请写下你的分析内容，要求分析全面、详细、有条理。
                    [在此进行紫微斗数分析]
                `,
                fields: [
                    {
                        label: "命盘排盘",
                        type: "radio",
                        required: true,
                        options: [
                            "子女运势",
                            "健康运势",
                            "事业运势",
                            "财运运程",
                            "姻缘运势",
                        ]
                    },
                    {
                        label: "出生日期(误差不超过15分钟)",
                        required: true,
                        type: "input",
                        placeholder: "例如：农历1990年1月1日5点半",
                    },
                    {
                        label: "出生地",
                        required: true,
                        type: "input",
                        placeholder: "例如：北京市朝阳区",
                    },
                    {
                        label: "性别",
                        required: true,
                        type: "radio",
                        options: [
                            "男",
                            "女",
                        ]
                    },
                    {
                        label: "父母生辰(误差不超过15分钟)",
                        type: "input",
                        placeholder: "例如：农历1990年1月1日5点半",
                    },
                    {
                        label: "重大事件",
                        type: "textarea",
                        placeholder: "例如：升学、婚变、伤病等重大事件",
                    }
                ]
            }
        ]
    },
]

// 工具栏配置项
export const topTabs = [
    {
        label: "简历分析",
        key: "resume",
        icon: 'https://static.cyjiaomu.com/ai/icons/resume.jpg',
        type: "img",
        isNew: true,
        className: "w-full h-auto px-[10px]",
        path: "/resume",
        auth: (availableTasks: string[], channelId?: any) => availableTasks.includes("resumeAnalyzer") && !['gy-ios', 'gy-android'].includes(channelId),
    },
    {
        label: "管用企业版",
        key: "管用企业版",
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building2-icon lucide-building-2"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/><path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/><path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/><path d="M10 6h4"/><path d="M10 10h4"/><path d="M10 14h4"/><path d="M10 18h4"/></svg>',
        activeIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building2-icon lucide-building-2"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/><path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/><path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/><path d="M10 6h4"/><path d="M10 10h4"/><path d="M10 14h4"/><path d="M10 18h4"/></svg>`,
        newIcon: `<svg t="1744940931899" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19767" width="28" height="28"><path d="M245.76 286.72h552.96c124.928 0 225.28 100.352 225.28 225.28s-100.352 225.28-225.28 225.28H0V532.48c0-135.168 110.592-245.76 245.76-245.76z m133.12 348.16V401.408H348.16v178.176l-112.64-178.176H204.8V634.88h30.72v-178.176L348.16 634.88h30.72z m182.272-108.544v-24.576h-96.256v-75.776h110.592v-24.576h-141.312V634.88h143.36v-24.576h-112.64v-83.968h96.256z m100.352 28.672l-34.816-151.552h-34.816l55.296 233.472H675.84l47.104-161.792 4.096-20.48 4.096 20.48 47.104 161.792h28.672l57.344-233.472h-34.816l-32.768 151.552-4.096 30.72-6.144-30.72-40.96-151.552h-30.72l-40.96 151.552-6.144 30.72-6.144-30.72z" fill="#F74643" p-id="19768"></path></svg>`,
        isNew: true,
        link: "https://jmso.cyjiaomu.com/gyb-h5",
        // auth: (availableTasks: string[], channelId?: any) => ['gy-ios', 'gy-android'].includes(channelId),
        auth: (availableTasks: string[], channelId?: any) => false,
    },
    // {
    //     key: 'career-coach',
    //     label: '职场技能陪练',
    //     icon: 'https://static.cyjiaomu.com/ai/resume.png',
    //     activeIcon: `https://static.cyjiaomu.com/ai/resume.png`,
    //     path: '/career-coach',
    //     isNew: true,
    //     newIcon: 'https://static.cyjiaomu.com/ai/new.png',
    //     auth: () => (availableTasks: string[]) => availableTasks.includes("careerCoach"),
    // }
]