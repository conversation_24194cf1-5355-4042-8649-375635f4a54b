import { get } from '@/lib/request';
import { getLocalItem } from '@/lib/utils';
import { createContext, useContext, useEffect, useState } from 'react';
import { useDeviceContext } from './DeviceContext';
import { NotEnoughPoints } from '../chat/Battery';
import { toast } from 'sonner';
import { useSearchParams } from 'next/navigation';

interface TaskPointsProps {
  managementMaster: number;
  resumeAnalyzer: number;
  webSearch: number;
}

interface BatteryProps {
  available: Boolean;
  isEnough: Boolean;
  remainingPoints: number;
  taskPoints: TaskPointsProps;
}

interface ChatContextBatteryProps {
  battery: BatteryProps;
  getBattery: Function;
  checkEnoughBattery: Function;
  setBattery: Function;
}

interface ChatContextType {
  sendMessage: Function;
  chatBattery?: ChatContextBatteryProps;
  isApp?: Boolean;
  isMobile?: Boolean;
  channelId?: String;
  chatId?: String;
  focusMode?: String;
}

const initBattery: BatteryProps = {
  available: false,
  isEnough: false,
  remainingPoints: 0,
  taskPoints: {
    managementMaster: 0,
    resumeAnalyzer: 0,
    webSearch: 0,
  }
}

export const useBattery = () => {
  let userId = getLocalItem('__user_detail__', true)?.data?.detail?.user_id;
  const query = useSearchParams() as any;
  const channelId = query?.get('td_channelid');
  const [battery, setBattery] = useState<BatteryProps>(initBattery);
  const getBattery: Function = async ({ needCheckEnough = false, focusMode }: any = {}) => {
    try {
      const res = await get({
        url: `${process.env.NEXT_PUBLIC_API_URL}/billing/checkUserPoints?userId=${userId}&channelId=${channelId}&taskType=${focusMode}`,
      })
      if (res.code === 0) {
        setBattery(res.data);
      }
      if (needCheckEnough) {
        if (!res?.data?.isEnough) {
          toast.custom((t) => (<NotEnoughPoints t={t} />), { duration: 10000 });
        }
        return !!(res?.data?.isEnough);
      }
    } catch (error) {
      console.warn("🚀 ~ getBattery ~ error:", error)
    }
    if (needCheckEnough) {
      return false;
    }
  }

  const checkEnoughBattery = async (focusMode: string) => {
    if (!battery?.available) {
      return true;
    }
    return await getBattery({ needCheckEnough: true, focusMode })
  }

  useEffect(() => {
    getBattery();
    return () => {
      setBattery(initBattery);
    }
  }, [userId, channelId]);
  return {
    battery,
    checkEnoughBattery,
    getBattery,
    setBattery,
  }
}

export const ChatContext = createContext<ChatContextType>({
  sendMessage: () => { },
});

export const useChatContext = () => {
  return useContext(ChatContext);
};