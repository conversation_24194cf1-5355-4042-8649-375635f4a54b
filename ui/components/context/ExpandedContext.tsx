'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getLocalItem, getQuery, setLocalItem } from '@/lib/utils';

type ExpandedContextType = {
    isExpanded: boolean;
    setIsExpanded: (value: boolean) => void;
};

export const ExpandedContext = createContext<ExpandedContextType>({
    isExpanded: true,
    setIsExpanded: () => { },
});

export function ExpandedProvider({ children }: { children: React.ReactNode }) {
    const [isExpanded, setIsExpanded] = useState(false);

    useEffect(() => {
        const share = getQuery()?.get?.('share') === 'true';
        if (share) {
            return setIsExpanded(false);
        }
        setIsExpanded(getLocalItem('__expanded__') === 'true');
    }, []);

    useEffect(() => {
        // 检查当前窗口宽度
        const checkWindowSize = () => {
            // Tailwind lg断点通常是1024px
            if (window.innerWidth < 1024) {
                setIsExpanded(false);
            }
        };

        // 初始检查
        checkWindowSize();

        // 添加resize事件监听
        window.addEventListener('resize', checkWindowSize);

        // 清理函数
        return () => {
            window.removeEventListener('resize', checkWindowSize);
        };
    }, []);

    const handleSetExpanded = (value: boolean) => {
        setIsExpanded(value);
        setLocalItem('__expanded__', `${value}`);
    };

    return (
        <ExpandedContext.Provider value={{ isExpanded, setIsExpanded: handleSetExpanded }}>
            {children}
        </ExpandedContext.Provider>
    );
}

export function useExpanded() {
    return useContext(ExpandedContext);
}