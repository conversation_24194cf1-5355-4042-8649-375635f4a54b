'use client';

import { useSearchParams } from 'next/navigation';
import React, {
    createContext,
    useContext,
    useState,
    Dispatch,
    SetStateAction,
    useEffect,
} from 'react';
import { useExpanded } from './ExpandedContext';
import { useBattery } from './ChatContext';

export interface Chat {
    id: string;
    title: string;
    createdAt: string;
    focusMode: string;
    task?: {
        id: string;
        status: string;
        agentType: string;
    } | null;
}

interface SelectedChatContextType {
    loading: boolean;
    loadingMessage: boolean;
    selectedChatId: string | null;
    chatBattery: any;
    chats: Chat[];
    hasMore: boolean;
    currentPage: number;
    totalPages: number;
    fetchChats: Function;
    setLoadingMessage: Function;
    loadNextPage: any;
    refreshChats: any;
    setSelectedChatId: Dispatch<SetStateAction<string | null>>;
    setChats: Dispatch<SetStateAction<Chat[]>>;
    setCurrentPage: Dispatch<SetStateAction<number>>;
    setTotalPages: Dispatch<SetStateAction<number>>;
}

const SelectedChatContext = createContext<SelectedChatContextType>({
    loading: false,
    loadingMessage: false,
    selectedChatId: null,
    chatBattery: null,
    currentPage: 1,
    totalPages: 1,
    hasMore: false,
    chats: [],
    refreshChats: () => { },
    loadNextPage: () => { },
    setLoadingMessage: () => { },
    setSelectedChatId: () => { },
    setChats: () => { },
    fetchChats: () => { },
    setCurrentPage: () => { },
    setTotalPages: () => { },
});

export const useChats = (needSource = false) => {
    const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
    const [chats, setChats] = useState<Chat[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [loadingMessage, setLoadingMessage] = useState(false);
    const { isExpanded } = useExpanded();

    const query = useSearchParams() as URLSearchParams;

    const source = query.get('source');

    const fetchChats = async () => {
        try {
            if (loading || !hasMore) {
                console.log("🚀 ~ fetchChats ~ loading:", loading)
                return;
            }
            setLoading(true);
            let requestUrl = `${process.env.NEXT_PUBLIC_API_URL}/chats?page=${currentPage}&limit=10`;
            console.log("🚀 ~ fetchChats ~ currentPage:", currentPage)
            if (needSource && source === 'resumeAnalyzer') {
                requestUrl += '&source=resumeAnalyzer';
            }
            const res = await fetch(requestUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Device-ID': localStorage.getItem('deviceId')!,
                },
            });
            const data = await res.json();
            if (currentPage > 1) {
                setChats([chats, data.chats].flat() || []);
            } else {
                setChats(data.chats || []);
            }
            setTotal(data?.pagination?.total || 0);
            const totalPages = Math.ceil(data?.pagination?.total / 10) || 1;
            setTotalPages(totalPages);
            const has_more = totalPages > currentPage;
            console.log("🚀 ~ fetchChats ~ has_more:", has_more)
            setHasMore(has_more);
        } catch (error) {
            console.error('获取历史记录失败', error);
        } finally {
            setLoading(false);
        }
    };

    const reset = () => {
        setLoading(false);
        setHasMore(true);
        setChats([]);
        setCurrentPage(1);
        setTotalPages(1);
        fetchChats();
    };

    /**
     * 加载下一页聊天内容
     */
    const loadNextPage = async () => {
        if (!hasMore || loading) { return; }
        setCurrentPage(cur => cur + 1);
        await fetchChats();
    };

    /**
     * 刷新聊天内容
     */
    const refreshChats = async () => {
        console.log("🚀 ~ refreshChats ~ refreshChats:")
        reset();
        setTimeout(fetchChats, 1000);
    };

    useEffect(() => {
        fetchChats();
    }, []);
    return {
        loading,
        selectedChatId,
        chats,
        currentPage,
        total,
        totalPages,
        loadingMessage,
        hasMore,
        refreshChats,
        loadNextPage,
        setLoadingMessage,
        fetchChats,
        setCurrentPage,
        setSelectedChatId,
        setChats,
        setTotalPages
    }
};

export function SelectedChatProvider({
    children,
}: {
    children: React.ReactNode;
}) {
    const chatBattery = useBattery(); // 灵豆信息
    const {
        loading,
        selectedChatId,
        chats,
        currentPage,
        hasMore,
        totalPages,
        loadingMessage,
        refreshChats,
        loadNextPage,
        setLoadingMessage,
        fetchChats,
        setCurrentPage,
        setSelectedChatId,
        setChats,
        setTotalPages
    } = useChats(false);

    return (
        <SelectedChatContext.Provider value={{
            chatBattery,
            loading,
            selectedChatId,
            chats,
            currentPage,
            hasMore,
            totalPages,
            loadingMessage,
            refreshChats,
            loadNextPage,
            setLoadingMessage,
            fetchChats,
            setCurrentPage,
            setSelectedChatId,
            setChats,
            setTotalPages
        }}>
            {children}
        </SelectedChatContext.Provider>
    );
}

export const useSelectedChat = () => {
    return useContext(SelectedChatContext);
};