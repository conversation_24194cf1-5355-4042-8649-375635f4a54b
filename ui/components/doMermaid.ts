import { clkLog, getQuery, win } from '@/lib/utils';
import { marked } from 'marked';
import { markedHighlight } from 'marked-highlight';
import hljs from 'highlight.js';

let isMermaidInitialized = false;

const channelId: any = getQuery().get('td_channelid');


function initializeMermaid(mermaid: any) {
    if (isMermaidInitialized) {
        return;
    }

    const isDarkMode = win?.matchMedia?.('(prefers-color-scheme: dark)')?.matches;
    const config: any = {
        startOnLoad: true,
        theme: isDarkMode ? 'dark' : 'default',
        securityLevel: "loose",
        themeVariables: {
            fontFamily: 'system-ui, sans-serif'
        },
        flowchart: {
            curve: 'basis',
            padding: 20
        }
    };

    try {
        mermaid.initialize({
            ...config,
            parseError: (err: any) => {
                console.warn('Mermaid parse error:', err);
                return true; // 允许继续处理错误
            }
        } as any);
        isMermaidInitialized = true;
    } catch (error) {
        console.error('Mermaid initialization error:', error);
    }
}

win._jm_doMermaid__ = {
    render: (id: string, mode: 'preview' | 'source' = 'preview') => {
        try {
            const container = document.getElementById(id);
            if (!container) return;
            const text = container.getAttribute('data-source');
            if (!text) return;
            clkLog('查看Mermaid图表', { "源码": text, mode });
            const previewElement: any = container.querySelector('.mermaid-preview');
            const sourceElement: any = container?.querySelector('.mermaid-source');

            if (mode === 'source') {
                if (previewElement) previewElement.style.display = 'none';
                if (sourceElement) {
                    sourceElement.style.display = 'grid';
                    sourceElement.textContent = text;
                }
                return;
            }

            if (previewElement) previewElement.style.display = 'grid';
            if (sourceElement) sourceElement.style.display = 'none';

            import('mermaid').then(async ({ default: mermaid }: any) => {
                if (!mermaid) return;
                try {
                    await mermaid.parse(text);
                    initializeMermaid(mermaid);
                    const requestIdleCallback = win.requestIdleCallback || ((cb: any) => setTimeout(cb, 1));
                    requestIdleCallback(() => {
                        mermaid.contentLoaded(id);
                    });
                } catch (error) {
                    console.error('Mermaid rendering error:', error);
                }
            }).catch((error: any) => {
                console.error('Mermaid initialization error:', error);
            });
        } catch (error) {
            console.error("🚀 ~ error:", error)
        }
    },
    scale: (id: string, action: 'zoomIn' | 'zoomOut') => {
        try {
            const mermaidContainer: any = document.querySelector(`#${id} .mermaid`);
            const mermaidElement: any = mermaidContainer?.querySelector('svg');
            if (!mermaidElement || !mermaidContainer) return;

            // 获取当前缩放比例
            let scale = parseFloat(mermaidElement.style?.transform?.split('scale(')[1]?.split(')')[0]);
            scale = isNaN(scale) ? 1 : scale;
            const newScale = action === 'zoomIn' ? scale + 0.1 : scale - 0.1;

            // 设置SVG容器样式
            mermaidElement.style.transform = `scale(${newScale})`;
            mermaidElement.style.transformOrigin = 'top left';
            mermaidElement.style.height = 'auto';
            mermaidElement.style.width = 'auto';
            mermaidElement.style.minHeight = 'min-content';

            const padding = Math.max(20 * newScale, 20);
            mermaidContainer.style.paddingRight = `${padding}px`;
            mermaidContainer.style.paddingBottom = `${padding}px`;

            // 确保容器可以容纳缩放后的内容
            const svgElement = mermaidElement.querySelector('svg');
            if (svgElement) {
                svgElement.style.height = 'auto';
                svgElement.style.width = '100%';
                svgElement.style.maxWidth = 'none';
            }
        } catch (error) {
            console.error('Mermaid scale error:', error);
        }
    },
    handleReset: (id: string) => {
        try {
            const container = document.getElementById(id);
            if (!container) return;
            const previewElement: any = container.querySelector('.mermaid-preview');
            if (!previewElement) return;
            const mermaidContainer: any = document.querySelector(`#${id} .mermaid`);
            if (!mermaidContainer) return;
            mermaidContainer.style.paddingRight = '';
            mermaidContainer.style.paddingBottom = '';
            const mermaidElement: any = previewElement.querySelector('.mermaid svg');
            if (!mermaidElement) return;
            mermaidElement.style.transform = '';
        } catch (error) {
            console.error('Mermaid reset error:', error);
        }
    }
}

marked.use(
    markedHighlight({
        langPrefix: 'hljs language-',
        highlight(code: any, lang: any) {
            const language = hljs.getLanguage(lang) ? lang : 'plaintext';
            return hljs.highlight(code, { language }).value;
        }
    }),
    {
        gfm: true,
        breaks: true,
        mangle: true,
        headerIds: true,
        walkTokens: (token: any) => {
            // 抽离检测数学公式的通用函数
            const hasMathDelimiters = (text: any = '') => {
                return (
                    // Check for display math with $$ delimiters
                    (text.includes('$$') && text.split('$$').length > 1) ||
                    // Check for display math with \[ \] delimiters
                    (text.includes('\\[') && text.includes('\\]')) ||
                    // Check for inline math with single $ delimiters (must have at least 2)
                    (text.includes('$') && text.match(/\$/g)?.length >= 2)
                );
            };

            // 抽离处理数学公式的通用函数
            const processMathContent = (content: string) => {
                // 使用正则表达式提取公式内容
                const formulaPattern = /\$\$(.*?)\$\$|\$([^$\n]+?)\$/g;
                let lastIndex = 0;
                let result = '';

                let match;
                while ((match = formulaPattern.exec(content)) !== null) {
                    const [fullMatch, displayFormula, inlineFormula] = match;
                    const formula = displayFormula || inlineFormula;
                    const formulaStartIndex = match.index;

                    // 添加非公式部分 - 不使用 marked.parse 避免递归调用
                    result += content.slice(lastIndex, formulaStartIndex);

                    // 添加公式部分
                    result += `<div class="math-display">${fullMatch}</div>`;

                    lastIndex = formulaStartIndex + fullMatch.length;
                }

                // 添加剩余的非公式部分 - 不使用 marked.parse 避免递归调用
                result += content.slice(lastIndex);

                return result;
            };

            if (token.type === 'text') {
                // 检测是否包含数学公式标记
                if (hasMathDelimiters(token.text)) {
                    // 将 token 类型改为 'html'，这样 marked 会直接原样输出内容
                    token.type = 'html';
                    token.text = processMathContent(token.text);
                    return;
                }
            }

            if (token.type === 'paragraph') {
                if (token.tokens && token.tokens.length > 0) {
                    // 合并所有子token的raw文本
                    const combinedText = token.tokens.map((t: any) => t.raw || t.text).join('');

                    // 使用通用函数检测公式
                    if (hasMathDelimiters(combinedText)) {
                        // 提取公式内容并替换
                        const processedText = processMathContent(combinedText);

                        // 更新 token 内容
                        token.type = 'html';
                        token.text = processedText;

                        // 清空 tokens 数组，因为我们已经处理了整个段落
                        token.tokens = [];
                    }
                }
            }
        },
        renderer: {
            code(props: any) {
                const { lang, text } = props;
                if (lang === 'mermaid') {
                    const id = `id_${Math.random().toString(36).substring(2, 30)}`;
                    const showButton = !['mp', 'mp-kl', 'gy-ios', 'gy-android'].includes(channelId)
                    return `
                    <div class="flex flex-col w-full" id="${id}" data-source="${text}">
                        <div class="flex items-center justify-start space-x-1  mb-2 ${showButton ? '' : 'hidden'}">
                        <button
                            class="px-2 py-1 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors border"
                            onclick="_jm_doMermaid__.render('${id}', 'preview')"
                        >预览图像</button>
                        <button
                            class="px-2 py-1 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors border"
                            onclick="_jm_doMermaid__.render('${id}', 'source')"
                        >文本</button>
                        </div>
                        <div class="mermaid-wrapper">
                        <pre class="mermaid-preview mermaid-modal-content hidden relative">
                            <div class="absolute z-[1] top-2 right-2 flex gap-2">
                            <button
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onclick="_jm_doMermaid__.scale('${id}', 'zoomIn')"
                                title="放大"
                            >
                                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </button>
                            <button
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onclick="_jm_doMermaid__.scale('${id}', 'zoomOut')"
                                title="缩小"
                            >
                                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
                                </svg>
                            </button>
                            <button
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                onclick="_jm_doMermaid__.handleReset('${id}')"
                                title="还原"
                            >
                                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </button>
                            </div>
                            <div class="mermaid grid max-h-fit overflow-auto">${text}</div>
                        </pre>
                        <pre class="mermaid-source">${text}</pre>
                        </div>
                    </div>`;
                }
                return `<pre><code class="grid language-${lang}">${marked.parse(text)}</code></pre>`;
            }
        }
    } as any);