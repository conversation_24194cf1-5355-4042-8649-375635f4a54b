// 创建LaTeX符号到HTML实体或Unicode字符的映射表
export const latexToHtmlMap: { [key: string]: string } = {
    '\\alpha': 'α',
    '\\beta': 'β',
    '\\gamma': 'γ',
    '\\delta': 'δ',
    '\\epsilon': 'ε',
    '\\zeta': 'ζ',
    '\\eta': 'η',
    '\\theta': 'θ',
    '\\iota': 'ι',
    '\\kappa': 'κ',
    '\\lambda': 'λ',
    '\\mu': 'μ',
    '\\nu': 'ν',
    '\\xi': 'ξ',
    '\\omicron': 'ο',
    '\\pi': 'π',
    '\\rho': 'ρ',
    '\\sigma': 'σ',
    '\\tau': 'τ',
    '\\upsilon': 'υ',
    '\\phi': 'φ',
    '\\chi': 'χ',
    '\\psi': 'ψ',
    '\\omega': 'ω',
    '\\sum': '∑',
    '\\frac': '⁄', // 这里只是一个占位符，实际处理需要更复杂的逻辑
    '\\left': '(',  // 这里只是一个占位符，实际处理需要更复杂的逻辑
    '\\right': ')', // 这里只是一个占位符，实际处理需要更复杂的逻辑
    '\\max': 'max',
    '\\min': 'min',
    '\\int': '∫',
    '\\lim': 'lim',
    '\\infty': '∞',
    '\\partial': '∂'
};