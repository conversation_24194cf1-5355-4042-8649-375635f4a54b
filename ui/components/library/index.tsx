import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { clkLog, formatTimeDifference, getUserText, withQuery } from '@/lib/utils';
import { ClockIcon, History, Sparkles, ArrowLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useChats } from '@/components/context/SelectedChatContext';
import { focusMap } from "@/components/MessageInputActions/Focus";


export default function Library() {
  const query = useSearchParams() as URLSearchParams;
  const source = query.get('source');
  const {
    loading,
    chats,
    total,
    hasMore,
    loadNextPage,
    setSelectedChatId,
    setCurrentPage
  } = useChats(source === 'resumeAnalyzer');
  const router = useRouter();
  const [tabTitle, setTabTitle] = useState(source === 'resumeAnalyzer' ? '我的简历' : '历史对话');

  const gotoChatDetail = (chat: any) => {
    if (!chat.id) return;
    clkLog(`查看${tabTitle}`, chat.title);
    setSelectedChatId(chat.id);
    if (chat.task?.agentType === 'resumeAnalyzer' && chat.task.status !== 'completed') {
      router.push(withQuery(`/rp/${chat.id}`, { taskId: chat.task.id, source: "resumeAnalyzer" }));
    } else {
      router.push(withQuery(`/c/${chat.id}`, {}, ['source', 'taskId']));
    }
  };

  const creatNewChat = () => {
    setSelectedChatId('');
    router.push(withQuery('/', { refresh: `${Math.random()}` }, ['source', 'taskId']));
  };

  useEffect(() => {
    setTabTitle(source === 'resumeAnalyzer' ? '我的简历' : '历史对话');
    setCurrentPage(1);
  }, [source]);

  // 添加触底检测逻辑
  useEffect(() => {
    const handleScroll = async () => {
      const scrollContainer = document.getElementById('scrollableDiv');
      if (scrollContainer) {
        const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
        console.log("🚀 ~ handleScroll ~ scrollTop:", scrollTop)
        const isNearBottom = scrollHeight - (scrollTop + clientHeight) < 100;

        if (isNearBottom && hasMore && !loading) {
          const currentScrollPosition = scrollTop;
          await loadNextPage();
          // 保持滚动位置
          setTimeout(() => {
            (document.getElementById('scrollableDiv') as any).scrollTop = currentScrollPosition;
          }, 0);
        }
      }
    };

    const scrollContainer = document.getElementById('scrollableDiv');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasMore, loading]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-slate-50 relative">
      {loading && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-gray-600">加载中...</span>
          </div>
        </div>
      )}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-gray-800" onClick={creatNewChat}>
                <img src="https://static.cyjiaomu.com/mp-mskl/<EMAIL>" className="w-auto h-5 mr-1" />
              </Button>
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500">
                  <History className="w-4 h-4 text-white" />
                </div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                  {tabTitle}
                </h1>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              共 {total} 条对话
            </div>
          </div>
        </div>
      </header>

      <main id="scrollableDiv" className="max-w-4xl mx-auto px-4 py-6 overflow-y-auto h-[calc(100vh-64px)]">
        <div className="space-y-3">
          <AnimatePresence>
            {chats.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 flex items-center justify-center">
                  <Sparkles className="w-8 h-8 text-blue-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-600 mb-2">
                  暂无{tabTitle}
                </h3>
                <p className="text-gray-400 text-sm">
                  开始与AI对话，历史记录会显示在这里
                </p>
              </motion.div>
            ) : (
              chats.map((chat, index) => (
                <motion.div
                  key={chat.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  className="group bg-white/80 backdrop-blur-sm rounded-xl border border-gray-100 hover:border-blue-200 hover:shadow-md transition-all duration-300 overflow-hidden relative"
                  onClick={() => gotoChatDetail(chat)}
                >
                  <div className="block p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center flex-shrink-0">
                            <Sparkles className="w-3 h-3 text-white" />
                          </div>
                          <h3 className="font-medium text-gray-800 truncate group-hover:text-blue-600 transition-colors">
                            {getUserText(chat.title)}
                          </h3>
                          {focusMap[chat.focusMode] && (
                            <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full flex-shrink-0">
                              {focusMap[chat.focusMode]?.title}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <ClockIcon size={15} />
                          <span>{formatTimeDifference(new Date(), chat.createdAt)} 前</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            )}
          </AnimatePresence>
        </div>
      </main>
    </div>
  );
}
