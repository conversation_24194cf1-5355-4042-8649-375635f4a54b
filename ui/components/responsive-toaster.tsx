"use client";

import { Toaster } from "sonner";
import { useMediaQuery } from "@/hooks/use-media-query";

export function ResponsiveToaster() {
  const isDesktop = useMediaQuery("(min-width: 640px)");

  return (
    <Toaster
      richColors
      closeButton
      visibleToasts={1}
      position={isDesktop ? "bottom-right" : "top-center"}
      toastOptions={{
        classNames: {
          toast:
            "dark:bg-blue-900/80 bg-blue-100/80 dark:text-white text-black border border-blue-300/50 dark:border-blue-700/50 shadow-xl rounded-xl backdrop-blur-lg",
          title: "dark:text-white text-black",
          description: "dark:text-white text-black",
          actionButton:
            "dark:bg-blue-800 bg-blue-200 dark:text-white text-black",
          cancelButton:
            "dark:bg-blue-800 bg-blue-200 dark:text-white text-black",
          closeButton:
            "dark:bg-blue-800 bg-blue-200 dark:text-white text-black",
        },
      }}
    />
  );
}