/* Photo组件样式 */

/* 切换动画 */
.photo-body {
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* 移动端适配 */
@media (max-width: 640px) {
  .file-name {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

/* 切换开关动画 */
.switch-transition {
  transition: transform 0.3s ease, background-color 0.3s ease;
}

/* 内容切换动画 */
.content-transition {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 文件上传区域悬停效果 */
.upload-area-hover {
  transition: all 0.2s ease;
}

.upload-area-hover:hover {
  border-color: #3b82f6;
  background-color: #f0f7ff;
}

/* 呼吸灯动画 */
@keyframes pulse-border {
  0% {
    border-color: #ef4444; /* red-500 */
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); /* red-500 with opacity */
  }
  50% {
    border-color: #f87171; /* red-400 */
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); /* red-500 transparent */
  }
  100% {
    border-color: #ef4444; /* red-500 */
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); /* red-500 with opacity */
  }
}

.highlight-pulse {
  animation: pulse-border 0.8s ease-out; /* 缩短动画时间，移除 infinite */
}

/* 晃动动画 */
@keyframes shake-photo {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

.highlight-shake {
  animation: shake-photo 0.3s ease-in-out; /* 短暂的晃动 */
}