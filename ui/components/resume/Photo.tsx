import React, { useState } from 'react';
import { FileText, MessageSquare, Upload, Image, X, History, PenSquare } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import './Photo.css';
import { toast } from 'sonner';
import { jmWx } from '@/lib/wx';
import { useChatContext } from '../context/ChatContext';

const allowedExtensions = ['png', 'jpg', 'pdf', 'doc', 'docx', 'txt', 'jpeg'];

const Photo = ({ pid = "", title, setFile, setText, fileList = [], from = 'jianli', fileCtx, placeholder, highlight = false, className = '' }: any) => {
    const [mode, setMode] = useState<'text' | 'file'>('file');
    const [isDragging, setIsDragging] = useState(false);
    const { channelId } = useChatContext() as any;
    console.log("🚀 ~ Photo ~ channelId:", channelId)
    const showWechatFile = ['mp-kl'].includes(channelId);
    console.log("🚀 ~ Photo ~ showWechatFile:", showWechatFile)
    const handleModeChange = (newMode: 'text' | 'file') => {
        setMode(newMode);
        if (newMode === 'text') {
            setFile([]);
        } else {
            setText('');
        }
    };

    // 统一上传处理
    const handleUpload = (e: any) => {
        const files = e.target.files;
        console.log("🚀 ~ handleUpload ~ files:", files, Array.from(files))
        if (!files?.length) {
            return toast('取消选择');
        }

        const invalidFiles = Array.from(files).filter((file: any) => {
            const extension = file.name.split('.').pop()?.toLowerCase();
            return !extension || !allowedExtensions.includes(extension);
        });
        if (invalidFiles.length > 0) {
            return toast(`不支持的文件格式: ${invalidFiles.map((f: any) => f.name).join(', ')}`);
        }
        setFile(Array.from(files));
    };

    const handleDragEnter = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const selectWxFile = (e: any) => {
        e.preventDefault();
        e.stopPropagation();
        const clean = []
        if (!fileCtx.jianli) { clean.push('jianli') }
        if (!fileCtx.job) { clean.push('job') }
        jmWx.navigateTo({ url: `/pages/file/index?from=${from}&clean=${clean.join()}` });
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        const files = e.dataTransfer.files;
        if (!files?.length) return;

        const invalidFiles = Array.from(files).filter((file: File) => {
            const extension = file.name.split('.').pop()?.toLowerCase();
            return !extension || !allowedExtensions.includes(extension);
        });
        console.log("🚀 ~ invalidFiles ~ invalidFiles:", invalidFiles)
        if (invalidFiles.length > 0) {
            return toast(`不支持的文件格式`);
        }
        setFile(Array.from(files));
    };

    return (
        <div
            className={`w-full h-full bg-white rounded-xl border p-4 shadow-sm flex flex-col min-h-0 transition-all duration-300 ease-in-out ${highlight ? 'border-red-300 ring-1 ring-red-300 highlight-pulse highlight-shake' : 'border-gray-200'} ${className}`}
        >
            <div className="flex justify-between items-center mb-2">
                <h2 className="text-base font-semibold text-gray-800">{title}</h2>
                <div className="flex items-center bg-gray-100 rounded-full p-0.5">
                    <Button
                        onClick={() => handleModeChange('file')}
                        className={`rounded-full text-xs px-2.5 py-1 h-auto border-none transition-all ${mode === 'file' ? 'bg-white text-blue-600 shadow-sm' : 'bg-transparent text-gray-500'} ${highlight ? 'border border-red-300 ring-1 ring-red-300' : ''}`}
                    >
                        <Upload className="w-3 h-3 mr-1" />文件
                    </Button>
                    <Button
                        onClick={() => handleModeChange('text')}
                        className={`rounded-full text-xs px-2.5 py-1 h-auto border-none transition-all ${mode === 'text' ? 'bg-white text-blue-600 shadow-sm' : 'bg-transparent text-gray-500'} ${highlight ? 'border border-red-300 ring-1 ring-red-300' : ''}`}
                    >
                        <PenSquare className="w-3 h-3 mr-1" />文本
                    </Button>
                </div>
            </div>
            <div className="flex-1 min-h-0 overflow-hidden flex relative">
                <div className={`absolute inset-0 flex flex-col ${mode === 'text' ? 'visible' : 'invisible'}`}>
                    <textarea
                        placeholder={placeholder}
                        onInput={(e: any) => setText(e.target?.value)}
                        className="flex-1 h-full border px-3 py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full bg-slate-50 border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 resize-none text-sm"
                    />
                </div>
                <div
                    onDragEnter={handleDragEnter}
                    onDragOver={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    className={`w-full absolute inset-0 relative border-2 border-dashed rounded-lg text-center transition-colors flex flex-col justify-center px-3 ${isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-slate-50'} ${mode === 'file' ? 'visible' : 'invisible'}`}
                >
                    {fileList && fileList.length > 0 ? (
                        <div className="flex flex-col items-center justify-center gap-2 text-sm text-gray-700 h-full">
                            <FileText className="w-7 h-7 text-blue-500" />
                            <span className="font-medium break-all">{fileList.map((f: File) => f.name).join(', ')}</span>
                            <Button onClick={() => setFile([])} variant="ghost" size="sm" className="absolute top-2 right-2 text-gray-400 hover:text-gray-600">
                                <X className="w-4 h-4" />
                            </Button>
                        </div>
                    ) : (
                        <div className="flex flex-col justify-center items-center h-full gap-2 p-4">
                            <p className="text-xs text-gray-500 text-center leading-tight">
                                支持拖拽上传<br />{allowedExtensions.join(', ')}
                            </p>
                            <div className="flex justify-center items-center gap-3 w-full flex-wrap">
                                <Button variant="outline" size="sm" className="cursor-pointer flex-1 flex flex-col md:flex-row items-center gap-1 md:gap-2 bg-white text-xs md:text-sm py-1.5 md:py-4 px-2 md:px-4 h-auto" onClick={() => document.getElementById(`${pid}-image`)?.click()}>
                                    <Image className="w-3.5 h-3.5 text-blue-500" />
                                    <span className="text-[10px] md:text-xs">图片</span>
                                </Button>
                                <Button variant="outline" size="sm" className="cursor-pointer flex-1 flex flex-col md:flex-row items-center gap-1 md:gap-2 bg-white text-xs md:text-sm py-1.5 md:py-4 px-2 md:px-4 h-auto" onClick={() => document.getElementById(`${pid}-document`)?.click()}>
                                    <FileText className="w-3.5 h-3.5 text-green-500" />
                                    <span className="text-[10px] md:text-xs">文档</span>
                                </Button>
                                {showWechatFile && <Button variant="outline" size="sm" className="cursor-pointer flex-1 flex flex-col md:flex-row items-center gap-1 md:gap-2 bg-white text-xs md:text-sm py-1.5 md:py-4 px-2 md:px-4 h-auto" onClick={selectWxFile}>
                                    <History className="w-3.5 h-3.5 text-indigo-500" />
                                    <span className="text-[10px] md:text-xs">聊天记录</span>
                                </Button>}
                            </div>
                            <input type="file" id={`${pid}-image`} className="hidden" accept="image/*" onChange={handleUpload} />
                            <input type="file" id={`${pid}-document`} className="hidden" accept=".pdf,.doc,.docx,.txt" onChange={handleUpload} />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Photo;