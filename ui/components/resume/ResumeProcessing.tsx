import React, { useEffect, useRef, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { withQuery } from '@/lib/utils';
import ProgressBar from '../ProgressBar';
import { useDeviceContext } from '../context/DeviceContext';
import { useSelectedChat } from '../context/SelectedChatContext';
import sseClient from '@/lib/sseClient';
import TipsMessage from '../chat/TipsMessage';

interface Props {
    chatId: string;
}

function ResumeProcessing({ chatId }: Props) {
    const router = useRouter();
    const usp = useSearchParams() as any;
    const taskId = usp.get('taskId');
    const [step, setStep] = useState(1);
    const { channelId }: any = useDeviceContext();
    const { setCurrentPage, fetchChats } = useSelectedChat();
    const taskClose = useRef(null) as any;
    const connectionEstablished = useRef(false);
    const isFirstUnmount = useRef(true); // 添加一个ref来跟踪是否是第一次卸载
    const stages = ['开始', '内容解析', '简历分析', '简历优化', '完成'];

    const refreshChats = async () => {
        setCurrentPage(1);
        await fetchChats();
        console.log("🚀 ~ refreshChats ~ refreshChats:");
    };

    useEffect(() => {
        // 添加connectionEstablished检查，确保只连接一次
        if (!taskId || taskClose.current || connectionEstablished.current) { return; }

        console.log("🚀 ~ useEffect ~ 建立SSE连接");
        connectionEstablished.current = true;

        // 使用SSE SDK连接到任务
        taskClose.current = sseClient.connectWithStatusHandlers({
            eventSource: `/resume/task-status/${taskId}`,
            options: {
                onMessage(data: any) {
                    console.log('Received message:', data);
                    const status = data?.status;
                    if (status === 'completed') {
                        setStep(4); // 完成阶段
                        router.replace(withQuery(`/c/${chatId}`, { source: 'resumeAnalyzer', taskId }));
                        return
                    }
                    if (status === 'optimizing') {
                        return setStep(3); // 简历优化阶段
                    }
                    if (['analyzing', 'recognizing'].includes(status)) {
                        return setStep(2); // 简历分析阶段
                    }
                    if (status === 'failed') {
                        console.error('简历分析失败，请重试');
                    }
                },
                onOpen: () => {
                    // setStep(1); // 更新为"内容解析"阶段
                },
            }
        });


    }, [taskId, chatId, router]);

    useEffect(() => {
        console.log("🚀 ~ useEffect ~ refreshed:")
        if (['mp-kl', 'mp', 'gy-ios', 'gy-android'].includes(channelId)) {
            return
        }
        if (step === 2 || step === 3) { // 前一步為上傳完成，下一步為分析中，更新首頁的記錄
            refreshChats();
        };
    }, [step, channelId]);

    useEffect(() => {
        return () => {
            console.log("🚀 ~ return ~ 组件卸载，是否第一次:", isFirstUnmount.current);

            // 如果是第一次卸载，不执行清理逻辑，只更新标志
            if (isFirstUnmount.current) {
                isFirstUnmount.current = false;
                console.log("🚀 ~ 第一次卸载，跳过清理逻辑");
                return;
            }

            // 非第一次卸载，执行正常的清理逻辑
            console.log("🚀 ~ 非第一次卸载，执行清理逻辑:", taskClose.current);
            if (taskClose.current && typeof taskClose.current === 'function') {
                taskClose.current();
                taskClose.current = null;
            }
            // 重置连接状态，以便在组件重新挂载时可以重新连接
            connectionEstablished.current = false;
        };
    }, []);

    const tips = 'AI 重塑简历，轻松赢得面试邀约 💼';

    return (
        <div className="flex items-center justify-center min-h-screen">
            <ProgressBar stage={step} stages={stages} />

             {/* 提示 - 移到外部以确保固定定位正常工作 */}
             <TipsMessage tips={tips} />
        </div>
    );
}

export default ResumeProcessing;