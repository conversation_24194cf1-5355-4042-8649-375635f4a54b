import { Send } from 'lucide-react';
import { motion } from 'framer-motion';
import { useRef, useState } from 'react';
import { FileUploader } from '@/lib/fileUploader';
import { toast } from 'sonner';
import Photo from "./Photo";
import "./Photo.css"
import { useDeviceContext } from '../context/DeviceContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { withQuery } from '@/lib/utils';
import ProgressBar from '../ProgressBar';
import { useSelectedChat } from '../context/SelectedChatContext';
import { useEffect } from 'react';
import { ChatContext, useBattery, useChatContext } from '../context/ChatContext';
import { removeUrlParams } from '@/lib/ulr';
import { Button } from '@/components/ui/button';

const batteryImage = 'https://static.cyjiaomu.com/ai/bean.png';

const Page = () => {
    const router = useRouter();
    const [resumeFile, setResumeFile] = useState<File[]>([]);
    const [jobFile, setJobFile] = useState<File[]>([]);
    const [resumeText, setResumeText] = useState('');
    const [jobText, setJobText] = useState('');
    const [loading, setLoading] = useState(false);
    const [init, setInit] = useState(false);
    const [step, setStep] = useState(1);
    const [highlightResume, setHighlightResume] = useState(false);

    const handleSetResumeText = (value: string) => {
        setResumeText(value);
    };

    const handleSetJobText = (value: string) => {
        setJobText(value);
    };
    const { channelId }: any = useDeviceContext();
    const { setCurrentPage, fetchChats } = useSelectedChat();
    const chatBattery = useBattery() as any; // 灵豆信息
    const query = useSearchParams() as URLSearchParams;
    const { getBattery } = chatBattery;
    let taskClose: any = useRef(null);
    const focusMode = "resumeAnalyzer";
    const disabled = loading || !(resumeFile?.length > 0 || resumeText);
    const [jianliFileName, jianliFileId] = query.get('jianli')?.split('-_-') || [];
    const [jobFileName, jobFileId] = query.get('job')?.split('-_-') || [];

    const { battery } = chatBattery as any;
    const costBeans = battery.taskPoints[focusMode] || "限免分析";

    const fileCtx = {
        jianli: (resumeFile?.[0] as any)?.id,
        job: (jobFile?.[0] as any)?.id,
    }

    // 小程序渠道，直接跳转到小程序
    function cleanInput() {
        document.querySelectorAll('input[type="file"]').forEach((input: any) => {
            input.value = '';
        });
        document.querySelectorAll('textarea').forEach((input: any) => {
            input.value = '';
        });
        setResumeFile([]);
        setJobFile([]);
        setResumeText('');
        setJobText('');
    }

    const refreshPage = () => {
        removeUrlParams(['jianli', 'job'], router);
        cleanInput();
        setLoading(false);
        setStep(1); // 错误状态
    };

    const refreshChats = async () => {
        setCurrentPage(1);
        await fetchChats();
    };

    const onMessage = async (data: any) => {
        console.log("🚀 ~ SSE消息 ~ data:", data);
        // 在这里处理接收到的数据更新
        if (data.status === 'completed') { // 完成状态
            setStep(4)
            await getBattery();
            toast('处理完成');
            // 清除 input
            cleanInput();

            // 如果有chatId，跳转到会话页面
            if (data.chatId && data.taskId) {
                router.push(withQuery(`/c/${data.chatId}`, { source: 'resumeAnalyzer', taskId: data.taskId }));
            }
            return
        }

        console.log("🚀 ~ uploadFile ~ step:", step)

        if (data.status === 'error') {
            toast(data.message || '处理失败');
            refreshPage(); // 错误状态
            return
        }

        if (data.status === 'optimizing') {
            return setStep(3); // 简历优化阶段
        }
        if (data.status === 'analyzing') {
            setStep(2); // 分析中状态
        }
    };

    // 文件上传处理
    const uploadFile = async () => {
        try {
            let resumeFileId, jobFileId;
            const filesToUpload = [];

            // 处理简历文件
            if (resumeFile?.[0]) {
                if ((resumeFile[0] as any).id) {
                    resumeFileId = (resumeFile[0] as any).id;
                } else {
                    filesToUpload.push(resumeFile);
                }
            }

            // 处理岗位文件
            if (jobFile?.[0]) {
                if ((jobFile[0] as any).id) {
                    jobFileId = (jobFile[0] as any).id;
                } else {
                    filesToUpload.push(jobFile);
                }
            }

            // 上传新文件并获取文件ID
            if (filesToUpload.length > 0) {
                const fileIds = await FileUploader.uploadFiles(filesToUpload.flat());

                // 按顺序分配文件ID
                if (resumeFileId && jobFile?.length) { // 已有简历文件ID，获取岗位文件ID
                    jobFileId = fileIds[0];
                } else if (jobFileId && jobFile?.length) { // 已有岗位文件ID，获取简历文件ID
                    resumeFileId = fileIds[0];
                } else { // 都没有文件ID，获取第一个文件ID
                    // 按顺序分配文件ID
                    if (!resumeFileId && resumeFile?.length) {
                        resumeFileId = fileIds[0];
                    }
                    if (!jobFileId && jobFile?.length) {
                        jobFileId = fileIds[filesToUpload.length === 2 ? 1 : 0];
                    }
                }
            }

            setStep(1); // 开始获取内容
            // 发送SSE请求
            const res = await FileUploader.getFileContentSSE({
                resumeFileId,
                resumeFileName: resumeFile[0]?.name,
                jobFileId,
                jobFileName: jobFile[0]?.name,
                resumeText,
                jobText,
                channelId,
                deviceId: localStorage.getItem('deviceId'),
                onMessage
            });
            return res;
        } catch (error) {
            console.error('文件上传失败:', error);
            throw new Error('文件上传处理失败');
        }
    };

    const handleAnalyze = async () => {
        console.log("🚀 ~ handleAnalyze ~ loading:", loading)
        if (!(await chatBattery.checkEnoughBattery(focusMode))) {
            return
        }
        if (!(resumeFile?.length > 0 || resumeText) && !(jobFile?.length > 0 || jobText)) {
            toast('上传简历或输入岗位信息后，才能开始诊断哦~');
            // Force re-animation by toggling the state
            setHighlightResume(false); // Reset first
            setTimeout(() => {
                setHighlightResume(true);
                setTimeout(() => {
                    setHighlightResume(false);
                }, 1000);
            }, 50); // Small delay to ensure class removal before re-adding
            return;
        }

        if (disabled) return;

        try {
            setLoading(true);
            taskClose.current = await uploadFile();
        } catch (error) {
            console.error('分析失败:', error);
            toast('分析失败，请重试');
            setLoading(false);
        }
    };

    const setFile = (f: File[], callback: Function, type: string = '') => {
        console.log("🚀 ~ setFile ~ f:", f)
        callback(f);
        if (f?.length <= 0) { // 需要删除url里参数 type
            removeUrlParams([type], router);
        }
    }

    useEffect(() => {
        refreshPage();
        return () => {
            if (taskClose.current && typeof taskClose.current === 'function') {
                taskClose.current?.()
            }
        };
    }, []);

    useEffect(() => {
        if (['mp-kl', 'mp', 'gy-ios', 'gy-android'].includes(channelId)) {
            return
        }
        if (step === 2 || step === 3) { // 前一步為上傳完成，下一步為分析中，更新首頁的記錄
            refreshChats();
        };
        return () => {
            // setRefreshed(false);
        }
    }, [step, channelId]);

    useEffect(() => {
        if (jianliFileName && jianliFileId) {
            setResumeFile([{ name: jianliFileName, id: jianliFileId, uploaded: true } as any]);
        }
        if (jobFileName && jobFileId) {
            setJobFile([{ name: jobFileName, id: jobFileId, uploaded: true } as any]);
        }
    }, [jianliFileName, jianliFileId, jobFileName, jobFileId]);

    const stages = ['开始', '内容解析', '简历分析', '简历优化', '完成']


    if (loading) {
        return <ProgressBar stage={step} stages={stages} />
    }

    return (
        <ChatContext.Provider value={{ chatBattery, channelId, sendMessage: () => { }, focusMode }}>
            <div className="flex flex-col items-center justify-start min-h-[90vh] md:pt-[15vh] relative">
                {/* 主要内容区域 */}
                <main className="flex-1 flex flex-col w-full p-2 min-h-0">
                    <div className="w-full max-w-4xl mx-auto flex-1 flex flex-col min-h-0">
                        {/* 标题区域 - 紧凑化 */}
                        <div className="text-center mb-6">
                            <motion.h1
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="text-2xl md:text-3xl font-bold text-blue-600 mb-2"
                            >
                                简历分析和优化
                            </motion.h1>
                            <motion.p
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.1 }}
                                className="text-sm text-gray-500"
                            >
                                上传简历，一键获取岗位适配分析
                                <br />
                                还可补充岗位要求，获取更精准建议
                            </motion.p>
                        </div>

                        {/* 上传卡片区域 - 占据剩余空间 */}
                        <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4 min-h-0">
                            <Photo title="简历信息" pid="简历内容-input" uploadedFiles={resumeFile} setText={handleSetResumeText} setFile={(f: any) => setFile(f, setResumeFile, 'jianli')} fileList={resumeFile} from="jianli" fileCtx={fileCtx} placeholder="输入你的个人简历内容" highlight={highlightResume} className="h-full" />
                            <Photo title="岗位信息" pid="岗位要求-input" uploadedFiles={jobFile} setText={handleSetJobText} setFile={(f: any) => setFile(f, setJobFile, 'job')} fileList={jobFile} from="job" fileCtx={fileCtx} placeholder="输入目标岗位的岗位要求" className="h-full" />
                        </div>
                    </div>
                </main>

                {/* 吸底按钮区域 */}
                <div className="bg-white/80 backdrop-blur-sm border-t border-gray-100 p-4 w-full md:mt-6">
                    <div className="max-w-4xl mx-auto">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                            className="flex flex-col items-center gap-3"
                        >
                            <Button
                                onClick={handleAnalyze}
                                className={`w-full max-w-sm h-12 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-base font-bold shadow-lg flex items-center justify-center gap-2 ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-xl transition-shadow'}`}
                            >
                                {/* <Gem className="w-5 h-5 fill-white" /> */}
                                <img className="h-3.5 -mr-1" src={batteryImage} />
                                {costBeans}
                                <Send className="w-5 h-5 ml-3" />
                            </Button>
                            <p className="text-xs text-gray-400">
                                AI 重塑简历, 轻松赢得面试邀约 💼
                            </p>
                        </motion.div>
                    </div>
                </div>
            </div>
        </ChatContext.Provider>
    );
};

export default Page;
