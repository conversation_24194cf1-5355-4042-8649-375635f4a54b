import { useEffect, useState, useCallback, useRef } from 'react';

interface ScrollDetectionOptions {
    debug?: boolean;
    bottomThreshold?: number;
    debounceTime?: number;
    onScrollDirectionChange?: (direction: 'up' | 'down') => void;
    onNearBottom?: () => void;
}

/**
 * 自定义 Hook，用于检测滚动方向并判断用户是否滚动
 * @param elementSelector - 要监听滚动的元素选择器或window对象
 * @param options - 配置选项
 * @returns 用户是否已滚动的状态和重置滚动状态的函数
 */
const useScrollDetection = (
    elementSelector: string,
    options: ScrollDetectionOptions = {}
) => {
    const [userScrolled, setUserScrolled] = useState(false);
    const lastScrollTopRef = useRef<number>(0);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    const {
        debug = process.env.NODE_ENV === 'development',
        bottomThreshold = 100,
        debounceTime = 100,
        onScrollDirectionChange,
        onNearBottom
    } = options;

    // 重置用户滚动状态的函数
    const resetScrollState = useCallback(() => {
        setUserScrolled(false);
    }, []);

    // 防抖处理函数
    const debounce = useCallback((fn: Function) => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        timeoutRef.current = setTimeout(() => {
            fn();
            timeoutRef.current = null;
        }, debounceTime);
    }, [debounceTime]);

    useEffect(() => {
        // 获取元素，支持选择器字符串或直接传入window对象
        const el = (elementSelector === 'window' ? window : document.querySelector(elementSelector))

        if (!el) {
            if (debug) {
                console.warn(`元素 "${elementSelector}" 未找到`);
            }
            return;
        }

        // 初始化最后滚动位置
        lastScrollTopRef.current = 'scrollY' in el ? el.scrollY : (el as HTMLElement).scrollTop;

        const handleScroll = () => {
            debounce(() => {
                // 获取当前滚动位置，兼容window和DOM元素
                const currentScrollTop = 'scrollY' in el ? el.scrollY : (el as HTMLElement).scrollTop;
                const scrollDirection = currentScrollTop < lastScrollTopRef.current ? 'up' : 'down';

                // 仅在开发环境或启用调试时输出日志
                if (debug) {
                    console.log("滚动方向:", scrollDirection, "当前位置:", currentScrollTop);
                }

                // 调用方向变化回调
                if (onScrollDirectionChange) {
                    onScrollDirectionChange(scrollDirection);
                }

                // 任何方向的滚动都认为是用户手动滚动
                setUserScrolled(true);

                if (scrollDirection === 'down') {
                    // 计算是否接近底部
                    const isNearBottom = 'innerHeight' in el
                        ? (document.documentElement.scrollHeight - currentScrollTop - el.innerHeight < bottomThreshold)
                        : ((el as HTMLElement).scrollHeight - currentScrollTop - (el as HTMLElement).clientHeight < bottomThreshold);

                    if (isNearBottom) {
                        // 当用户接近底部时，重置userScrolled状态
                        setUserScrolled(false);

                        // 调用接近底部回调
                        if (onNearBottom) {
                            onNearBottom();
                        }
                    }
                }

                lastScrollTopRef.current = currentScrollTop;
            });
        };

        // 添加滚动事件监听
        if ('addEventListener' in el) {
            el.addEventListener('scroll', handleScroll, { passive: true });
            return () => {
                el.removeEventListener('scroll', handleScroll);
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                }
            };
        }

        return undefined;
    }, [elementSelector, debug, bottomThreshold, debounce, onScrollDirectionChange, onNearBottom]);

    return { userScrolled, resetScrollState };
};

export default useScrollDetection;