import { useEffect, useRef } from "react";
import { toast } from "sonner";

export const useSocket = (
    url: string,
    setIsWSReady: (ready: boolean) => void,
    setError: (error: boolean) => void,
) => {
    const wsRef = useRef<WebSocket | null>(null);
    const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
    const retryCountRef = useRef(0);
    const isCleaningUpRef = useRef(false);
    const MAX_RETRIES = 3;
    const INITIAL_BACKOFF = 1000; // 1 second

    const getBackoffDelay = (retryCount: number) => {
        return Math.min(INITIAL_BACKOFF * Math.pow(2, retryCount), 10000); // Cap at 10 seconds
    };

    useEffect(() => {
        const connectWs = async () => {
            if (wsRef.current?.readyState === WebSocket.OPEN) {
                wsRef.current.close();
            }

            try {
                let chatModel = localStorage.getItem('chatModel');
                let embeddingModel = localStorage.getItem('embeddingModel');

                const providers = await fetch(
                    `${process.env.NEXT_PUBLIC_API_URL}/models`,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    },
                ).then(async (res) => {
                    if (!res.ok)
                        throw new Error(
                            `获取模型失败: ${res.status} ${res.statusText}`,
                        );
                    return res.json();
                });

                const chatModelProviders = providers.chatModelProviders;
                const embeddingModelProviders = providers.embeddingModelProviders;

                // 检查是否有可用的模型提供者
                if (!chatModelProviders?.openai || !embeddingModelProviders?.openai) {
                    toast.error('没有可用的模型配置');
                    setError(true);
                    return;
                }

                // 如果没有存储的模型或存储的模型不可用，使用第一个可用的模型
                if (!chatModel || !chatModelProviders.openai[chatModel]) {
                    chatModel = Object.keys(chatModelProviders.openai)[0];
                    if (!chatModel) {
                        toast.error('没有可用的聊天模型');
                        setError(true);
                        return;
                    }
                    localStorage.setItem('chatModel', chatModel);
                }

                if (!embeddingModel || !embeddingModelProviders.openai[embeddingModel]) {
                    embeddingModel = Object.keys(embeddingModelProviders.openai)[0];
                    if (!embeddingModel) {
                        toast.error('没有可用的嵌入模型');
                        setError(true);
                        return;
                    }
                    localStorage.setItem('embeddingModel', embeddingModel);
                }

                const wsURL = new URL(url);
                const searchParams = new URLSearchParams({});

                // 获取设备ID
                const deviceId = localStorage.getItem('deviceId') || '';

                // 设置WebSocket连接参数
                searchParams.append('chatModel', chatModel);
                searchParams.append('embeddingModel', embeddingModel);
                searchParams.append('deviceId', deviceId);

                wsURL.search = searchParams.toString();

                const ws = new WebSocket(wsURL.toString());
                wsRef.current = ws;

                const timeoutId = setTimeout(() => {
                    if (ws.readyState !== 1) {
                        toast.error('连接服务器失败，稍后重试。');
                    }
                }, 10000);

                ws.addEventListener('message', (e) => {
                    const data = JSON.parse(e.data);
                    if (data.type === 'signal' && data.data === 'open') {
                        const interval = setInterval(() => {
                            if (ws.readyState === 1) {
                                setIsWSReady(true);
                                setError(false);
                                if (retryCountRef.current > 0) {
                                    toast.success('连接恢复。');
                                }
                                retryCountRef.current = 0;
                                clearInterval(interval);
                            }
                        }, 5);
                        clearTimeout(timeoutId);
                        console.debug(new Date(), 'ws:connected');
                    }
                    // 添加对 ping 消息的处理
                    if (data.type === 'ping') {
                        ws.send(JSON.stringify({ type: 'pong' }));
                        console.debug(new Date(), 'ws:heartbeat');
                        return;
                    }
                    if (data.type === 'error') {
                        toast.error(data.data);
                    }
                });

                ws.onerror = () => {
                    clearTimeout(timeoutId);
                    setIsWSReady(false);
                    toast.error('WebSocket 连接错误。');
                };

                ws.onclose = () => {
                    clearTimeout(timeoutId);
                    setIsWSReady(false);
                    console.debug(new Date(), 'ws:disconnected');
                    if (!isCleaningUpRef.current) {
                        toast.error('连接丢失，尝试重新连接...');
                        attemptReconnect();
                    }
                };
            } catch (error) {
                console.debug(new Date(), 'ws:error', error);
                setIsWSReady(false);
                attemptReconnect();
            }
        };

        const attemptReconnect = () => {
            retryCountRef.current += 1;

            if (retryCountRef.current > MAX_RETRIES) {
                console.debug(new Date(), 'ws:max_retries');
                setError(true);
                toast.error(
                    '多次尝试后无法连接到服务器。请刷新页面重试。',
                );
                return;
            }

            const backoffDelay = getBackoffDelay(retryCountRef.current);
            console.debug(
                new Date(),
                `ws:retry attempt=${retryCountRef.current}/${MAX_RETRIES} delay=${backoffDelay}ms`,
            );

            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
            }

            reconnectTimeoutRef.current = setTimeout(() => {
                connectWs();
            }, backoffDelay);
        };

        connectWs();

        return () => {
            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
            }
            if (wsRef.current?.readyState === WebSocket.OPEN) {
                wsRef.current.close();
                isCleaningUpRef.current = true;
                console.debug(new Date(), 'ws:cleanup');
            }
        };
    }, [url, setIsWSReady, setError]);

    return wsRef.current;
};
