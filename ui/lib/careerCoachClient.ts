/**
 * 职场技能陪练客户端
 * 用于处理与职场技能陪练相关的API请求和SSE连接
 */

import { toast } from 'sonner';

export interface CareerCoachEvaluationResult {
  score: number;
  passed: boolean;
  analysis: {
    strengths: string[];
    weaknesses: string[];
  };
  improvements: string;
  referenceAnswer: string;
  key_points: string[];
  star_rating: number;
  summary: string;
}

export interface CareerCoachEvaluationRequest {
  topic: string;
  knowledge_points: string;
  question: string;
  options: string;
  correct_answer: string;
  user_answer: string;
}

export interface SSECallbacks {
  onPartial?: (partialData: string) => void;
  onFull?: (fullData: CareerCoachEvaluationResult) => void;
  onComplete?: (finalData: CareerCoachEvaluationResult) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
}

export class CareerCoachClient {
  private baseUrl: string;
  private eventSource: EventSource | null = null;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  }

  /**
   * 获取训练主题列表
   * @returns 主题列表
   */
  async getTopics() {
    try {
      const response = await fetch(`${this.baseUrl}/career-coach/topics`);
      if (!response.ok) {
        throw new Error('获取训练主题失败');
      }
      return await response.json();
    } catch (error) {
      console.error('获取训练主题失败:', error);
      toast('获取训练主题失败，请重试');
      throw error;
    }
  }

  /**
   * 获取特定主题的训练题目
   * @param topicId 主题ID
   * @returns 题目列表
   */
  async getQuestions(topicId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/career-coach/topics/${topicId}/questions`);
      if (!response.ok) {
        throw new Error('获取训练题目失败');
      }
      return await response.json();
    } catch (error) {
      console.error('获取训练题目失败:', error);
      toast('获取训练题目失败，请重试');
      throw error;
    }
  }

  // 获取特定主题的知识点
  async getKnowledge(topicId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/career-coach/topics/${topicId}/knowledge`);
      if (!response.ok) {
        throw new Error('获取知识点失败');
      }
      return await response.json();
    } catch (error) {
      console.error('获取知识点失败:', error);
      toast('获取知识点失败，请重试');
      throw error;
    }
  }

  // 获取问题详情
  async getQuestionDetail(questionId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/career-coach/questions/${questionId}`);
      if (!response.ok) {
        throw new Error('获取问题详情失败');
      }
      return await response.json();
    } catch (error) {
      console.error('获取问题详情失败:', error);
      toast('获取问题详情失败，请重试');
      throw error;
    }
  }

  /**
   * 评估用户回答（非流式）
   * @param request 评估请求
   * @returns 评估结果
   */
  async evaluateAnswer(request: CareerCoachEvaluationRequest): Promise<CareerCoachEvaluationResult> {
    try {
      const response = await fetch(`${this.baseUrl}/career-coach/evaluate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('评估回答失败');
      }

      const data = await response.json();
      return data.result;
    } catch (error) {
      console.error('评估回答失败:', error);
      toast('评估回答失败，请重试');
      throw error;
    }
  }

  /**
   * 流式评估用户回答
   * @param request 评估请求
   * @param callbacks 回调函数
   * @returns 清理函数，用于关闭连接
   */
  streamEvaluateAnswer(request: CareerCoachEvaluationRequest, callbacks: SSECallbacks): () => void {
    // 关闭现有连接
    this.closeConnection();

    // 创建EventSource连接
    const url = new URL(`${this.baseUrl}/career-coach/evaluate-stream`);
    
    // 使用fetch API发起请求并处理流式响应
    fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      // 获取响应的reader
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }
      
      // 创建文本解码器
      const decoder = new TextDecoder();
      let buffer = '';
      
      // 处理数据流
      const processStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              callbacks.onClose?.();
              break;
            }
            
            // 解码接收到的数据
            buffer += decoder.decode(value, { stream: true });
            
            // 处理SSE消息
            const lines = buffer.split('\n\n');
            buffer = lines.pop() || '';
            
            for (const line of lines) {
              if (line.trim() === '') continue;
              
              // 解析SSE消息
              const dataMatch = line.match(/^data: (.+)$/m);
              if (dataMatch && dataMatch[1]) {
                try {
                  const data = JSON.parse(dataMatch[1]);
                  
                  // 根据消息类型调用不同的回调
                  if (data.type === 'partial') {
                    callbacks.onPartial?.(data.data);
                  } else if (data.type === 'full') {
                    callbacks.onFull?.(data.data);
                  } else if (data.type === 'complete') {
                    callbacks.onComplete?.(data.data);
                  } else if (data.status === 'error') {
                    callbacks.onError?.(data);
                  } else if (data.status === 'completed') {
                    callbacks.onClose?.();
                  }
                } catch (err) {
                  console.error('解析SSE消息失败:', err);
                }
              }
            }
          }
        } catch (err) {
          console.error('SSE读取错误:', err);
          callbacks.onError?.(err);
        }
      };
      
      // 开始处理流
      processStream();
    })
    .catch(error => {
      console.error('建立SSE连接失败:', error);
      callbacks.onError?.(error);
    });

    // 返回清理函数
    return () => this.closeConnection();
  }

  /**
   * 关闭现有连接
   */
  private closeConnection() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  /**
   * 生成参考答案（非流式）
   * @param request 生成请求
   * @returns 参考答案
   */
  async generateAnswer(request: any): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/career-coach/generate-answer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('生成参考答案失败');
      }

      const data = await response.json();
      return data.result;
    } catch (error) {
      console.error('生成参考答案失败:', error);
      toast('生成参考答案失败，请重试');
      throw error;
    }
  }

  /**
   * 流式生成参考答案
   * @param request 生成请求
   * @param callbacks 回调函数
   * @returns 清理函数
   */
  streamGenerateAnswer(request: any, callbacks: SSECallbacks): () => void {
    // 关闭现有连接
    this.closeConnection();

    // 使用fetch API发起请求并处理流式响应
    fetch(`${this.baseUrl}/career-coach/generate-answer-stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }
      
      const decoder = new TextDecoder();
      let buffer = '';
      
      const processStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              callbacks.onClose?.();
              break;
            }
            
            buffer += decoder.decode(value, { stream: true });
            
            const lines = buffer.split('\n\n');
            buffer = lines.pop() || '';
            
            for (const line of lines) {
              if (line.trim() === '') continue;
              
              const dataMatch = line.match(/^data: (.+)$/m);
              if (dataMatch && dataMatch[1]) {
                try {
                  const data = JSON.parse(dataMatch[1]);
                  
                  if (data.type === 'partial') {
                    callbacks.onPartial?.(data.data);
                  } else if (data.type === 'complete') {
                    callbacks.onComplete?.(data.data);
                  } else if (data.status === 'error') {
                    callbacks.onError?.(data);
                  } else if (data.status === 'completed') {
                    callbacks.onClose?.();
                  }
                } catch (err) {
                  console.error('解析SSE消息失败:', err);
                }
              }
            }
          }
        } catch (err) {
          console.error('SSE读取错误:', err);
          callbacks.onError?.(err);
        }
      };
      
      processStream();
    })
    .catch(error => {
      console.error('建立SSE连接失败:', error);
      callbacks.onError?.(error);
    });

    return () => this.closeConnection();
  }
}

// 导出默认实例，方便直接使用
export default new CareerCoachClient();