import { toast } from 'sonner';
import sseClient from './sseClient';

export class FileUploader {
    private static getUserId(): string {
        let userId = '';
        try {
            userId = JSON.parse(localStorage.getItem('__user_detail__') || '')?.data?.detail?.user_id;
        } catch (error) { }
        return userId || Math.random().toString(36).slice(-8);
    }

    public static async uploadFiles(files: File[]): Promise<Array<string>> {
        if (!files?.length) return [];

        // 校验文件大小
        const validFiles = files?.filter(file => {
            if (file.size > 1024 * 1024 * 5) {
                toast(`${file.name} 大小超过5MB`);
                return false;
            }
            return true;
        });

        if (!validFiles.length) return [];

        const formData = new FormData();
        validFiles.forEach((file, index) => {
            formData.append(`file${index + 1}`, file);
        });
        formData.append('num', `${files.length}`);
        formData.append('userId', this.getUserId());

        try {
            const response = await fetch('https://jmso.cyjiaomu.com/ai/files/attach/batch-upload', {
                method: 'POST',
                body: formData,
            });
            const result = await response.json();
            console.log("🚀 ~ 批量文件上传成功 ~ data:", result.data);
            return result.data?.fileIds || [];
        } catch (error) {
            console.error("🚀 ~ 批量文件上传失败 ~ error:", error);
            toast('文件上传失败');
            return [];
        }
    }

    public static async uploadFile(file: File): Promise<Array<string>> {
        const results = await this.uploadFiles([file]);
        return results;
    }

    public static async getFileContentSSE({
        resumeFileId,
        resumeFileName,
        jobFileId,
        jobFileName,
        resumeText,
        jobText,
        channelId,
        deviceId,
        onMessage
    }: any): Promise<() => void> {
        // 确保在客户端环境
        if (typeof window === 'undefined') {
            return () => { };
        }

        // 创建任务
        try {
            // 使用 sseClient 连接到任务的 SSE 流
            return sseClient.connectToTaskSSE({
                eventSource: `/resume/analyze-sse`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Device-Id': deviceId || ''
                },
                body: {
                    resumeFileId,
                    resumeFileName,
                    jobFileId,
                    jobFileName,
                    resumeText,
                    jobText,
                    channelId,
                    deviceId,
                    userId: this.getUserId()
                },
                options: {
                    onMessage,
                    onError: (error: any) => {
                        console.error("🚀 ~ SSE 连接错误:", error);
                        toast('连接中断，请重试');
                    },
                    onClose: () => {
                        console.log("🚀 ~ SSE 连接已关闭");
                    }
                }
            });
        } catch (error) {
            console.error("🚀 ~ 创建分析任务失败:", error);
            toast('创建分析任务失败，请重试');
            return () => { };
        }
    }
}