// 添加全局类型声明
declare global {
    interface Window {
        webkit?: {
            messageHandlers: {
                [key: string]: {
                    postMessage: (data: any) => void;
                };
            };
        };
        jsBridge?: {
            finish: (flag?: any) => void;
            getToken: () => string;
            getStatusBarHeight: () => number;
            openShare: (data: string) => void;
            showSignInDialog: (data: string) => void;
            shareSignIn: (data: any) => void;
            login: () => void;
            appPay: (data: string) => void;
            startGoodsDetail: (data: string) => void;
            autoJump: (data: string) => void;
            universalJump: (data: string) => void;
            titleBarEnable: (param: string) => void;
            openMainPage: (page: string) => void;
            customRoute: (url: string) => void;
            updateHeight: (height: string) => void;
            getAppBasicParameter: () => any;
        };
        prompt: (message?: string | undefined, _default?: string | undefined) => string | null;
    }
}

const win: any = globalThis || {};

export const FZ_JsBridge = {
    isApp: win.navigator?.userAgent?.includes('foundingAzApp'),
    isMobile: win.navigator?.userAgent?.includes('Mobile'),
    isAndroid() {
        if (!this.isApp) {
            return false
        }
        return win.navigator?.userAgent?.toString()?.toLowerCase()?.includes('android');
    },
    isIOS() {
        if (!this.isApp) {
            return false
        }
        return !this.isAndroid()
    },
    // Mozilla/5.0 (Linux; Android 14; 23013RK75C Build/UKQ1.230804.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.135 Mobile Safari/537.36Android/foundingAzApp/23013RK75C/14/4.6.1/androidChannel=100
    getAndroidChannel(): number {
        if (!this.isApp || !this.isAndroid()) {
            return -1;
        }
        const userAgent = win.navigator?.userAgent?.toString()?.toLowerCase() || '';
        const match = userAgent.match(/androidchannel=(\d+)/);
        return match ? parseInt(match[1], 10) : -1;
    },
    closeWebView() {
        if (this.isIOS() && win.webkit) {
            win.webkit?.messageHandlers?.finish.postMessage(null);
        }
        if (this.isAndroid() && win.jsBridge) {
            win.jsBridge?.finish()
        }
    },
    finishWebView(flag: any) {
        if (this.isIOS() && win.webkit) {
            win.webkit?.messageHandlers?.finish?.postMessage(flag);
        }
        if (this.isAndroid() && win.jsBridge) {
            win.jsBridge?.finish(flag)
        }
    },
    getToken() {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.getToken();
        }
        if (this.isIOS() && win.webkit) {
            return win.prompt('getToken');
        }
    },
    getStatusHeight() {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.getStatusBarHeight();
        }
        if (this.isIOS() && win.webkit) {
            return win.prompt('getStatusBarHeight');
        }
    },
    getStatusBarHeight() {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.getStatusBarHeight();
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.getStatusBarHeight.postMessage(null);
        }
    },

    /**
     * 分享
     * @param data
     * @returns {*}
     */
    openShare(data: any) {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.openShare(JSON.stringify(data));
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.openShare.postMessage(JSON.stringify(data));
        }
    },

    getLoad(data: any) {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.showSignInDialog(JSON.stringify(data));
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.showSignInDialog.postMessage(JSON.stringify(data));
        }
    },

    getSignShare(data: any) {
        if (this.isAndroid() && win.jsBridge) {

            return win.jsBridge?.shareSignIn(data);
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.shareSignIn.postMessage(data);
        }
    },


    login() {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.login();
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.login.postMessage(null);
        }
    },

    appPay(id: string | number, price: number, method: string, goods_spec: string,
        gifts: any, paId: string | number, buy_num: number, sku_id: string | number) {
        const param = {
            id, price, method, goods_spec, gifts, page_activity_id: paId, buy_num, sku_id
        };
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.appPay(JSON.stringify(param));
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.appPay.postMessage(JSON.stringify(param));
        }
    },
    goToGoods(goodsId: string | number, deliveryId?: string | number, isBuy?: boolean) {
        const param = {
            id: goodsId,
            delivery_id: deliveryId,
            is_buy: isBuy,
        };
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.startGoodsDetail(JSON.stringify(param));
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.jsToOcFunctionGoodsDetail.postMessage(JSON.stringify(param));
        }
    },
    goToPath(path: String, params: any) {
        let param = [
            {
                "path": path,
                "parameter": [params]
            }
        ]
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.autoJump(JSON.stringify(param));
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.autoJump.postMessage(JSON.stringify(param));
        }
    },
    goAutoJump(path: String, ios_path: String, params: any) {
        let param =
        {
            "path": path,
            "ios_path": ios_path,
            "parameter": params
        }


        if (this.isAndroid() && win.jsBridge) {

            return win.jsBridge?.universalJump(JSON.stringify(param));
        }
        if (this.isIOS() && win.webkit) {

            return win.webkit?.messageHandlers.universalJump.postMessage(JSON.stringify(param));
        }
    },
    titleBarEnable(isShow: Boolean) {
        let param = "false"
        if (isShow) {
            param = "true"
        }
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.titleBarEnable(param);
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.titleBarEnable.postMessage(param);
        }
    },
    openMainPage(page: String) {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.openMainPage(page.toString());
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.openMainPage.postMessage(page.toString());
        }
    },
    customRoute(androidUrl: String, iosUrl: String) {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.customRoute(androidUrl.toString());
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.customRoute.postMessage(iosUrl.toString());
        }
    },
    updateHeight(height: number) {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.updateHeight((Number(height) * 3).toString());
        }
        if (this.isIOS() && win.webkit) {
            return win.webkit?.messageHandlers.updateHeight.postMessage(height.toString());
        }
    },
    /**
      * 获取埋点信息：device_id 和 root_source
      */
    getAppBasicParameter() {
        if (this.isAndroid() && win.jsBridge) {
            return win.jsBridge?.getAppBasicParameter();
        }
        if (this.isIOS() && win.webkit) {
            return win.prompt('getAppBasicParameter');
        }
    }
}

export function jsBridge() {
    return FZ_JsBridge
}

