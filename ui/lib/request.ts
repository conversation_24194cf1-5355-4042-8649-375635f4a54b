interface RequestConfig {
    url: string;
    method?: string;
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
}

interface Interceptors {
    request?: (config: RequestConfig) => RequestConfig;
    response?: (response: Response) => Response | Promise<Response>;
}

const DEFAULT_TIMEOUT = 10000;
const interceptors: Interceptors = {};

export function setInterceptors(newInterceptors: Interceptors) {
    Object.assign(interceptors, newInterceptors);
}

export async function request({
    url = '',
    method = 'GET',
    headers = {},
    body = null,
    timeout = DEFAULT_TIMEOUT,
}: RequestConfig) {
    try {
        let config: RequestConfig = { url, method, headers, body, timeout };

        // Apply request interceptor
        if (interceptors.request) {
            config = interceptors.request(config);
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...headers,
            },
            body: body instanceof FormData ? body : (body ? JSON.stringify(body) : undefined),
            signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Apply response interceptor
        let processedResponse: any = response;

        if (interceptors.response) {
            processedResponse = await interceptors.response(response);
        }

        if (!processedResponse.ok) {
            throw new Error(`HTTP error! status: ${processedResponse.status}`);
        }

        return processedResponse.json();
    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
}

export async function get({ url = '', headers = {} }: Omit<RequestConfig, 'method'>) {
    return await request({ url, method: 'GET', headers });
}

export async function post({
    url = '',
    body = null,
    headers = {},
}: Omit<RequestConfig, 'method'>) {
    // 如果是FormData则不设置Content-Type
    const isFormData = body instanceof FormData;
    const finalHeaders = isFormData 
        ? headers 
        : { 'Content-Type': 'application/json', ...headers };
    return await request({ url, method: 'POST', headers: finalHeaders, body });
}