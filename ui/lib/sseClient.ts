/**
 * SSE客户端SDK
 * 用于处理服务器发送事件(Server-Sent Events)的连接和消息处理
 */


export interface SSEOptions {
  baseUrl?: string;
  onOpen?: () => void;
  onMessage?: (data: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
}

export interface SSEStatusHandler {
  status: string;
  handler: (data: any) => void;
}

export class SSEClient {
  private controller: AbortController | null = null;
  private baseUrl: string;

  constructor(options: SSEOptions = {}) {
    this.baseUrl = options?.baseUrl || process.env.NEXT_PUBLIC_API_URL || '';
  }

  /**
   * 连接到任务的SSE流
   * @param taskId 任务ID
   * @param options 配置选项
   * @returns 返回清理函数，用于关闭连接
   */
  connectToTaskSSE({ eventSource, options, method = 'GET', headers = {}, body = null }: any): () => void {
    this.controller = new AbortController();
    const signal = this.controller.signal;

    // 设置请求头
    const requestHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      ...headers
    };

    // 发起请求
    fetch(`${this.baseUrl}${eventSource}`, {
      method: method,
      headers: requestHeaders,
      body: body ? JSON.stringify(body) : undefined,
      signal: signal,
      cache: 'no-store'
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      options?.onOpen?.();
      console.log("🚀 ~ SSE 连接已建立");

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // 处理数据流
      const processStream = async () => {
        if (!reader) return;

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });

            // 处理接收到的数据
            const lines = buffer.split('\n\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.trim() === '') continue;

              // 解析数据行
              const dataMatch = line.match(/^data: (.+)$/m);
              if (dataMatch && dataMatch[1]) {
                try {
                  const data = JSON.parse(dataMatch[1]);
                  console.log("🚀 ~ SSE 消息:", data);
                  options?.onMessage?.(data);
                } catch (err) {
                  console.error('解析 SSE 消息失败:', err);
                }
              }
            }
          }
        } catch (err) {
          if (signal.aborted) {
            console.log("🚀 ~ SSE 连接已中止");
          } else {
            console.error('SSE 读取错误:', err);
            options?.onError?.(err);
          }
        }
      };

      processStream();
    }).catch(err => {
      console.error('SSE 连接错误:', err);
      options?.onError?.(err);
    });

    // 返回清理函数
    return () => {
      console.log("🚀 ~ 关闭 SSE 连接");
      this.close();
      options?.onClose?.();
    };
  }

  close(): void {
    if (this.controller) {
      this.controller.abort("用户主动关闭SSE连接");
      this.controller = null;
    }
  }

  /**
   * 连接到任务SSE并处理不同状态
   * @param eventSource 任务ID
   * @param options 配置选项
   * @returns 返回清理函数
   */
  connectWithStatusHandlers({ eventSource, options }: any): () => void {
    console.log("🚀 ~ SSEClient ~ connectWithStatusHandlers ~ eventSource:", eventSource)
    return this.connectToTaskSSE({
      eventSource, options: {
        ...options,
        onMessage: (data: any) => {
          console.log("🚀 ~ SSEClient ~ connectWithStatusHandlers ~ data:", data)
          options?.onMessage?.(data);

          if (['connected'].includes(data.status)) {
            options?.onOpen?.(data);
            return
          }

          if (['error'].includes(data.status)) {
            console.error('SSE 错误:', data.message);
            options?.onError?.(new Error(data.message));
            this.close();
            return
          }

          const status = data?.status;
          // 查找匹配的状态处理器
          const handler = options?.statusHandlers?.find?.((h: any) => h.status === status);
          if (handler) {
            handler.handler(data);
          }
        },
      }
    });
  }
}

// 导出默认实例，方便直接使用
export default new SSEClient();