import clsx, { ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { FZ_JsBridge } from './js-bridge';
import { jmWx } from './wx';

export const win: any = typeof window !== 'undefined' ? window : {};

export const cn = (...classes: ClassValue[]) => twMerge(clsx(...classes));

export const formatTimeDifference = (
  date1: Date | string,
  date2: Date | string,
): string => {
  date1 = new Date(date1);
  date2 = new Date(date2);

  const diffInSeconds = Math.floor(
    Math.abs(date2.getTime() - date1.getTime()) / 1000,
  );

  if (diffInSeconds < 60)
    return `${diffInSeconds} 秒`;
  else if (diffInSeconds < 3600)
    return `${Math.floor(diffInSeconds / 60)} 分钟`;
  else if (diffInSeconds < 86400)
    return `${Math.floor(diffInSeconds / 3600)} 小时`;
  else if (diffInSeconds < 31536000)
    return `${Math.floor(diffInSeconds / 86400)} 天`;
  else
    return `${Math.floor(diffInSeconds / 31536000)} 年`;
};

export function getQuery() {
  if (typeof window === 'undefined' || !window.location.search) {
    return new URLSearchParams();
  }
  return new URLSearchParams(window.location.search);
}

export function withQuery(url: string, customParams: Record<string, string> = {}, excludeParams: string[] = []) {
  try {
    clkLog('页面跳转', { url });
  } catch (error) {
    // Handle error
  }
  const params = getQuery();
  excludeParams.forEach(param => params.delete(param));
  Object.entries(customParams).forEach(([key, value]) => {
    params.set(key, value);
  });
  return url + '?' + params.toString();
}

export function loadImage(src: string) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    // 先设置事件监听器
    img.onload = () => resolve(src);
    img.onerror = (error) => {
      console.error('图片加载失败:', src, error);
      reject(new Error(`Failed to load image: ${src}`));
    };

    // 设置跨域属性
    img.crossOrigin = 'anonymous';
    img.referrerPolicy = 'no-referrer';
    img.decoding = 'async';
    img.loading = 'lazy';

    // 最后设置 src 触发加载
    img.src = src;

    // 添加超时处理
    const timeout = setTimeout(() => {
      img.src = '';
      reject(new Error(`Image load timeout: ${src}`));
    }, 10000); // 10秒超时

    img.onload = () => {
      clearTimeout(timeout);
      resolve(img);
    };

    img.onerror = (error) => {
      clearTimeout(timeout);
      console.error('图片加载失败:', src, error);
      reject(new Error(`Failed to load image: ${src}`));
    };
  });
}

export const isValidUrl = (url: string): boolean => {
  if (typeof window === 'undefined') return false;
  // 初步使用正则表达式验证
  const regex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
  if (!regex.test(url)) {
    return false;
  }

  // 使用URL对象进行验证
  try {
    new URL(url);

    return true;
  } catch (e) {

  }
  return false;
};
export function tdLog(eventId: String, args: any = {}): void {
  if (typeof window === 'undefined') return;
  (window as any)?.TDAPP?.onEvent(eventId, "", { ...args, user: localStorage.getItem('__user_detail__') });
}

export function clkLog(eventId: String, args: any = {}): void {
  tdLog(eventId, { ...args, type: 'clk' });
}

export function expLog(eventId: String, args: any = {}): void {
  tdLog(eventId, { ...args, type: 'exp' });
}

export function otherLog(eventId: String, args: any = {}): void {
  tdLog(eventId, { ...args, type: 'other' });
}
export function getUserText(str: string = '') {
  try {
    return str?.split('<<|>>')[0].trim()
  } catch (error) {

  }
  return str || ''
}

export function getLocalItem(key: string, needParse = false) {
  if (typeof window === 'undefined') return ''
  try {
    if (needParse) {
      return JSON.parse(localStorage.getItem(key) || '')
    }
    return localStorage.getItem(key) || ''
  } catch (error) {
  }
  return ''
}

export function setLocalItem(key: string, value: string) {
  if (typeof window === 'undefined') return;
  try {
    localStorage.setItem(key, value)
  } catch (error) {
  }
}

export function formatPrice(price: number, showFree = false) {
  if (!price) {
    return showFree ? '免费' : "0"
  }
  return (price / 100).toFixed(2).replace(".00", "");
}

/**
 * 异步加载脚本文件
 *
 * @param src 脚本文件的路径
 * @returns 返回一个Promise，在脚本加载完成后解析，如果脚本加载失败则拒绝
 */
export async function loadScript(src: string): Promise<void> {
  if (typeof window === 'undefined') {
    return Promise.reject(new Error('Window object is not available.'));
  }
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
    document.head.appendChild(script);
  });
}

export function arrayCopy<T>(arr: T[], times = 1): T[] {
  if (!Array.isArray(arr)) throw new Error('First argument must be an array');
  if (times < 0) throw new Error('Times must be a non-negative number');
  if (times === 0) return [];
  return Array(times).fill(null).map(() => [...arr]).flat();
}

export const getDisplayRows = (totalQuestions: number) => {
  if (totalQuestions > 20) return 2;
  if (totalQuestions > 10) return 3;
  if (totalQuestions > 5) return 4;
  return 5;
};

export const gotoWebPage = async ({ url = '', channelId = '' }) => {
  try {
    if (FZ_JsBridge.isApp) {
      return FZ_JsBridge.goAutoJump('/common/WebDetailActivity', 'FouningazWkWebVC', { url })
    }

    if (["mp-kl", "mp"].includes(channelId)) {
      const mpUrl = `pages/webview/index?url=${encodeURIComponent(url)}`
      return jmWx.navigateTo({ url: `/${mpUrl}` });
    }

    console.log("🚀 ~ gotoWebPage ~ channelId:", channelId)
    return window.open(url);
  } catch (error) {
  }
}


export function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}