import { loadScript } from './utils';

const WECHAT_SDK_URL = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js';

export class wxBridge {
    constructor() {
    }

    async loadWechatSDK(): Promise<void> {
        try {
            await loadScript(WECHAT_SDK_URL);
        } catch (error) {
            console.error('Failed to load WeChat SDK:', error);
            throw new Error('微信SDK加载失败');
        }
    }

    async navigateTo({ url = "" }): Promise<void> {
        await this.loadWechatSDK();
        (window as any).wx.miniProgram.navigateTo({ url });
    }

    async switchTab({ url = "" }): Promise<void> {
        await this.loadWechatSDK();
        (window as any).wx.miniProgram.switchTab({ url });
    }
}

export const jmWx = new wxBridge();
