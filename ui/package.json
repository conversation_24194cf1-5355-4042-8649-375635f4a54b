{"name": "perplexica-frontend", "version": "1.10.0-rc2", "license": "MIT", "author": "ItzCrazyKns", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format:write": "prettier . --write"}, "dependencies": {"@headlessui/react": "^2.2.0", "@icons-pack/react-simple-icons": "^9.4.0", "@langchain/openai": "^0.0.25", "@radix-ui/react-slot": "^1.1.2", "@tailwindcss/typography": "^0.5.12", "@types/crypto-js": "^4.2.2", "@types/mermaid": "^9.2.0", "better-react-mathjax": "^2.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "framer-motion": "^12.4.7", "hex-sha1": "^1.0.2", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "langchain": "^0.1.30", "lucide-react": "^0.363.0", "marked": "^15.0.7", "marked-highlight": "^2.2.1", "mermaid": "^11.5.0", "next": "^15.3.0-canary.21", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-fast-marquee": "^1.6.5", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "^7.6.2", "react-syntax-highlighter": "^15.6.1", "react-text-to-speech": "^0.14.5", "react-textarea-autosize": "^8.5.3", "sonner": "^1.4.41", "tailwind-merge": "^2.2.2", "weixin-js-sdk": "^1.6.5", "yet-another-react-lightbox": "^3.17.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.12", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.4", "postcss": "^8", "prettier": "^3.2.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}